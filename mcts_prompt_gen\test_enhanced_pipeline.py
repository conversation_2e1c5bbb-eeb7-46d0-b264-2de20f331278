#!/usr/bin/env python3
"""
Test script for the enhanced ML pipeline with ComfyUI parameter extraction.

This script tests the complete pipeline with enhanced prompt extraction to verify
that the new features improve model performance.
"""

import os
import sys
import dill
import pandas as pd
from pathlib import Path
import tempfile

# Add src directory to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)
from utils import image_info

from data_validation import DataValidator
from lightgbm_trainer import LightGBMTrainer, PromptFeatureExtractor
from model_evaluator import ModelEvaluator


def create_enhanced_test_dataset(original_pkl_file: str, output_file: str, sample_size: int = 1000):
    """
    Create a test dataset with enhanced prompt extraction from a subset of the original data.
    
    Args:
        original_pkl_file: Path to the original promptlabels.pkl
        output_file: Path to save the enhanced test dataset
        sample_size: Number of samples to process for testing
    """
    print(f"Creating enhanced test dataset from {original_pkl_file}...")
    
    # Load original data
    with open(original_pkl_file, 'rb') as f:
        original_data = dill.load(f)
    
    print(f"Original dataset size: {len(original_data)}")
    
    # Take a sample for testing
    import random
    random.seed(42)
    sample_data = random.sample(original_data, min(sample_size, len(original_data)))
    
    print(f"Processing {len(sample_data)} samples with enhanced extraction...")
    
    enhanced_data = []
    enhanced_count = 0
    failed_count = 0
    
    for i, (filename, original_prompt, quality) in enumerate(sample_data):
        if i % 100 == 0:
            print(f"  Progress: {i}/{len(sample_data)} ({i/len(sample_data)*100:.1f}%)")
        
        try:
            # Check if file exists
            if not os.path.exists(filename):
                print(f"  Warning: File not found: {filename}")
                enhanced_data.append((filename, original_prompt, quality))
                continue
            
            # Extract enhanced prompt
            from PIL import Image
            with Image.open(filename) as img:
                info = image_info(img, filename, enhanced_prompts=True)
                enhanced_prompt = info.get('prompt', original_prompt)
                
                # Check if enhancement was successful
                if 'workflow_params' in info and info['workflow_params']:
                    enhanced_count += 1
                
                enhanced_data.append((filename, enhanced_prompt, quality))
                
        except Exception as e:
            print(f"  Error processing {filename}: {e}")
            enhanced_data.append((filename, original_prompt, quality))
            failed_count += 1
    
    # Save enhanced dataset
    with open(output_file, 'wb') as f:
        dill.dump(enhanced_data, f)
    
    print(f"Enhanced dataset saved to: {output_file}")
    print(f"Enhanced extractions: {enhanced_count}/{len(enhanced_data)} ({enhanced_count/len(enhanced_data)*100:.1f}%)")
    print(f"Failed extractions: {failed_count}")
    
    return enhanced_data


def test_feature_extraction_comparison():
    """Test the difference between basic and enhanced feature extraction."""
    print("\n" + "="*60)
    print("TESTING FEATURE EXTRACTION COMPARISON")
    print("="*60)
    
    # Sample prompts for testing
    test_prompts = [
        "1girl, masterpiece, best quality",
        "1girl, masterpiece, best quality --cfg 7.5 --steps 35 --size 832x1216 --sampler euler_ancestral --lora character_lora.safetensors:1.0",
        "beautiful landscape, detailed --cfg 5.0 --steps 20 --size 1024x1024",
        "portrait, high quality --steps 40 --lora style_lora.safetensors:0.8 --lora character_lora.safetensors:1.2"
    ]
    
    labels = ['good', 'good', 'normal', 'good']
    
    # Test feature extraction
    extractor = PromptFeatureExtractor(max_features=1000, ngram_range=(1, 2))
    extractor.fit(test_prompts, labels)
    
    features, encoded_labels = extractor.transform(test_prompts, labels)
    feature_names = extractor.get_feature_names()
    
    print(f"Feature matrix shape: {features.shape}")
    print(f"Number of feature names: {len(feature_names)}")
    
    # Show some enhanced features
    print("\nEnhanced feature names (last 20):")
    for name in feature_names[-20:]:
        print(f"  {name}")
    
    # Show feature values for enhanced prompts
    print("\nFeature values for sample prompts:")
    features_dense = features.toarray()
    
    for i, prompt in enumerate(test_prompts):
        print(f"\nPrompt {i+1}: {prompt[:50]}...")
        
        # Show enhanced parameter features (last few features)
        enhanced_features = features_dense[i, -13:]  # Last 13 features are enhanced
        enhanced_names = feature_names[-13:]
        
        for name, value in zip(enhanced_names, enhanced_features):
            if value > 0:
                print(f"  {name}: {value}")


def run_mini_pipeline_test(enhanced_pkl_file: str):
    """Run a mini version of the ML pipeline to test enhanced features."""
    print("\n" + "="*60)
    print("RUNNING MINI PIPELINE TEST")
    print("="*60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_results_dir = Path(temp_dir) / "results"
        temp_results_dir.mkdir()
        
        # Phase 1: Data validation
        print("Phase 1: Data validation...")
        validator = DataValidator(enhanced_pkl_file, str(temp_results_dir))
        success = validator.run_full_validation()
        
        if not success:
            print("❌ Data validation failed!")
            return False
        
        # Find train/test files
        train_file = validator.run_dir / "train_data.pkl"
        test_file = validator.run_dir / "test_data.pkl"
        
        # Phase 2: Quick training (no hyperparameter optimization)
        print("Phase 2: Quick model training...")
        trainer = LightGBMTrainer(str(temp_results_dir))
        
        try:
            model, feature_extractor = trainer.run_training_pipeline(
                str(train_file), str(test_file),
                optimize_hyperparams=False,  # Skip optimization for speed
                n_trials=5
            )
            
            print("✓ Training completed successfully!")
            
            # Phase 3: Quick evaluation
            print("Phase 3: Model evaluation...")
            evaluator = ModelEvaluator(str(temp_results_dir))
            metrics = evaluator.run_evaluation_pipeline(str(trainer.run_dir), str(test_file))
            
            print("✓ Evaluation completed successfully!")
            
            # Show key metrics
            print(f"\nKey Performance Metrics:")
            print(f"  ROC-AUC: {metrics.get('roc_auc', 'N/A'):.4f}")
            print(f"  Average Precision: {metrics.get('avg_precision', 'N/A'):.4f}")
            print(f"  F1-Score: {metrics.get('f1_score', 'N/A'):.4f}")
            if 'mean_score_diff' in metrics:
                print(f"  Mean Score Difference: {metrics['mean_score_diff']:.4f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Pipeline failed: {e}")
            return False


def main():
    """Main test function."""
    print("Enhanced Pipeline Test Suite")
    print("="*60)
    
    # Check if original data exists
    original_pkl = "promptlabels.pkl"
    if not os.path.exists(original_pkl):
        print(f"❌ Original dataset not found: {original_pkl}")
        print("Please run the data cleaning script first:")
        print("  python run_data_cleaning.py --run")
        return False
    
    # Create enhanced test dataset
    enhanced_pkl = "enhanced_test_dataset.pkl"
    
    if not os.path.exists(enhanced_pkl):
        print("Creating enhanced test dataset...")
        create_enhanced_test_dataset(original_pkl, enhanced_pkl, sample_size=2000)
    else:
        print(f"Using existing enhanced test dataset: {enhanced_pkl}")
    
    # Test feature extraction
    test_feature_extraction_comparison()
    
    # Run mini pipeline test
    success = run_mini_pipeline_test(enhanced_pkl)
    
    if success:
        print("\n🎉 Enhanced pipeline test completed successfully!")
        print("The enhanced prompt extraction is working and ready for full deployment.")
        return True
    else:
        print("\n❌ Enhanced pipeline test failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

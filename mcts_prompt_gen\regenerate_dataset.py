#!/usr/bin/env python3
"""
Parallel Dataset Regeneration Script with Improved LoRA Extraction

This script regenerates the prompt dataset using the new graph-based LoRA extraction
logic with parallel processing and comprehensive error handling.

Features:
- Parallel processing using ThreadPoolExecutor (following build.py patterns)
- Thread-safe error logging to separate log files per process
- Comprehensive error handling with detailed failure reasons
- Progress tracking and statistics
- New prompt format: list of (tag, weight) tuples

Usage:
    python regenerate_dataset.py [--input-file promptlabels.pkl] [--output-file promptlabels_v2.pkl] [--workers 8]
"""

import argparse
import os
import sys
import dill
import json
import logging
import threading
import traceback
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict, Counter

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import image_info, parse_prompt_with_weights
from PIL import Image


class ThreadSafeLogger:
    """Thread-safe logger for parallel processing."""

    def __init__(self, log_file):
        self.log_file = log_file
        self.lock = threading.Lock()

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def info(self, message):
        with self.lock:
            self.logger.info(message)

    def error(self, message):
        with self.lock:
            self.logger.error(message)

    def warning(self, message):
        with self.lock:
            self.logger.warning(message)


def process_single_image(args):
    """
    Process a single image with improved LoRA extraction.

    Args:
        args: Tuple of (filename, old_prompt, goodness, thread_id)

    Returns:
        dict: Processing result with success status and details
    """
    filename, old_prompt, goodness, thread_id = args

    result = {
        'filename': filename,
        'old_prompt': old_prompt,
        'goodness': goodness,
        'new_prompt': None,
        'success': False,
        'error': None,
        'loras_found': 0,
        'tags_parsed': 0
    }

    try:
        # Check if file still exists
        if not os.path.exists(filename):
            result['error'] = f"File not found: {filename}"
            return result

        # Re-extract prompt with improved logic
        with Image.open(filename) as img:
            info = image_info(img, filename, enhanced_prompts=True)
            enhanced_prompt = info.get('prompt', '')

        if enhanced_prompt:
            # Parse prompt into (tag, weight) tuples
            tags_with_weights = parse_prompt_with_weights(enhanced_prompt)

            # Count LoRAs
            loras_found = sum(1 for tag, weight in tags_with_weights if tag.endswith('.safetensors'))

            result['new_prompt'] = tags_with_weights
            result['loras_found'] = loras_found
            result['tags_parsed'] = len(tags_with_weights)
            result['success'] = True
        else:
            # Keep old prompt if extraction fails
            if isinstance(old_prompt, str):
                # Convert old string prompt to tag format
                result['new_prompt'] = parse_prompt_with_weights(old_prompt)
            else:
                result['new_prompt'] = old_prompt
            result['error'] = "No enhanced prompt extracted"
            result['success'] = True  # Still successful, just using old data

    except Exception as e:
        result['error'] = str(e)
        result['traceback'] = traceback.format_exc()
        # Keep old data on error
        if isinstance(old_prompt, str):
            result['new_prompt'] = parse_prompt_with_weights(old_prompt)
        else:
            result['new_prompt'] = old_prompt
        result['success'] = True  # Don't fail completely, use old data

    return result


def regenerate_prompt_data_parallel(input_file, output_file, max_workers=8, verbose=False):
    """
    Regenerate prompt data using improved LoRA extraction with parallel processing.

    Args:
        input_file: Path to existing promptlabels.pkl file
        output_file: Path to save regenerated data
        max_workers: Number of parallel workers
        verbose: Whether to print detailed progress

    Returns:
        tuple: (success_count, error_count, total_count)
    """
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f'regeneration_{timestamp}.log'
    logger = ThreadSafeLogger(log_file)

    logger.info(f"Loading existing dataset from: {input_file}")

    # Load existing data
    try:
        with open(input_file, 'rb') as f:
            existing_data = dill.load(f)
    except Exception as e:
        logger.error(f"Error loading input file: {e}")
        return 0, 0, 0

    logger.info(f"Loaded {len(existing_data)} entries from existing dataset")

    # Prepare tasks for parallel processing
    tasks = []
    for i, (filename, old_prompt, goodness) in enumerate(existing_data):
        tasks.append((filename, old_prompt, goodness, i % max_workers))

    logger.info(f"Starting parallel processing with {max_workers} workers...")

    # Process in parallel
    results = []
    success_count = 0
    error_count = 0

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_task = {executor.submit(process_single_image, task): task for task in tasks}

        # Process completed tasks
        for i, future in enumerate(as_completed(future_to_task), 1):
            task = future_to_task[future]

            try:
                result = future.result()
                results.append(result)

                if result['success']:
                    success_count += 1
                    if verbose and result['error']:
                        logger.warning(f"Warning for {result['filename']}: {result['error']}")
                else:
                    error_count += 1
                    logger.error(f"Failed to process {result['filename']}: {result['error']}")

                # Progress logging
                if i % 1000 == 0:
                    logger.info(f"Processed {i}/{len(tasks)} images ({i/len(tasks)*100:.1f}%)")

            except Exception as e:
                error_count += 1
                logger.error(f"Exception processing task {task}: {e}")

    # Prepare final dataset in new format: (filename, prompt_tags, goodness)
    regenerated_data = []
    lora_stats = Counter()
    tag_stats = Counter()

    for result in results:
        filename = result['filename']
        new_prompt = result['new_prompt']
        goodness = result['goodness']

        # Convert to new format if needed
        if isinstance(new_prompt, str):
            new_prompt = parse_prompt_with_weights(new_prompt)

        regenerated_data.append((filename, new_prompt, goodness))

        # Collect statistics
        for tag, weight in new_prompt:
            if tag.endswith('.safetensors'):
                lora_stats[tag] += 1
            tag_stats[tag] += 1

    # Save regenerated data
    logger.info(f"Saving regenerated dataset to: {output_file}")
    try:
        with open(output_file, 'wb') as f:
            dill.dump(regenerated_data, f)
        logger.info(f"Successfully saved {len(regenerated_data)} entries")
    except Exception as e:
        logger.error(f"Error saving output file: {e}")
        return success_count, error_count, len(tasks)

    # Generate statistics
    logger.info("\n" + "=" * 60)
    logger.info("REGENERATION STATISTICS")
    logger.info("=" * 60)
    logger.info(f"Total entries processed: {len(results)}")
    logger.info(f"Successful regenerations: {success_count}")
    logger.info(f"Errors/warnings: {error_count}")
    logger.info(f"Success rate: {success_count/len(results)*100:.2f}%")

    # LoRA statistics
    total_loras = sum(lora_stats.values())
    logger.info(f"\nLoRA Statistics:")
    logger.info(f"  Total LoRA instances: {total_loras}")
    logger.info(f"  Unique LoRA models: {len(lora_stats)}")
    logger.info(f"  Top 10 LoRAs:")
    for lora, count in lora_stats.most_common(10):
        logger.info(f"    {lora}: {count}")

    # Tag statistics
    logger.info(f"\nTag Statistics:")
    logger.info(f"  Total tag instances: {sum(tag_stats.values())}")
    logger.info(f"  Unique tags: {len(tag_stats)}")

    return success_count, error_count, len(tasks)


def analyze_changes(old_file, new_file):
    """
    Analyze the differences between old and new datasets.
    
    Args:
        old_file: Path to original dataset
        new_file: Path to regenerated dataset
    """
    print("\nAnalyzing changes between datasets...")
    
    try:
        with open(old_file, 'rb') as f:
            old_data = dill.load(f)
        with open(new_file, 'rb') as f:
            new_data = dill.load(f)
    except Exception as e:
        print(f"Error loading files for analysis: {e}")
        return
    
    # Create lookup dictionaries
    old_dict = {filename: prompt for filename, prompt, _ in old_data}
    new_dict = {filename: prompt for filename, prompt, _ in new_data}
    
    # Count changes
    lora_changes = 0
    lora_reductions = 0
    lora_increases = 0
    
    for filename in old_dict:
        if filename in new_dict:
            old_prompt = old_dict[filename]
            new_prompt = new_dict[filename]
            
            old_loras = len([part for part in old_prompt.split() if part.startswith('--lora')])
            new_loras = len([part for part in new_prompt.split() if part.startswith('--lora')])
            
            if old_loras != new_loras:
                lora_changes += 1
                if new_loras < old_loras:
                    lora_reductions += 1
                else:
                    lora_increases += 1
    
    print(f"LoRA extraction changes:")
    print(f"  Total files with LoRA changes: {lora_changes}")
    print(f"  Files with fewer LoRAs (disconnected removed): {lora_reductions}")
    print(f"  Files with more LoRAs (new connections found): {lora_increases}")
    print(f"  Percentage of files affected: {lora_changes/len(old_dict)*100:.2f}%")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Regenerate dataset with improved LoRA extraction")
    parser.add_argument('--input-file', default='promptlabels.pkl', 
                       help='Input dataset file (default: promptlabels.pkl)')
    parser.add_argument('--output-file', default='promptlabels_v2.pkl',
                       help='Output dataset file (default: promptlabels_v2.pkl)')
    parser.add_argument('--workers', type=int, default=8,
                       help='Number of parallel workers (default: 8)')
    parser.add_argument('--verbose', action='store_true',
                       help='Print detailed progress information')
    parser.add_argument('--analyze', action='store_true',
                       help='Analyze changes between old and new datasets')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found")
        print("Please run the data cleaning script first to generate the dataset")
        return 1
    
    # Create backup of original file
    if args.input_file != args.output_file:
        backup_file = f"{args.input_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"Creating backup: {backup_file}")
        try:
            import shutil
            shutil.copy2(args.input_file, backup_file)
        except Exception as e:
            print(f"Warning: Could not create backup: {e}")
    
    print("=" * 60)
    print("Dataset Regeneration with Improved LoRA Extraction")
    print("=" * 60)
    print(f"Input file: {args.input_file}")
    print(f"Output file: {args.output_file}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Regenerate dataset using parallel processing
    success_count, error_count, total_count = regenerate_prompt_data_parallel(
        args.input_file, args.output_file, args.workers, args.verbose
    )
    
    # Print summary
    print("\n" + "=" * 60)
    print("REGENERATION SUMMARY")
    print("=" * 60)
    print(f"Total entries processed: {total_count}")
    print(f"Successfully regenerated: {success_count}")
    print(f"Errors/kept original: {error_count}")
    print(f"Success rate: {success_count/total_count*100:.2f}%")
    
    # Analyze changes if requested
    if args.analyze and args.input_file != args.output_file:
        analyze_changes(args.input_file, args.output_file)
    
    print(f"\nRegenerated dataset saved to: {args.output_file}")
    print("You can now use this improved dataset for ML training.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

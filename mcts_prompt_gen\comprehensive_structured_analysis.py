#!/usr/bin/env python3
"""
Comprehensive Analysis Script for promptlabels_structured.pkl

This script performs both data exploration and validation of the structured prompt dataset.
It analyzes the data format, generates statistics, validates parsing accuracy, and identifies
any issues that need to be addressed.

Requirements:
- Load and analyze promptlabels_structured.pkl structure
- Generate comprehensive statistics and visualizations
- Validate >98% parsing accuracy requirement
- Perform data integrity checks
- Identify and trace any parsing issues
- Generate detailed reports with recommendations

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import dill
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from collections import Counter, defaultdict
from typing import List, Tuple, Dict, Any, Optional, Union
from datetime import datetime
import logging
import traceback
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'structured_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add src directory to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

try:
    from enhanced_prompt_processor import EnhancedPromptProcessor
    from validate_prompt_parsing_system import PromptParsingValidator
except ImportError as e:
    logger.warning(f"Could not import some modules: {e}")


class StructuredDataAnalyzer:
    """Comprehensive analyzer for structured prompt data."""
    
    def __init__(self, pkl_file: str = "promptlabels_structured.pkl"):
        """
        Initialize the analyzer.
        
        Args:
            pkl_file: Path to the structured pickle file
        """
        self.pkl_file = pkl_file
        self.data = None
        self.analysis_results = {}
        self.validation_results = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create output directory
        self.output_dir = f"analysis_results_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        logger.info(f"Initialized analyzer for {pkl_file}")
        logger.info(f"Output directory: {self.output_dir}")
    
    def load_data(self) -> bool:
        """
        Load the structured data from pickle file.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not os.path.exists(self.pkl_file):
                logger.error(f"File not found: {self.pkl_file}")
                return False
            
            logger.info(f"Loading data from {self.pkl_file}...")
            with open(self.pkl_file, 'rb') as f:
                self.data = dill.load(f)
            
            logger.info(f"Successfully loaded {len(self.data)} entries")
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def analyze_data_structure(self) -> Dict[str, Any]:
        """
        Analyze the basic structure and format of the data.
        
        Returns:
            Dictionary containing structure analysis results
        """
        logger.info("Analyzing data structure...")
        
        if not self.data:
            return {"error": "No data loaded"}
        
        structure_analysis = {
            "total_entries": len(self.data),
            "data_type": str(type(self.data)),
            "sample_entry_types": [],
            "format_validation": {},
            "structure_issues": []
        }
        
        # Analyze first few entries to understand structure
        sample_size = min(10, len(self.data))
        for i in range(sample_size):
            entry = self.data[i]
            entry_info = {
                "index": i,
                "type": str(type(entry)),
                "length": len(entry) if hasattr(entry, '__len__') else "N/A"
            }
            
            # Analyze entry components
            if isinstance(entry, (tuple, list)) and len(entry) >= 3:
                filename, tags, goodness = entry[0], entry[1], entry[2]
                entry_info.update({
                    "filename_type": str(type(filename)),
                    "tags_type": str(type(tags)),
                    "goodness_type": str(type(goodness)),
                    "tags_count": len(tags) if hasattr(tags, '__len__') else "N/A"
                })
                
                # Check if tags are in correct format
                if isinstance(tags, list) and len(tags) > 0:
                    first_tag = tags[0]
                    entry_info["first_tag_type"] = str(type(first_tag))
                    if isinstance(first_tag, tuple) and len(first_tag) == 2:
                        entry_info["tag_format"] = "correct (tag, weight) tuple"
                    else:
                        entry_info["tag_format"] = "incorrect format"
                        structure_analysis["structure_issues"].append(f"Entry {i}: Tags not in (tag, weight) format")
            else:
                structure_analysis["structure_issues"].append(f"Entry {i}: Not in expected (filename, tags, goodness) format")
            
            structure_analysis["sample_entry_types"].append(entry_info)
        
        # Validate expected format across all entries
        correct_format_count = 0
        for i, entry in enumerate(self.data):
            if isinstance(entry, (tuple, list)) and len(entry) == 3:
                filename, tags, goodness = entry
                if (isinstance(filename, str) and 
                    isinstance(tags, list) and 
                    isinstance(goodness, (int, float))):
                    correct_format_count += 1
        
        structure_analysis["format_validation"] = {
            "correct_format_entries": correct_format_count,
            "correct_format_percentage": (correct_format_count / len(self.data)) * 100,
            "format_issues": len(self.data) - correct_format_count
        }
        
        self.analysis_results["structure"] = structure_analysis
        logger.info(f"Structure analysis complete. {correct_format_count}/{len(self.data)} entries in correct format")
        
        return structure_analysis

    def generate_comprehensive_statistics(self) -> Dict[str, Any]:
        """
        Generate comprehensive statistics about the dataset.

        Returns:
            Dictionary containing detailed statistics
        """
        logger.info("Generating comprehensive statistics...")

        if not self.data:
            return {"error": "No data loaded"}

        stats = {
            "dataset_overview": {},
            "tag_statistics": {},
            "weight_statistics": {},
            "goodness_statistics": {},
            "lora_analysis": {},
            "technical_params": {}
        }

        # Dataset overview
        total_entries = len(self.data)
        valid_entries = []
        tag_counts = []
        all_tags = []
        all_weights = []
        goodness_scores = []
        lora_models = []
        technical_params = defaultdict(list)

        for i, entry in enumerate(self.data):
            try:
                if len(entry) >= 3:
                    filename, tags, goodness = entry[0], entry[1], entry[2]
                    valid_entries.append(entry)
                    goodness_scores.append(goodness)

                    if isinstance(tags, list):
                        tag_counts.append(len(tags))

                        for tag_item in tags:
                            if isinstance(tag_item, tuple) and len(tag_item) == 2:
                                tag, weight = tag_item
                                all_tags.append(tag)
                                all_weights.append(weight)

                                # Identify LoRA models
                                if isinstance(tag, str) and 'lora' in tag.lower():
                                    lora_models.append((tag, weight))

                                # Identify technical parameters
                                if isinstance(tag, str):
                                    if any(param in tag.lower() for param in ['cfg', 'steps', 'width', 'height', 'sampler']):
                                        technical_params[tag].append(weight)
            except Exception as e:
                logger.warning(f"Error processing entry {i}: {e}")

        # Dataset overview statistics
        stats["dataset_overview"] = {
            "total_entries": total_entries,
            "valid_entries": len(valid_entries),
            "validity_percentage": (len(valid_entries) / total_entries) * 100 if total_entries > 0 else 0,
            "invalid_entries": total_entries - len(valid_entries)
        }

        # Tag count statistics
        if tag_counts:
            stats["tag_statistics"] = {
                "total_tags": sum(tag_counts),
                "unique_tags": len(set(all_tags)),
                "avg_tags_per_prompt": np.mean(tag_counts),
                "median_tags_per_prompt": np.median(tag_counts),
                "min_tags_per_prompt": min(tag_counts),
                "max_tags_per_prompt": max(tag_counts),
                "std_tags_per_prompt": np.std(tag_counts)
            }

            # Top 50 most frequent tags
            tag_counter = Counter(all_tags)
            stats["tag_statistics"]["top_50_tags"] = tag_counter.most_common(50)

            # Tag length distribution
            tag_lengths = [len(str(tag)) for tag in all_tags]
            stats["tag_statistics"]["tag_length_stats"] = {
                "avg_length": np.mean(tag_lengths),
                "median_length": np.median(tag_lengths),
                "min_length": min(tag_lengths),
                "max_length": max(tag_lengths),
                "std_length": np.std(tag_lengths)
            }

            # Find long tags (>100 characters)
            long_tags = [(tag, len(str(tag))) for tag in set(all_tags) if len(str(tag)) > 100]
            stats["tag_statistics"]["long_tags"] = long_tags

        # Weight statistics
        if all_weights:
            stats["weight_statistics"] = {
                "total_weights": len(all_weights),
                "avg_weight": np.mean(all_weights),
                "median_weight": np.median(all_weights),
                "min_weight": min(all_weights),
                "max_weight": max(all_weights),
                "std_weight": np.std(all_weights),
                "weight_range_validation": {
                    "within_normal_range": sum(1 for w in all_weights if -2.0 <= w <= 2.0),
                    "outside_normal_range": sum(1 for w in all_weights if w < -2.0 or w > 2.0),
                    "percentage_valid": (sum(1 for w in all_weights if -2.0 <= w <= 2.0) / len(all_weights)) * 100
                }
            }

        # Goodness score statistics
        if goodness_scores:
            stats["goodness_statistics"] = {
                "total_scores": len(goodness_scores),
                "avg_goodness": np.mean(goodness_scores),
                "median_goodness": np.median(goodness_scores),
                "min_goodness": min(goodness_scores),
                "max_goodness": max(goodness_scores),
                "std_goodness": np.std(goodness_scores),
                "score_distribution": dict(Counter(goodness_scores))
            }

        # LoRA analysis
        if lora_models:
            lora_counter = Counter([lora[0] for lora in lora_models])
            lora_weights = [lora[1] for lora in lora_models]

            stats["lora_analysis"] = {
                "total_lora_usage": len(lora_models),
                "unique_lora_models": len(lora_counter),
                "most_used_loras": lora_counter.most_common(20),
                "lora_weight_stats": {
                    "avg_weight": np.mean(lora_weights),
                    "median_weight": np.median(lora_weights),
                    "min_weight": min(lora_weights),
                    "max_weight": max(lora_weights),
                    "std_weight": np.std(lora_weights)
                }
            }

        # Technical parameters analysis
        if technical_params:
            stats["technical_params"] = {}
            for param, values in technical_params.items():
                stats["technical_params"][param] = {
                    "count": len(values),
                    "avg_value": np.mean(values),
                    "median_value": np.median(values),
                    "min_value": min(values),
                    "max_value": max(values),
                    "std_value": np.std(values)
                }

        self.analysis_results["statistics"] = stats
        logger.info("Statistics generation completed")

        return stats

    def validate_data_quality(self) -> Dict[str, Any]:
        """
        Perform comprehensive data quality validation.

        Returns:
            Dictionary containing validation results
        """
        logger.info("Performing data quality validation...")

        if not self.data:
            return {"error": "No data loaded"}

        validation = {
            "format_validation": {},
            "tag_validation": {},
            "weight_validation": {},
            "integrity_checks": {},
            "parsing_accuracy": {},
            "error_analysis": []
        }

        total_entries = len(self.data)
        format_errors = 0
        tag_errors = 0
        weight_errors = 0
        parsing_errors = 0

        malformed_tags = []
        invalid_weights = []
        missing_files = []

        for i, entry in enumerate(self.data):
            try:
                # Format validation
                if not isinstance(entry, (tuple, list)) or len(entry) != 3:
                    format_errors += 1
                    validation["error_analysis"].append(f"Entry {i}: Invalid format - expected (filename, tags, goodness)")
                    continue

                filename, tags, goodness = entry

                # Filename validation
                if not isinstance(filename, str) or not filename:
                    format_errors += 1
                    validation["error_analysis"].append(f"Entry {i}: Invalid filename")

                # Check if file exists (sample check for first 100 entries)
                if i < 100 and isinstance(filename, str):
                    if not os.path.exists(filename):
                        missing_files.append(filename)

                # Tags validation
                if not isinstance(tags, list):
                    tag_errors += 1
                    validation["error_analysis"].append(f"Entry {i}: Tags not a list")
                    continue

                for j, tag_item in enumerate(tags):
                    if not isinstance(tag_item, tuple) or len(tag_item) != 2:
                        tag_errors += 1
                        validation["error_analysis"].append(f"Entry {i}, tag {j}: Not a (tag, weight) tuple")
                        continue

                    tag, weight = tag_item

                    # Tag validation
                    if not isinstance(tag, str):
                        malformed_tags.append((i, j, tag, "Not a string"))
                    elif not tag or tag.isspace():
                        malformed_tags.append((i, j, tag, "Empty or whitespace"))
                    elif len(tag) > 100:
                        malformed_tags.append((i, j, tag, f"Too long ({len(tag)} chars)"))

                    # Weight validation
                    if not isinstance(weight, (int, float)):
                        invalid_weights.append((i, j, weight, "Not numeric"))
                    elif weight < -2.0 or weight > 2.0:
                        invalid_weights.append((i, j, weight, "Outside normal range"))
                    elif np.isnan(weight) or np.isinf(weight):
                        invalid_weights.append((i, j, weight, "NaN or Inf"))

                # Goodness validation
                if not isinstance(goodness, (int, float)):
                    format_errors += 1
                    validation["error_analysis"].append(f"Entry {i}: Invalid goodness score type")

            except Exception as e:
                parsing_errors += 1
                validation["error_analysis"].append(f"Entry {i}: Parsing error - {str(e)}")

        # Calculate validation metrics
        validation["format_validation"] = {
            "total_entries": total_entries,
            "format_errors": format_errors,
            "format_accuracy": ((total_entries - format_errors) / total_entries) * 100 if total_entries > 0 else 0
        }

        validation["tag_validation"] = {
            "tag_errors": tag_errors,
            "malformed_tags": len(malformed_tags),
            "malformed_tag_examples": malformed_tags[:10],  # First 10 examples
            "tag_accuracy": ((total_entries - tag_errors) / total_entries) * 100 if total_entries > 0 else 0
        }

        validation["weight_validation"] = {
            "invalid_weights": len(invalid_weights),
            "invalid_weight_examples": invalid_weights[:10],  # First 10 examples
            "weight_accuracy": ((sum(len(entry[1]) for entry in self.data if len(entry) >= 2 and isinstance(entry[1], list)) - len(invalid_weights)) /
                               sum(len(entry[1]) for entry in self.data if len(entry) >= 2 and isinstance(entry[1], list))) * 100 if self.data else 0
        }

        validation["integrity_checks"] = {
            "missing_files_sample": missing_files,
            "missing_files_count": len(missing_files),
            "parsing_errors": parsing_errors
        }

        # Overall parsing accuracy
        total_issues = format_errors + tag_errors + parsing_errors
        overall_accuracy = ((total_entries - total_issues) / total_entries) * 100 if total_entries > 0 else 0

        validation["parsing_accuracy"] = {
            "overall_accuracy": overall_accuracy,
            "meets_98_percent_requirement": overall_accuracy >= 98.0,
            "total_issues": total_issues,
            "accuracy_breakdown": {
                "format_accuracy": validation["format_validation"]["format_accuracy"],
                "tag_accuracy": validation["tag_validation"]["tag_accuracy"],
                "weight_accuracy": validation["weight_validation"]["weight_accuracy"]
            }
        }

        self.validation_results = validation
        logger.info(f"Validation completed. Overall accuracy: {overall_accuracy:.2f}%")

        return validation

    def create_visualizations(self) -> bool:
        """
        Create comprehensive visualizations of the data analysis.

        Returns:
            True if successful, False otherwise
        """
        logger.info("Creating visualizations...")

        try:
            if not self.analysis_results.get("statistics"):
                logger.warning("No statistics available for visualization")
                return False

            stats = self.analysis_results["statistics"]

            # Set up the plotting style
            plt.style.use('default')
            sns.set_palette("husl")

            # Create a comprehensive figure with multiple subplots
            fig = plt.figure(figsize=(20, 16))

            # 1. Tag count distribution
            if "tag_statistics" in stats and stats["tag_statistics"]:
                plt.subplot(3, 3, 1)
                tag_counts = []
                for entry in self.data:
                    if len(entry) >= 2 and isinstance(entry[1], list):
                        tag_counts.append(len(entry[1]))

                if tag_counts:
                    plt.hist(tag_counts, bins=50, alpha=0.7, edgecolor='black')
                    plt.title('Tag Count Distribution')
                    plt.xlabel('Number of Tags per Prompt')
                    plt.ylabel('Frequency')
                    plt.axvline(np.mean(tag_counts), color='red', linestyle='--', label=f'Mean: {np.mean(tag_counts):.1f}')
                    plt.legend()

            # 2. Top 20 most frequent tags
            if "tag_statistics" in stats and "top_50_tags" in stats["tag_statistics"]:
                plt.subplot(3, 3, 2)
                top_tags = stats["tag_statistics"]["top_50_tags"][:20]
                tags, counts = zip(*top_tags) if top_tags else ([], [])

                if tags:
                    plt.barh(range(len(tags)), counts)
                    plt.yticks(range(len(tags)), [tag[:20] + '...' if len(tag) > 20 else tag for tag in tags])
                    plt.title('Top 20 Most Frequent Tags')
                    plt.xlabel('Frequency')
                    plt.gca().invert_yaxis()

            # 3. Weight distribution
            if "weight_statistics" in stats and stats["weight_statistics"]:
                plt.subplot(3, 3, 3)
                all_weights = []
                for entry in self.data:
                    if len(entry) >= 2 and isinstance(entry[1], list):
                        for tag_item in entry[1]:
                            if isinstance(tag_item, tuple) and len(tag_item) == 2:
                                all_weights.append(tag_item[1])

                if all_weights:
                    plt.hist(all_weights, bins=50, alpha=0.7, edgecolor='black')
                    plt.title('Weight Distribution')
                    plt.xlabel('Weight Value')
                    plt.ylabel('Frequency')
                    plt.axvline(np.mean(all_weights), color='red', linestyle='--', label=f'Mean: {np.mean(all_weights):.2f}')
                    plt.legend()

            # 4. Goodness score distribution
            if "goodness_statistics" in stats and stats["goodness_statistics"]:
                plt.subplot(3, 3, 4)
                goodness_scores = [entry[2] for entry in self.data if len(entry) >= 3]

                if goodness_scores:
                    unique_scores = list(set(goodness_scores))
                    counts = [goodness_scores.count(score) for score in unique_scores]
                    plt.bar(unique_scores, counts, alpha=0.7, edgecolor='black')
                    plt.title('Goodness Score Distribution')
                    plt.xlabel('Goodness Score')
                    plt.ylabel('Frequency')

            # 5. Tag length distribution
            if "tag_statistics" in stats and "tag_length_stats" in stats["tag_statistics"]:
                plt.subplot(3, 3, 5)
                tag_lengths = []
                for entry in self.data:
                    if len(entry) >= 2 and isinstance(entry[1], list):
                        for tag_item in entry[1]:
                            if isinstance(tag_item, tuple) and len(tag_item) == 2:
                                tag_lengths.append(len(str(tag_item[0])))

                if tag_lengths:
                    plt.hist(tag_lengths, bins=30, alpha=0.7, edgecolor='black')
                    plt.title('Tag Length Distribution')
                    plt.xlabel('Tag Length (characters)')
                    plt.ylabel('Frequency')
                    plt.axvline(np.mean(tag_lengths), color='red', linestyle='--', label=f'Mean: {np.mean(tag_lengths):.1f}')
                    plt.legend()

            # 6. LoRA usage analysis
            if "lora_analysis" in stats and stats["lora_analysis"]:
                plt.subplot(3, 3, 6)
                lora_data = stats["lora_analysis"]["most_used_loras"][:15]
                if lora_data:
                    loras, counts = zip(*lora_data)
                    plt.barh(range(len(loras)), counts)
                    plt.yticks(range(len(loras)), [lora[:25] + '...' if len(lora) > 25 else lora for lora in loras])
                    plt.title('Top 15 Most Used LoRA Models')
                    plt.xlabel('Usage Count')
                    plt.gca().invert_yaxis()

            # 7. Validation accuracy overview
            if self.validation_results and "parsing_accuracy" in self.validation_results:
                plt.subplot(3, 3, 7)
                accuracy_data = self.validation_results["parsing_accuracy"]["accuracy_breakdown"]
                categories = list(accuracy_data.keys())
                accuracies = list(accuracy_data.values())

                colors = ['green' if acc >= 98 else 'orange' if acc >= 95 else 'red' for acc in accuracies]
                bars = plt.bar(categories, accuracies, color=colors, alpha=0.7, edgecolor='black')
                plt.title('Validation Accuracy by Category')
                plt.ylabel('Accuracy (%)')
                plt.xticks(rotation=45)
                plt.axhline(98, color='red', linestyle='--', label='98% Requirement')
                plt.legend()

                # Add value labels on bars
                for bar, acc in zip(bars, accuracies):
                    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                            f'{acc:.1f}%', ha='center', va='bottom')

            # 8. Technical parameters usage
            if "technical_params" in stats and stats["technical_params"]:
                plt.subplot(3, 3, 8)
                param_names = list(stats["technical_params"].keys())[:10]
                param_counts = [stats["technical_params"][param]["count"] for param in param_names]

                if param_names:
                    plt.bar(range(len(param_names)), param_counts, alpha=0.7, edgecolor='black')
                    plt.xticks(range(len(param_names)), [name[:15] + '...' if len(name) > 15 else name for name in param_names], rotation=45)
                    plt.title('Technical Parameters Usage')
                    plt.ylabel('Usage Count')

            # 9. Overall dataset health
            plt.subplot(3, 3, 9)
            if self.validation_results:
                health_metrics = {
                    'Format\nAccuracy': self.validation_results["format_validation"]["format_accuracy"],
                    'Tag\nAccuracy': self.validation_results["tag_validation"]["tag_accuracy"],
                    'Weight\nAccuracy': self.validation_results["weight_validation"]["weight_accuracy"],
                    'Overall\nAccuracy': self.validation_results["parsing_accuracy"]["overall_accuracy"]
                }

                metrics = list(health_metrics.keys())
                values = list(health_metrics.values())
                colors = ['green' if val >= 98 else 'orange' if val >= 95 else 'red' for val in values]

                bars = plt.bar(metrics, values, color=colors, alpha=0.7, edgecolor='black')
                plt.title('Dataset Health Overview')
                plt.ylabel('Accuracy (%)')
                plt.ylim(0, 105)
                plt.axhline(98, color='red', linestyle='--', alpha=0.5)

                # Add value labels
                for bar, val in zip(bars, values):
                    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                            f'{val:.1f}%', ha='center', va='bottom', fontweight='bold')

            plt.tight_layout()

            # Save the comprehensive visualization
            viz_path = os.path.join(self.output_dir, f"comprehensive_analysis_{self.timestamp}.png")
            plt.savefig(viz_path, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"Visualizations saved to: {viz_path}")
            return True

        except Exception as e:
            logger.error(f"Error creating visualizations: {e}")
            logger.error(traceback.format_exc())
            return False

    def generate_comprehensive_report(self) -> str:
        """
        Generate a comprehensive analysis report.

        Returns:
            Formatted report string
        """
        logger.info("Generating comprehensive report...")

        report = []
        report.append("=" * 80)
        report.append("COMPREHENSIVE STRUCTURED PROMPT DATA ANALYSIS REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Dataset: {self.pkl_file}")
        report.append("")

        # Dataset Overview
        if "structure" in self.analysis_results:
            structure = self.analysis_results["structure"]
            report.append("📊 DATASET OVERVIEW")
            report.append("-" * 40)
            report.append(f"Total entries: {structure['total_entries']:,}")
            report.append(f"Data type: {structure['data_type']}")

            if "format_validation" in structure:
                fv = structure["format_validation"]
                report.append(f"Correct format entries: {fv['correct_format_entries']:,} ({fv['correct_format_percentage']:.1f}%)")
                report.append(f"Format issues: {fv['format_issues']:,}")

            if structure["structure_issues"]:
                report.append(f"Structure issues found: {len(structure['structure_issues'])}")
                for issue in structure["structure_issues"][:5]:
                    report.append(f"  • {issue}")
                if len(structure["structure_issues"]) > 5:
                    report.append(f"  ... and {len(structure['structure_issues']) - 5} more")
            report.append("")

        # Statistics Summary
        if "statistics" in self.analysis_results:
            stats = self.analysis_results["statistics"]

            # Tag Statistics
            if "tag_statistics" in stats:
                ts = stats["tag_statistics"]
                report.append("🏷️  TAG ANALYSIS")
                report.append("-" * 40)
                report.append(f"Total tags: {ts.get('total_tags', 0):,}")
                report.append(f"Unique tags: {ts.get('unique_tags', 0):,}")
                report.append(f"Average tags per prompt: {ts.get('avg_tags_per_prompt', 0):.1f}")
                report.append(f"Tag count range: {ts.get('min_tags_per_prompt', 0)} - {ts.get('max_tags_per_prompt', 0)}")

                if "top_50_tags" in ts and ts["top_50_tags"]:
                    report.append("\nTop 10 Most Frequent Tags:")
                    for i, (tag, count) in enumerate(ts["top_50_tags"][:10], 1):
                        report.append(f"  {i:2d}. {tag}: {count:,} occurrences")

                if "long_tags" in ts and ts["long_tags"]:
                    report.append(f"\n⚠️  Long tags found (>100 chars): {len(ts['long_tags'])}")
                    for tag, length in ts["long_tags"][:3]:
                        report.append(f"  • {tag[:50]}... ({length} chars)")

                report.append("")

            # Weight Statistics
            if "weight_statistics" in stats:
                ws = stats["weight_statistics"]
                report.append("⚖️  WEIGHT ANALYSIS")
                report.append("-" * 40)
                report.append(f"Total weights: {ws.get('total_weights', 0):,}")
                report.append(f"Average weight: {ws.get('avg_weight', 0):.3f}")
                report.append(f"Weight range: {ws.get('min_weight', 0):.3f} - {ws.get('max_weight', 0):.3f}")

                if "weight_range_validation" in ws:
                    wrv = ws["weight_range_validation"]
                    report.append(f"Weights in normal range (-2.0 to 2.0): {wrv['percentage_valid']:.1f}%")
                    if wrv["outside_normal_range"] > 0:
                        report.append(f"⚠️  Weights outside normal range: {wrv['outside_normal_range']:,}")

                report.append("")

            # LoRA Analysis
            if "lora_analysis" in stats and stats["lora_analysis"]:
                la = stats["lora_analysis"]
                report.append("🎨 LORA MODEL ANALYSIS")
                report.append("-" * 40)
                report.append(f"Total LoRA usage: {la.get('total_lora_usage', 0):,}")
                report.append(f"Unique LoRA models: {la.get('unique_lora_models', 0):,}")

                if "most_used_loras" in la and la["most_used_loras"]:
                    report.append("\nTop 10 Most Used LoRA Models:")
                    for i, (lora, count) in enumerate(la["most_used_loras"][:10], 1):
                        report.append(f"  {i:2d}. {lora}: {count:,} uses")

                if "lora_weight_stats" in la:
                    lws = la["lora_weight_stats"]
                    report.append(f"\nLoRA Weight Statistics:")
                    report.append(f"  Average: {lws.get('avg_weight', 0):.3f}")
                    report.append(f"  Range: {lws.get('min_weight', 0):.3f} - {lws.get('max_weight', 0):.3f}")

                report.append("")

            # Goodness Score Analysis
            if "goodness_statistics" in stats:
                gs = stats["goodness_statistics"]
                report.append("⭐ GOODNESS SCORE ANALYSIS")
                report.append("-" * 40)
                report.append(f"Total scores: {gs.get('total_scores', 0):,}")
                report.append(f"Average goodness: {gs.get('avg_goodness', 0):.3f}")
                report.append(f"Score range: {gs.get('min_goodness', 0):.3f} - {gs.get('max_goodness', 0):.3f}")

                if "score_distribution" in gs:
                    report.append("\nScore Distribution:")
                    for score, count in sorted(gs["score_distribution"].items()):
                        percentage = (count / gs["total_scores"]) * 100
                        report.append(f"  Score {score}: {count:,} ({percentage:.1f}%)")

                report.append("")

        # Validation Results
        if self.validation_results:
            vr = self.validation_results
            report.append("✅ VALIDATION RESULTS")
            report.append("-" * 40)

            if "parsing_accuracy" in vr:
                pa = vr["parsing_accuracy"]
                overall_acc = pa.get("overall_accuracy", 0)
                meets_req = pa.get("meets_98_percent_requirement", False)

                report.append(f"Overall Parsing Accuracy: {overall_acc:.2f}%")
                report.append(f"Meets 98% Requirement: {'✅ YES' if meets_req else '❌ NO'}")
                report.append(f"Total Issues Found: {pa.get('total_issues', 0):,}")

                if "accuracy_breakdown" in pa:
                    report.append("\nAccuracy Breakdown:")
                    for category, accuracy in pa["accuracy_breakdown"].items():
                        status = "✅" if accuracy >= 98 else "⚠️" if accuracy >= 95 else "❌"
                        report.append(f"  {status} {category.replace('_', ' ').title()}: {accuracy:.2f}%")

            # Error Analysis
            if "error_analysis" in vr and vr["error_analysis"]:
                report.append(f"\n⚠️  Error Analysis ({len(vr['error_analysis'])} issues):")
                for error in vr["error_analysis"][:10]:
                    report.append(f"  • {error}")
                if len(vr["error_analysis"]) > 10:
                    report.append(f"  ... and {len(vr['error_analysis']) - 10} more errors")

            report.append("")

        # Recommendations
        report.append("💡 RECOMMENDATIONS")
        report.append("-" * 40)

        if self.validation_results:
            overall_acc = self.validation_results.get("parsing_accuracy", {}).get("overall_accuracy", 0)

            if overall_acc >= 98:
                report.append("🎉 Dataset quality is excellent!")
                report.append("✅ Ready for production use")
                report.append("✅ Parsing accuracy meets requirements")
            elif overall_acc >= 95:
                report.append("⚠️  Dataset quality is good but could be improved")
                report.append("• Consider investigating and fixing parsing errors")
                report.append("• Review tag validation issues")
            else:
                report.append("❌ Dataset quality needs significant improvement")
                report.append("• Regenerate dataset with improved parsing")
                report.append("• Fix underlying parsing code issues")
                report.append("• Validate LoRA extraction accuracy")

        # Technical Issues
        if "statistics" in self.analysis_results:
            stats = self.analysis_results["statistics"]

            if "tag_statistics" in stats and "long_tags" in stats["tag_statistics"] and stats["tag_statistics"]["long_tags"]:
                report.append("• Investigate and fix long tags (>100 characters)")

            if "weight_statistics" in stats and "weight_range_validation" in stats["weight_statistics"]:
                wrv = stats["weight_statistics"]["weight_range_validation"]
                if wrv["outside_normal_range"] > 0:
                    report.append("• Review weights outside normal range (-2.0 to 2.0)")

        report.append("")
        report.append("=" * 80)
        report.append("END OF REPORT")
        report.append("=" * 80)

        return "\n".join(report)

    def display_sample_data(self, num_samples: int = 5) -> None:
        """
        Display sample entries from the dataset to show the format.

        Args:
            num_samples: Number of sample entries to display
        """
        logger.info(f"Displaying {num_samples} sample entries...")

        if not self.data:
            logger.error("No data loaded")
            return

        print("\n" + "=" * 80)
        print("SAMPLE DATA ENTRIES")
        print("=" * 80)

        sample_size = min(num_samples, len(self.data))

        for i in range(sample_size):
            entry = self.data[i]
            print(f"\n--- Sample {i+1} ---")

            try:
                if len(entry) >= 3:
                    filename, tags, goodness = entry[0], entry[1], entry[2]

                    print(f"Filename: {Path(filename).name if isinstance(filename, str) else filename}")
                    print(f"Goodness Score: {goodness}")
                    print(f"Number of Tags: {len(tags) if isinstance(tags, list) else 'N/A'}")

                    if isinstance(tags, list) and len(tags) > 0:
                        print("Sample Tags (first 10):")
                        for j, tag_item in enumerate(tags[:10]):
                            if isinstance(tag_item, tuple) and len(tag_item) == 2:
                                tag, weight = tag_item
                                print(f"  {j+1:2d}. ({tag}, {weight})")
                            else:
                                print(f"  {j+1:2d}. {tag_item} (malformed)")

                        if len(tags) > 10:
                            print(f"  ... and {len(tags) - 10} more tags")
                    else:
                        print("Tags: None or empty")
                else:
                    print(f"Entry format: {type(entry)} with {len(entry) if hasattr(entry, '__len__') else 'unknown'} elements")
                    print(f"Content: {str(entry)[:200]}...")

            except Exception as e:
                print(f"Error displaying entry: {e}")

        print("\n" + "=" * 80)

    def save_results(self) -> bool:
        """
        Save all analysis results to files.

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Saving analysis results...")

            # Save analysis results as JSON
            results_file = os.path.join(self.output_dir, f"analysis_results_{self.timestamp}.json")
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "analysis_results": self.analysis_results,
                    "validation_results": self.validation_results,
                    "timestamp": self.timestamp,
                    "dataset_file": self.pkl_file
                }, f, indent=2, default=str)

            # Save comprehensive report
            report = self.generate_comprehensive_report()
            report_file = os.path.join(self.output_dir, f"comprehensive_report_{self.timestamp}.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            # Print report to console
            print("\n" + report)

            logger.info(f"Results saved to:")
            logger.info(f"  JSON: {results_file}")
            logger.info(f"  Report: {report_file}")

            return True

        except Exception as e:
            logger.error(f"Error saving results: {e}")
            logger.error(traceback.format_exc())
            return False

    def run_comprehensive_analysis(self, skip_viz: bool = False, num_samples: int = 5) -> bool:
        """
        Run the complete analysis pipeline.

        Args:
            skip_viz: Whether to skip visualization generation
            num_samples: Number of sample entries to display

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting comprehensive analysis...")

            # Load data
            if not self.load_data():
                return False

            # Display sample data
            self.display_sample_data(num_samples)

            # Run all analysis components
            logger.info("Running structure analysis...")
            self.analyze_data_structure()

            logger.info("Generating statistics...")
            self.generate_comprehensive_statistics()

            logger.info("Performing validation...")
            self.validate_data_quality()

            # Create visualizations (unless skipped)
            if not skip_viz:
                logger.info("Creating visualizations...")
                self.create_visualizations()
            else:
                logger.info("Skipping visualization generation...")

            logger.info("Saving results...")
            self.save_results()

            # Final summary
            if self.validation_results and "parsing_accuracy" in self.validation_results:
                overall_acc = self.validation_results["parsing_accuracy"]["overall_accuracy"]
                meets_req = self.validation_results["parsing_accuracy"]["meets_98_percent_requirement"]

                print(f"\n🎯 FINAL SUMMARY")
                print(f"Overall Parsing Accuracy: {overall_acc:.2f}%")
                print(f"Meets 98% Requirement: {'✅ YES' if meets_req else '❌ NO'}")

                if meets_req:
                    print("🎉 Dataset is ready for production use!")
                else:
                    print("⚠️  Dataset needs improvement before production use.")

            logger.info("Comprehensive analysis completed successfully")
            return True

        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {e}")
            logger.error(traceback.format_exc())
            return False
        """
        Run the complete analysis pipeline.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting comprehensive analysis...")
            
            # Load data
            if not self.load_data():
                return False
            
            # Run all analysis components
            self.analyze_data_structure()
            
            logger.info("Comprehensive analysis completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {e}")
            logger.error(traceback.format_exc())
            return False


def main():
    """Main function to run the comprehensive analysis."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Comprehensive analysis of structured prompt data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python comprehensive_structured_analysis.py
  python comprehensive_structured_analysis.py --file my_data.pkl --samples 10
  python comprehensive_structured_analysis.py --quick-check --no-viz
  python comprehensive_structured_analysis.py --output-dir custom_results
        """
    )

    parser.add_argument("--file", default="promptlabels_structured.pkl",
                       help="Path to structured pickle file (default: promptlabels_structured.pkl)")
    parser.add_argument("--output-dir", help="Output directory for results")
    parser.add_argument("--samples", type=int, default=5,
                       help="Number of sample entries to display (default: 5)")
    parser.add_argument("--quick-check", action="store_true",
                       help="Run quick validation check only")
    parser.add_argument("--no-viz", action="store_true",
                       help="Skip visualization generation")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize analyzer
    analyzer = StructuredDataAnalyzer(args.file)

    if args.output_dir:
        analyzer.output_dir = args.output_dir
        os.makedirs(analyzer.output_dir, exist_ok=True)

    try:
        # Load data first
        if not analyzer.load_data():
            return 1

        if args.quick_check:
            # Quick validation check only
            logger.info("Running quick validation check...")
            analyzer.display_sample_data(args.samples)
            analyzer.analyze_data_structure()
            validation_results = analyzer.validate_data_quality()

            # Print quick summary
            if validation_results and "parsing_accuracy" in validation_results:
                overall_acc = validation_results["parsing_accuracy"]["overall_accuracy"]
                meets_req = validation_results["parsing_accuracy"]["meets_98_percent_requirement"]

                print(f"\n🎯 QUICK CHECK RESULTS")
                print(f"Overall Parsing Accuracy: {overall_acc:.2f}%")
                print(f"Meets 98% Requirement: {'✅ YES' if meets_req else '❌ NO'}")

                if not meets_req:
                    print("\n⚠️  Issues found:")
                    for error in validation_results.get("error_analysis", [])[:5]:
                        print(f"  • {error}")

            return 0 if meets_req else 1
        else:
            # Full comprehensive analysis
            success = analyzer.run_comprehensive_analysis(
                skip_viz=args.no_viz,
                num_samples=args.samples
            )

            if success:
                logger.info("🎉 Analysis completed successfully!")
                logger.info(f"Results saved to: {analyzer.output_dir}")

                # Check if visualization was skipped
                if args.no_viz:
                    logger.info("Visualization generation was skipped (--no-viz)")

                return 0
            else:
                logger.error("❌ Analysis failed!")
                return 1

    except KeyboardInterrupt:
        logger.info("Analysis interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())

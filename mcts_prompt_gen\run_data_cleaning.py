#!/usr/bin/env python3
"""
Runner script for data cleaning operations.

This script provides a command-line interface for running data cleaning operations
with various options and configurations.
"""

import os
import sys
import argparse
from pathlib import Path

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from utils import stderr_print
from data_cleaning_script import DataCleaner
from test_data_cleaning import validate_pickle_file, test_data_cleaner


def run_full_cleaning(output_path: str = "promptlabels.pkl", custom_dirs: list = None,
                     enhanced_prompts: bool = True):
    """Run the full data cleaning process."""
    cleaner = DataCleaner()

    if custom_dirs:
        cleaner.input_directories = custom_dirs
        stderr_print(f"Using custom input directories: {custom_dirs}")

    extraction_type = "enhanced prompts (with ComfyUI parameters)" if enhanced_prompts else "basic prompts"
    stderr_print(f"Starting full data cleaning process with {extraction_type}...")
    stderr_print(f"Output will be saved to: {output_path}")

    try:
        # Phase 1: Collect all images with quality labels
        stderr_print("\n" + "="*60)
        stderr_print("PHASE 1: COLLECTING IMAGES AND QUALITY LABELS")
        stderr_print("="*60)
        images_with_quality = cleaner.collect_all_images_with_quality()

        if not images_with_quality:
            stderr_print("No images found to process!")
            return False

        stderr_print(f"Phase 1 complete: Found {len(images_with_quality)} images")

        # Phase 2: Extract prompts from images
        stderr_print("\n" + "="*60)
        stderr_print(f"PHASE 2: EXTRACTING {extraction_type.upper()}")
        stderr_print("="*60)
        results = cleaner.process_images_with_prompts(images_with_quality, enhanced_prompts=enhanced_prompts)

        # Save results
        stderr_print(f"\nSaving results to {output_path}...")
        cleaner.save_results(results, output_path)

        # Print statistics
        cleaner.print_statistics()

        stderr_print(f"\n✓ Processing complete! Results saved to {output_path}")
        return True

    except KeyboardInterrupt:
        stderr_print("\n✗ Processing interrupted by user.")
        return False
    except Exception as e:
        stderr_print(f"\n✗ Error during processing: {e}")
        return False


def run_dry_run(custom_dirs: list = None):
    """Run a dry run to see what would be processed without actually extracting prompts."""
    cleaner = DataCleaner()
    
    if custom_dirs:
        cleaner.input_directories = custom_dirs
    
    stderr_print("Running dry run (Phase 1 only)...")
    
    try:
        images_with_quality = cleaner.collect_all_images_with_quality()
        
        if not images_with_quality:
            stderr_print("No images found to process!")
            return False
        
        # Print summary without extracting prompts
        stderr_print(f"\nDry run complete:")
        stderr_print(f"  Total images found: {len(images_with_quality)}")
        stderr_print(f"  Good quality: {cleaner.stats['good_images']}")
        stderr_print(f"  Normal quality: {cleaner.stats['normal_images']}")
        stderr_print(f"  Directories processed: {cleaner.stats['directories_processed']}")
        stderr_print(f"  Date directories found: {cleaner.stats['date_directories_found']}")
        
        # Show sample files
        stderr_print(f"\nSample files (first 10):")
        for i, (filepath, quality) in enumerate(images_with_quality[:10]):
            stderr_print(f"  {i+1}. {Path(filepath).name} ({quality})")
        
        if len(images_with_quality) > 10:
            stderr_print(f"  ... and {len(images_with_quality) - 10} more files")
        
        return True
        
    except Exception as e:
        stderr_print(f"Error during dry run: {e}")
        return False


def list_directories():
    """List the default input directories and check their existence."""
    cleaner = DataCleaner()
    
    stderr_print("Default input directories:")
    stderr_print("="*60)
    
    for i, directory in enumerate(cleaner.input_directories, 1):
        path = Path(directory)
        exists = path.exists()
        status = "✓ EXISTS" if exists else "✗ NOT FOUND"
        
        stderr_print(f"{i}. {directory}")
        stderr_print(f"   Status: {status}")
        
        if exists:
            try:
                # Count subdirectories
                subdirs = [item for item in path.iterdir() if item.is_dir()]
                date_dirs = [item for item in subdirs if cleaner.is_date_directory(item.name)]
                stderr_print(f"   Subdirectories: {len(subdirs)} total, {len(date_dirs)} date-based")
            except (PermissionError, OSError):
                stderr_print(f"   Warning: Cannot access directory")
        
        stderr_print()


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Data cleaning script for image datasets with prompt extraction",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --run                          # Run full cleaning process
  %(prog)s --dry-run                      # See what would be processed
  %(prog)s --list-dirs                    # List input directories
  %(prog)s --test                         # Run test with temporary data
  %(prog)s --validate promptlabels.pkl    # Validate existing results
  %(prog)s --run --output my_results.pkl  # Save to custom file
  %(prog)s --run --dirs "C:/path1" "C:/path2"  # Use custom directories
        """
    )
    
    # Main actions (mutually exclusive)
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument('--run', action='store_true', 
                             help='Run the full data cleaning process')
    action_group.add_argument('--dry-run', action='store_true',
                             help='Run dry run (collect filenames only, no prompt extraction)')
    action_group.add_argument('--list-dirs', action='store_true',
                             help='List default input directories and their status')
    action_group.add_argument('--test', action='store_true',
                             help='Run test with temporary directory structure')
    action_group.add_argument('--validate', type=str, metavar='PICKLE_FILE',
                             help='Validate an existing pickle file')
    
    # Options
    parser.add_argument('--output', '-o', type=str, default='promptlabels.pkl',
                       help='Output pickle file path (default: promptlabels.pkl)')
    parser.add_argument('--dirs', nargs='+', metavar='DIR',
                       help='Custom input directories (overrides defaults)')
    parser.add_argument('--basic-prompts', action='store_true',
                       help='Extract basic prompts only (disable enhanced ComfyUI parameter extraction)')
    
    args = parser.parse_args()
    
    # Execute the requested action
    success = True
    
    if args.run:
        # Check for enhanced prompts flag
        enhanced = not getattr(args, 'basic_prompts', False)
        success = run_full_cleaning(args.output, args.dirs, enhanced_prompts=enhanced)
    
    elif args.dry_run:
        success = run_dry_run(args.dirs)
    
    elif args.list_dirs:
        list_directories()
    
    elif args.test:
        stderr_print("Running test with temporary directory structure...")
        success = test_data_cleaner()
        if success:
            stderr_print("✓ Test completed successfully!")
        else:
            stderr_print("✗ Test failed!")
    
    elif args.validate:
        stderr_print(f"Validating pickle file: {args.validate}")
        success = validate_pickle_file(args.validate)
        if success:
            stderr_print("✓ Validation successful!")
        else:
            stderr_print("✗ Validation failed!")
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Goodness Predictor Models

This module implements modular model architectures for predicting prompt goodness scores
from structured prompt data containing (filename, [(tag, weight) tuples], goodness_score).

Features:
- Base class architecture with standard interfaces
- LightGBM ranking/regression model
- ResNet-like neural network model
- Comprehensive feature engineering
- NDCG and precision@k evaluation metrics
- Model persistence and loading

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import List, Tuple, Dict, Any, Optional, Union
from datetime import datetime
import pickle
import dill

# ML imports
import lightgbm as lgb
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import ndcg_score, precision_score, recall_score, roc_auc_score
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from scipy.sparse import hstack, csr_matrix

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BaseGoodnessPredictorModel(ABC):
    """
    Base class for goodness prediction models.
    
    Provides standard interface for training, prediction, evaluation, and persistence.
    """
    
    def __init__(self, model_name: str):
        """
        Initialize the base model.
        
        Args:
            model_name: Name identifier for the model
        """
        self.model_name = model_name
        self.model = None
        self.feature_extractor = None
        self.is_trained = False
        self.training_history = {}
        self.feature_names = []
        
    @abstractmethod
    def train_model(self, X_train: List[Tuple[str, List[Tuple[str, float]], float]],
                    y_train: List[float],
                    X_val: Optional[List[Tuple[str, List[Tuple[str, float]], float]]] = None,
                    y_val: Optional[List[float]] = None,
                    **kwargs) -> Dict[str, Any]:
        """
        Train the model on structured prompt data.
        
        Args:
            X_train: Training data in format [(filename, [(tag, weight)], goodness)]
            y_train: Training labels (goodness scores)
            X_val: Optional validation data
            y_val: Optional validation labels
            **kwargs: Additional training parameters
            
        Returns:
            Dictionary containing training metrics and history
        """
        pass
    
    @abstractmethod
    def predict(self, prompt_data: Union[List[Tuple[str, List[Tuple[str, float]], float]], 
                                       Tuple[str, List[Tuple[str, float]], float]]) -> Union[List[float], float]:
        """
        Predict goodness scores for prompt data.
        
        Args:
            prompt_data: Single prompt or list of prompts in structured format
            
        Returns:
            Predicted goodness score(s)
        """
        pass
    
    @abstractmethod
    def evaluate(self, X_test: List[Tuple[str, List[Tuple[str, float]], float]], 
                 y_test: List[float]) -> Dict[str, float]:
        """
        Evaluate model performance using NDCG and precision@k metrics.
        
        Args:
            X_test: Test data
            y_test: Test labels
            
        Returns:
            Dictionary containing evaluation metrics
        """
        pass
    
    def save_model(self, save_path: str) -> None:
        """
        Save the trained model and feature extractor.
        
        Args:
            save_path: Path to save the model
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        model_data = {
            'model_name': self.model_name,
            'model': self.model,
            'feature_extractor': self.feature_extractor,
            'is_trained': self.is_trained,
            'training_history': self.training_history,
            'feature_names': self.feature_names,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(save_path, 'wb') as f:
            dill.dump(model_data, f)
        
        logger.info(f"Model saved to {save_path}")
    
    def load_model(self, load_path: str) -> None:
        """
        Load a trained model and feature extractor.
        
        Args:
            load_path: Path to load the model from
        """
        with open(load_path, 'rb') as f:
            model_data = dill.load(f)
        
        self.model_name = model_data['model_name']
        self.model = model_data['model']
        self.feature_extractor = model_data['feature_extractor']
        self.is_trained = model_data['is_trained']
        self.training_history = model_data.get('training_history', {})
        self.feature_names = model_data.get('feature_names', [])
        
        logger.info(f"Model loaded from {load_path}")


class PromptFeatureExtractor:
    """
    Comprehensive feature extractor for structured prompt data.
    
    Extracts both tag-based and weight-based features for ML models.
    """
    
    def __init__(self, max_features: int = 5000, ngram_range: Tuple[int, int] = (1, 2)):
        """
        Initialize the feature extractor.
        
        Args:
            max_features: Maximum number of TF-IDF features
            ngram_range: N-gram range for TF-IDF
        """
        self.max_features = max_features
        self.ngram_range = ngram_range
        
        # Feature extractors
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=max_features,
            ngram_range=ngram_range,
            stop_words=None,
            lowercase=True,
            token_pattern=r'[a-zA-Z0-9_]+',
            min_df=2
        )
        
        self.scaler = StandardScaler()
        self.is_fitted = False
        
        # Feature categories
        self.technical_params = {'cfg', 'steps', 'width', 'height', 'sampler', 'scheduler', 'seed'}
        self.quality_tags = {'masterpiece', 'best_quality', 'amazing_quality', 'very_aesthetic', 'absurdres'}
        self.common_tags = set()
        
    def fit(self, prompt_data: List[Tuple[str, List[Tuple[str, float]], float]]) -> None:
        """
        Fit the feature extractor on training data.
        
        Args:
            prompt_data: List of (filename, [(tag, weight)], goodness) tuples
        """
        logger.info("Fitting feature extractor...")
        
        # Extract text for TF-IDF
        text_data = []
        all_tags = set()
        
        for filename, tags, goodness in prompt_data:
            # Separate content tags from technical parameters
            content_tags = []
            for tag, weight in tags:
                all_tags.add(tag)
                if not self._is_technical_param(tag) and not self._is_lora_tag(tag):
                    content_tags.append(tag)
            
            text_data.append(' '.join(content_tags))
        
        # Fit TF-IDF vectorizer
        self.tfidf_vectorizer.fit(text_data)
        
        # Identify common tags for binary features
        tag_counts = {}
        for filename, tags, goodness in prompt_data:
            for tag, weight in tags:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # Select top tags for binary features (excluding technical params and LoRAs)
        content_tag_counts = {tag: count for tag, count in tag_counts.items() 
                            if not self._is_technical_param(tag) and not self._is_lora_tag(tag)}
        self.common_tags = set(sorted(content_tag_counts.keys(), 
                                    key=content_tag_counts.get, reverse=True)[:100])
        
        # Extract numerical features for scaling
        numerical_features = []
        for filename, tags, goodness in prompt_data:
            features = self._extract_numerical_features(tags)
            numerical_features.append(features)
        
        # Fit scaler
        self.scaler.fit(numerical_features)
        
        self.is_fitted = True
        logger.info(f"Feature extractor fitted: {len(self.tfidf_vectorizer.vocabulary_)} TF-IDF features, "
                   f"{len(self.common_tags)} common tags")
    
    def transform(self, prompt_data: List[Tuple[str, List[Tuple[str, float]], float]]) -> csr_matrix:
        """
        Transform prompt data into feature matrix.
        
        Args:
            prompt_data: List of (filename, [(tag, weight)], goodness) tuples
            
        Returns:
            Sparse feature matrix
        """
        if not self.is_fitted:
            raise ValueError("Feature extractor must be fitted before transform")
        
        # Extract text for TF-IDF
        text_data = []
        for filename, tags, goodness in prompt_data:
            content_tags = []
            for tag, weight in tags:
                if not self._is_technical_param(tag) and not self._is_lora_tag(tag):
                    content_tags.append(tag)
            text_data.append(' '.join(content_tags))
        
        # TF-IDF features
        tfidf_features = self.tfidf_vectorizer.transform(text_data)
        
        # Numerical features
        numerical_features = []
        binary_features = []
        
        for filename, tags, goodness in prompt_data:
            # Extract numerical features
            num_features = self._extract_numerical_features(tags)
            numerical_features.append(num_features)
            
            # Extract binary features
            bin_features = self._extract_binary_features(tags)
            binary_features.append(bin_features)
        
        # Scale numerical features
        numerical_features = self.scaler.transform(numerical_features)
        
        # Combine all features
        numerical_sparse = csr_matrix(numerical_features)
        binary_sparse = csr_matrix(binary_features)
        
        combined_features = hstack([tfidf_features, numerical_sparse, binary_sparse])
        
        return combined_features
    
    def _extract_numerical_features(self, tags: List[Tuple[str, float]]) -> List[float]:
        """Extract numerical features from tags."""
        features = []
        
        # Basic statistics
        weights = [weight for tag, weight in tags]
        features.extend([
            len(tags),  # tag_count
            np.mean(weights) if weights else 0.0,  # avg_weight
            np.std(weights) if len(weights) > 1 else 0.0,  # weight_std
            np.min(weights) if weights else 0.0,  # min_weight
            np.max(weights) if weights else 0.0,  # max_weight
            np.median(weights) if weights else 0.0,  # median_weight
        ])
        
        # Technical parameter values
        tech_values = {}
        for tag, weight in tags:
            if self._is_technical_param(tag):
                tech_values[tag] = weight
        
        features.extend([
            tech_values.get('cfg', 0.0),
            tech_values.get('steps', 0.0),
            tech_values.get('width', 0.0),
            tech_values.get('height', 0.0),
            tech_values.get('width', 0.0) * tech_values.get('height', 0.0) / 1000000.0,  # megapixels
        ])
        
        # LoRA statistics
        lora_count = sum(1 for tag, weight in tags if self._is_lora_tag(tag))
        lora_weights = [weight for tag, weight in tags if self._is_lora_tag(tag)]
        features.extend([
            lora_count,
            np.mean(lora_weights) if lora_weights else 0.0,
            len([w for w in lora_weights if w < 0]),  # negative_lora_count
        ])
        
        return features
    
    def _extract_binary_features(self, tags: List[Tuple[str, float]]) -> List[float]:
        """Extract binary features from tags."""
        features = []
        
        tag_set = {tag for tag, weight in tags}
        
        # Common tag presence
        for common_tag in sorted(self.common_tags):
            features.append(1.0 if common_tag in tag_set else 0.0)
        
        # Quality tag presence
        for quality_tag in sorted(self.quality_tags):
            features.append(1.0 if quality_tag in tag_set else 0.0)
        
        # Technical parameter presence
        for tech_param in sorted(self.technical_params):
            features.append(1.0 if tech_param in tag_set else 0.0)
        
        return features
    
    def _is_technical_param(self, tag: str) -> bool:
        """Check if tag is a technical parameter."""
        return tag.lower() in self.technical_params
    
    def _is_lora_tag(self, tag: str) -> bool:
        """Check if tag is a LoRA model reference."""
        return (tag.startswith('<lora:') and tag.endswith('>')) or tag.startswith('lora_')
    
    def get_feature_names(self) -> List[str]:
        """Get names of all features."""
        feature_names = []
        
        # TF-IDF feature names
        if hasattr(self.tfidf_vectorizer, 'get_feature_names_out'):
            tfidf_names = self.tfidf_vectorizer.get_feature_names_out()
        else:
            tfidf_names = self.tfidf_vectorizer.get_feature_names()
        feature_names.extend([f'tfidf_{name}' for name in tfidf_names])
        
        # Numerical feature names
        numerical_names = [
            'tag_count', 'avg_weight', 'weight_std', 'min_weight', 'max_weight', 'median_weight',
            'cfg_value', 'steps_value', 'width_value', 'height_value', 'megapixels',
            'lora_count', 'avg_lora_weight', 'negative_lora_count'
        ]
        feature_names.extend(numerical_names)
        
        # Binary feature names
        for common_tag in sorted(self.common_tags):
            feature_names.append(f'has_{common_tag}')
        for quality_tag in sorted(self.quality_tags):
            feature_names.append(f'has_{quality_tag}')
        for tech_param in sorted(self.technical_params):
            feature_names.append(f'has_{tech_param}')
        
        return feature_names


class LightGBMGoodnessPredictorModel(BaseGoodnessPredictorModel):
    """
    LightGBM-based goodness prediction model using ranking/regression objective.

    Optimized for handling high-dimensional sparse features with comprehensive
    hyperparameter tuning and ranking-focused evaluation.
    """

    def __init__(self, max_features: int = 5000, random_state: int = 42):
        """
        Initialize LightGBM model.

        Args:
            max_features: Maximum number of TF-IDF features
            random_state: Random seed for reproducibility
        """
        super().__init__("LightGBM_Goodness_Predictor")
        self.max_features = max_features
        self.random_state = random_state
        self.feature_extractor = PromptFeatureExtractor(max_features=max_features)

        # Default hyperparameters (can be optimized)
        self.params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.1,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': random_state,
            'n_estimators': 100
        }

    def train_model(self, X_train: List[Tuple[str, List[Tuple[str, float]], float]],
                    y_train: List[float],
                    X_val: Optional[List[Tuple[str, List[Tuple[str, float]], float]]] = None,
                    y_val: Optional[List[float]] = None,
                    optimize_hyperparams: bool = True,
                    n_trials: int = 50,
                    **kwargs) -> Dict[str, Any]:
        """
        Train the LightGBM model with optional hyperparameter optimization.

        Args:
            X_train: Training data
            y_train: Training labels
            X_val: Validation data
            y_val: Validation labels
            optimize_hyperparams: Whether to optimize hyperparameters
            n_trials: Number of optimization trials
            **kwargs: Additional parameters

        Returns:
            Training metrics and history
        """
        logger.info(f"Training {self.model_name} on {len(X_train)} samples...")

        # Fit feature extractor
        self.feature_extractor.fit(X_train)

        # Transform training data
        X_train_features = self.feature_extractor.transform(X_train)

        # Transform validation data if provided
        X_val_features = None
        if X_val is not None and y_val is not None:
            X_val_features = self.feature_extractor.transform(X_val)

        # Hyperparameter optimization
        if optimize_hyperparams:
            logger.info("Optimizing hyperparameters...")
            self.params = self._optimize_hyperparameters(
                X_train_features, y_train, X_val_features, y_val, n_trials
            )

        # Train final model
        train_data = lgb.Dataset(X_train_features, label=y_train)
        valid_sets = [train_data]
        valid_names = ['train']

        if X_val_features is not None:
            val_data = lgb.Dataset(X_val_features, label=y_val, reference=train_data)
            valid_sets.append(val_data)
            valid_names.append('valid')

        # Training callbacks
        callbacks = [
            lgb.log_evaluation(period=10),
            lgb.early_stopping(stopping_rounds=20, verbose=True)
        ]

        # Train model
        self.model = lgb.train(
            self.params,
            train_data,
            valid_sets=valid_sets,
            valid_names=valid_names,
            callbacks=callbacks
        )

        self.is_trained = True
        self.feature_names = self.feature_extractor.get_feature_names()

        # Calculate training metrics
        train_pred = self.model.predict(X_train_features)
        train_metrics = self._calculate_metrics(y_train, train_pred)

        val_metrics = {}
        if X_val_features is not None:
            val_pred = self.model.predict(X_val_features)
            val_metrics = self._calculate_metrics(y_val, val_pred)

        self.training_history = {
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'params': self.params,
            'feature_importance': self.get_feature_importance()
        }

        logger.info(f"Training complete. Train RMSE: {train_metrics.get('rmse', 0):.4f}")
        if val_metrics:
            logger.info(f"Validation RMSE: {val_metrics.get('rmse', 0):.4f}")

        return self.training_history

    def predict(self, prompt_data: Union[List[Tuple[str, List[Tuple[str, float]], float]],
                                       Tuple[str, List[Tuple[str, float]], float]]) -> Union[List[float], float]:
        """
        Predict goodness scores for prompt data.

        Args:
            prompt_data: Single prompt or list of prompts

        Returns:
            Predicted goodness score(s)
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")

        # Handle single prompt
        if isinstance(prompt_data, tuple):
            prompt_data = [prompt_data]
            single_prediction = True
        else:
            single_prediction = False

        # Transform features
        features = self.feature_extractor.transform(prompt_data)

        # Predict
        predictions = self.model.predict(features)

        # Clip predictions to valid range [0, 1]
        predictions = np.clip(predictions, 0.0, 1.0)

        if single_prediction:
            return float(predictions[0])
        else:
            return predictions.tolist()

    def evaluate(self, X_test: List[Tuple[str, List[Tuple[str, float]], float]],
                 y_test: List[float]) -> Dict[str, float]:
        """
        Evaluate model using comprehensive ranking metrics.

        Args:
            X_test: Test data
            y_test: Test labels

        Returns:
            Dictionary of evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")

        logger.info(f"Evaluating {self.model_name} on {len(X_test)} test samples...")

        # Get predictions
        y_pred = self.predict(X_test)

        # Calculate comprehensive metrics
        metrics = self._calculate_metrics(y_test, y_pred)

        # Add ranking-specific metrics
        ranking_metrics = self._calculate_ranking_metrics(y_test, y_pred)
        metrics.update(ranking_metrics)

        logger.info(f"Evaluation complete. NDCG: {metrics.get('ndcg', 0):.4f}, "
                   f"Precision@10: {metrics.get('precision_at_10', 0):.4f}")

        return metrics

    def get_feature_importance(self, importance_type: str = 'gain') -> Dict[str, float]:
        """
        Get feature importance scores.

        Args:
            importance_type: Type of importance ('gain', 'split', 'weight')

        Returns:
            Dictionary mapping feature names to importance scores
        """
        if not self.is_trained:
            raise ValueError("Model must be trained to get feature importance")

        importance_scores = self.model.feature_importance(importance_type=importance_type)
        feature_names = self.feature_names

        if len(feature_names) != len(importance_scores):
            # Fallback to generic names if mismatch
            feature_names = [f'feature_{i}' for i in range(len(importance_scores))]

        importance_dict = dict(zip(feature_names, importance_scores))

        # Sort by importance
        sorted_importance = dict(sorted(importance_dict.items(),
                                      key=lambda x: x[1], reverse=True))

        return sorted_importance

    def _optimize_hyperparameters(self, X_train, y_train, X_val, y_val, n_trials: int) -> Dict[str, Any]:
        """
        Optimize hyperparameters using Optuna.

        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features
            y_val: Validation labels
            n_trials: Number of optimization trials

        Returns:
            Optimized parameters
        """
        try:
            import optuna

            def objective(trial):
                params = {
                    'objective': 'regression',
                    'metric': 'rmse',
                    'boosting_type': 'gbdt',
                    'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                    'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                    'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                    'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                    'verbose': -1,
                    'random_state': self.random_state,
                    'n_estimators': 200
                }

                train_data = lgb.Dataset(X_train, label=y_train)

                if X_val is not None and y_val is not None:
                    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                    valid_sets = [train_data, val_data]
                    valid_names = ['train', 'valid']
                else:
                    valid_sets = [train_data]
                    valid_names = ['train']

                model = lgb.train(
                    params,
                    train_data,
                    valid_sets=valid_sets,
                    valid_names=valid_names,
                    callbacks=[lgb.early_stopping(stopping_rounds=10, verbose=False)]
                )

                if X_val is not None and y_val is not None:
                    val_pred = model.predict(X_val)
                    rmse = np.sqrt(np.mean((y_val - val_pred) ** 2))
                else:
                    train_pred = model.predict(X_train)
                    rmse = np.sqrt(np.mean((y_train - train_pred) ** 2))

                return rmse

            study = optuna.create_study(direction='minimize')
            study.optimize(objective, n_trials=n_trials, show_progress_bar=True)

            best_params = study.best_params
            best_params.update({
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'verbose': -1,
                'random_state': self.random_state,
                'n_estimators': 200
            })

            logger.info(f"Hyperparameter optimization complete. Best RMSE: {study.best_value:.4f}")
            return best_params

        except ImportError:
            logger.warning("Optuna not available. Using default parameters.")
            return self.params

    def _calculate_metrics(self, y_true: List[float], y_pred: List[float]) -> Dict[str, float]:
        """Calculate basic regression metrics."""
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)

        metrics = {
            'rmse': np.sqrt(np.mean((y_true - y_pred) ** 2)),
            'mae': np.mean(np.abs(y_true - y_pred)),
            'r2': 1 - np.sum((y_true - y_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2),
            'mean_pred': np.mean(y_pred),
            'std_pred': np.std(y_pred)
        }

        # Add classification metrics if binary labels
        unique_labels = np.unique(y_true)
        if len(unique_labels) == 2 and set(unique_labels) == {0.0, 1.0}:
            try:
                metrics['auc'] = roc_auc_score(y_true, y_pred)
            except:
                pass

        return metrics

    def _calculate_ranking_metrics(self, y_true: List[float], y_pred: List[float]) -> Dict[str, float]:
        """Calculate ranking-specific metrics."""
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)

        metrics = {}

        try:
            # NDCG calculation
            # Reshape for sklearn's ndcg_score which expects 2D arrays
            y_true_2d = y_true.reshape(1, -1)
            y_pred_2d = y_pred.reshape(1, -1)

            metrics['ndcg'] = ndcg_score(y_true_2d, y_pred_2d)

            # Precision@K calculations
            for k in [5, 10, 20]:
                if len(y_true) >= k:
                    # Get top-k predictions
                    top_k_indices = np.argsort(y_pred)[-k:]
                    top_k_true = y_true[top_k_indices]

                    # Calculate precision@k (fraction of top-k that are actually good)
                    precision_k = np.mean(top_k_true)
                    metrics[f'precision_at_{k}'] = precision_k

            # Mean score difference between good and normal prompts
            good_mask = y_true == 1.0
            normal_mask = y_true == 0.0

            if np.any(good_mask) and np.any(normal_mask):
                good_scores = y_pred[good_mask]
                normal_scores = y_pred[normal_mask]
                metrics['score_difference'] = np.mean(good_scores) - np.mean(normal_scores)

        except Exception as e:
            logger.warning(f"Error calculating ranking metrics: {e}")

        return metrics

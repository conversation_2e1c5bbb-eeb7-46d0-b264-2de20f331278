#!/usr/bin/env python3
"""
Example usage of the data cleaning results.

This script demonstrates how to load and use the results from the data cleaning process.
"""

import os
import sys
from pathlib import Path
from collections import Counter, defaultdict
import dill

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from utils import stderr_print


def load_dataset(pickle_path: str = "promptlabels.pkl"):
    """Load the dataset from pickle file."""
    try:
        with open(pickle_path, 'rb') as f:
            results = dill.load(f)
        stderr_print(f"Loaded {len(results)} entries from {pickle_path}")
        return results
    except FileNotFoundError:
        stderr_print(f"Error: File {pickle_path} not found!")
        stderr_print("Run the data cleaning script first: python run_data_cleaning.py --run")
        return None
    except Exception as e:
        stderr_print(f"Error loading dataset: {e}")
        return None


def analyze_dataset(results):
    """Analyze the dataset and print statistics."""
    if not results:
        return
    
    stderr_print("\n" + "="*60)
    stderr_print("DATASET ANALYSIS")
    stderr_print("="*60)
    
    # Basic statistics
    total_entries = len(results)
    good_count = sum(1 for _, _, quality in results if quality == 'good')
    normal_count = sum(1 for _, _, quality in results if quality == 'normal')
    empty_prompts = sum(1 for _, prompt, _ in results if not prompt.strip())
    
    stderr_print(f"Total entries: {total_entries}")
    stderr_print(f"Good quality: {good_count} ({good_count/total_entries*100:.1f}%)")
    stderr_print(f"Normal quality: {normal_count} ({normal_count/total_entries*100:.1f}%)")
    stderr_print(f"Empty prompts: {empty_prompts} ({empty_prompts/total_entries*100:.1f}%)")
    
    # Prompt length statistics
    prompt_lengths = [len(prompt) for _, prompt, _ in results if prompt.strip()]
    if prompt_lengths:
        avg_length = sum(prompt_lengths) / len(prompt_lengths)
        max_length = max(prompt_lengths)
        min_length = min(prompt_lengths)
        
        stderr_print(f"\nPrompt length statistics:")
        stderr_print(f"  Average: {avg_length:.1f} characters")
        stderr_print(f"  Maximum: {max_length} characters")
        stderr_print(f"  Minimum: {min_length} characters")
    
    # Directory distribution
    dir_counter = Counter()
    for filename, _, _ in results:
        parent_dir = Path(filename).parent.name
        dir_counter[parent_dir] += 1
    
    stderr_print(f"\nDirectory distribution (top 10):")
    for dir_name, count in dir_counter.most_common(10):
        stderr_print(f"  {dir_name}: {count} images")
    
    # File extension distribution
    ext_counter = Counter()
    for filename, _, _ in results:
        ext = Path(filename).suffix.lower()
        ext_counter[ext] += 1
    
    stderr_print(f"\nFile extension distribution:")
    for ext, count in ext_counter.most_common():
        stderr_print(f"  {ext}: {count} images")


def show_sample_entries(results, n=5):
    """Show sample entries from the dataset."""
    if not results:
        return
    
    stderr_print(f"\n" + "="*60)
    stderr_print(f"SAMPLE ENTRIES (first {n})")
    stderr_print("="*60)
    
    for i, (filename, prompt, quality) in enumerate(results[:n]):
        stderr_print(f"\nEntry {i+1}:")
        stderr_print(f"  File: {Path(filename).name}")
        stderr_print(f"  Path: {filename}")
        stderr_print(f"  Quality: {quality}")
        stderr_print(f"  Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")


def filter_by_quality(results, quality='good'):
    """Filter results by quality label."""
    filtered = [(filename, prompt, q) for filename, prompt, q in results if q == quality]
    stderr_print(f"Filtered {len(filtered)} entries with quality '{quality}'")
    return filtered


def filter_by_prompt_keywords(results, keywords, case_sensitive=False):
    """Filter results by keywords in prompts."""
    if not case_sensitive:
        keywords = [kw.lower() for kw in keywords]
    
    filtered = []
    for filename, prompt, quality in results:
        search_prompt = prompt if case_sensitive else prompt.lower()
        if any(kw in search_prompt for kw in keywords):
            filtered.append((filename, prompt, quality))
    
    stderr_print(f"Filtered {len(filtered)} entries containing keywords: {keywords}")
    return filtered


def export_to_text(results, output_path="dataset_export.txt"):
    """Export dataset to a text file for inspection."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("Image Dataset Export\n")
            f.write("="*60 + "\n\n")
            
            for i, (filename, prompt, quality) in enumerate(results, 1):
                f.write(f"Entry {i}:\n")
                f.write(f"  File: {Path(filename).name}\n")
                f.write(f"  Full Path: {filename}\n")
                f.write(f"  Quality: {quality}\n")
                f.write(f"  Prompt: {prompt}\n")
                f.write("-" * 60 + "\n")
        
        stderr_print(f"Dataset exported to {output_path}")
        
    except Exception as e:
        stderr_print(f"Error exporting dataset: {e}")


def create_quality_split(results, output_dir="dataset_splits"):
    """Create separate files for good and normal quality images."""
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        good_results = filter_by_quality(results, 'good')
        normal_results = filter_by_quality(results, 'normal')
        
        # Save good quality dataset
        good_path = os.path.join(output_dir, "good_quality.pkl")
        with open(good_path, 'wb') as f:
            dill.dump(good_results, f)
        
        # Save normal quality dataset
        normal_path = os.path.join(output_dir, "normal_quality.pkl")
        with open(normal_path, 'wb') as f:
            dill.dump(normal_results, f)
        
        stderr_print(f"Quality splits saved:")
        stderr_print(f"  Good quality: {good_path} ({len(good_results)} entries)")
        stderr_print(f"  Normal quality: {normal_path} ({len(normal_results)} entries)")
        
    except Exception as e:
        stderr_print(f"Error creating quality splits: {e}")


def find_common_prompt_patterns(results, min_frequency=5):
    """Find common words/patterns in prompts."""
    if not results:
        return
    
    # Collect all words from prompts
    word_counter = Counter()
    for _, prompt, _ in results:
        if prompt.strip():
            # Simple word extraction (split by common delimiters)
            words = prompt.lower().replace(',', ' ').replace('.', ' ').split()
            word_counter.update(words)
    
    stderr_print(f"\n" + "="*60)
    stderr_print(f"COMMON PROMPT WORDS (frequency >= {min_frequency})")
    stderr_print("="*60)
    
    common_words = [(word, count) for word, count in word_counter.most_common() 
                   if count >= min_frequency and len(word) > 2]
    
    for word, count in common_words[:20]:  # Top 20
        stderr_print(f"  {word}: {count}")


def main():
    """Main function demonstrating various usage patterns."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Example usage of data cleaning results")
    parser.add_argument('--input', '-i', default='promptlabels.pkl', 
                       help='Input pickle file (default: promptlabels.pkl)')
    parser.add_argument('--analyze', action='store_true', 
                       help='Analyze the dataset')
    parser.add_argument('--samples', type=int, default=5, 
                       help='Number of sample entries to show')
    parser.add_argument('--export-text', type=str, 
                       help='Export dataset to text file')
    parser.add_argument('--create-splits', action='store_true',
                       help='Create quality-based dataset splits')
    parser.add_argument('--find-patterns', action='store_true',
                       help='Find common prompt patterns')
    parser.add_argument('--filter-quality', choices=['good', 'normal'],
                       help='Filter by quality and show results')
    parser.add_argument('--filter-keywords', nargs='+',
                       help='Filter by keywords in prompts')
    
    args = parser.parse_args()
    
    # Load dataset
    results = load_dataset(args.input)
    if not results:
        sys.exit(1)
    
    # Apply filters if specified
    filtered_results = results
    
    if args.filter_quality:
        filtered_results = filter_by_quality(filtered_results, args.filter_quality)
    
    if args.filter_keywords:
        filtered_results = filter_by_prompt_keywords(filtered_results, args.filter_keywords)
    
    # Execute requested operations
    if args.analyze:
        analyze_dataset(filtered_results)
    
    if args.samples > 0:
        show_sample_entries(filtered_results, args.samples)
    
    if args.export_text:
        export_to_text(filtered_results, args.export_text)
    
    if args.create_splits:
        create_quality_split(results)  # Use original results, not filtered
    
    if args.find_patterns:
        find_common_prompt_patterns(filtered_results)
    
    # If no specific action requested, show basic analysis
    if not any([args.analyze, args.export_text, args.create_splits, args.find_patterns]):
        analyze_dataset(filtered_results)
        show_sample_entries(filtered_results, args.samples)


if __name__ == "__main__":
    main()

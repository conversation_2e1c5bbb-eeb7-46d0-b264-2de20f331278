#!/usr/bin/env python3
"""
Comprehensive Validation Script for Prompt Parsing System

This script validates the enhanced prompt parsing system against real images
from different folders, ensuring >98% parsing accuracy and proper LoRA extraction.

Validation Requirements:
1. Test against at least 5 different images from different folders
2. Verify LoRA extraction accuracy by comparing:
   a. Raw workflow JSON LoRA nodes (containing "safetensors")
   b. Graph connectivity of those nodes
   c. Final extracted prompt data
3. Run validation test on 1000 randomly sampled prompts from full dataset
4. Ensure 100% test validation before full processing

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import dill
import json
import random
import logging
from typing import List, Tuple, Dict, Any, Optional
from collections import Counter, defaultdict
from datetime import datetime
import traceback

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(__file__))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from enhanced_prompt_processor import EnhancedPromptProcessor
from utils import (
    extract_comfyui_workflow_params,
    traverse_workflow_graph,
    extract_connected_lora_nodes,
    image_info
)
from PIL import Image

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PromptParsingValidator:
    """Comprehensive validator for the prompt parsing system."""
    
    def __init__(self):
        self.processor = EnhancedPromptProcessor()
        self.validation_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'parsing_accuracy': 0.0,
            'lora_extraction_accuracy': 0.0,
            'workflow_extraction_accuracy': 0.0,
            'weight_syntax_accuracy': 0.0,
            'technical_param_accuracy': 0.0,
            'error_details': []
        }
    
    def validate_tag_weight_parsing(self) -> Dict[str, Any]:
        """Validate tag weight parsing with comprehensive test cases."""
        logger.info("Starting tag weight parsing validation")
        
        test_cases = [
            # Basic weight syntaxes
            ("{masterpiece}", [("masterpiece", 1.1)]),
            ("{{masterpiece}}", [("masterpiece", 1.2)]),
            ("{{{masterpiece}}}", [("masterpiece", 1.3)]),
            ("[low quality]", [("low_quality", 0.9)]),
            ("[[low quality]]", [("low_quality", 0.8)]),
            ("(detailed:1.2)", [("detailed", 1.2)]),
            ("(beautiful:0.8)", [("beautiful", 0.8)]),
            
            # Multi-delimiter splitting
            ("tag1, tag2; tag3，tag4\ntag5", 
             [("tag1", 1.0), ("tag2", 1.0), ("tag3", 1.0), ("tag4", 1.0), ("tag5", 1.0)]),
            
            # Complex combinations
            ("1girl, {masterpiece}, [blurry], (detailed face:1.3), best quality",
             [("1girl", 1.0), ("masterpiece", 1.1), ("blurry", 0.9), ("detailed_face", 1.3), ("best_quality", 1.0)]),
            
            # Technical parameters
            ("--cfg 7.5, --steps 30, --lora test.safetensors:0.8",
             [("cfg_value", 7.5), ("sampling_steps", 30.0), ("lora_test", 0.8)]),
            
            # Edge cases
            ("", []),  # Empty
            ("   ,  ,  ", []),  # Only delimiters
            ("normal_tag", [("normal_tag", 1.0)]),  # Single tag
        ]
        
        passed = 0
        total = len(test_cases)
        
        for i, (input_prompt, expected_tags) in enumerate(test_cases, 1):
            try:
                # Parse the prompt
                actual_tags = self.processor._parse_prompt_to_tags(input_prompt, "dummy.png")
                
                # Filter out workflow tags for this test
                actual_tags = [(tag, weight) for tag, weight in actual_tags 
                              if not self._is_workflow_tag(tag)]
                
                # Compare results
                if self._compare_tag_lists(actual_tags, expected_tags):
                    passed += 1
                    logger.debug(f"✅ Test {i} passed: {input_prompt}")
                else:
                    error_msg = f"❌ Test {i} failed: {input_prompt}\n  Expected: {expected_tags}\n  Actual: {actual_tags}"
                    logger.warning(error_msg)
                    self.validation_results['error_details'].append(error_msg)
                    
            except Exception as e:
                error_msg = f"💥 Test {i} error: {input_prompt} - {e}"
                logger.error(error_msg)
                self.validation_results['error_details'].append(error_msg)
        
        accuracy = (passed / total) * 100
        self.validation_results['weight_syntax_accuracy'] = accuracy
        
        logger.info(f"Tag weight parsing accuracy: {accuracy:.1f}% ({passed}/{total})")
        return {'accuracy': accuracy, 'passed': passed, 'total': total}
    
    def validate_lora_extraction(self, test_images: List[str]) -> Dict[str, Any]:
        """Validate LoRA extraction against real images with ComfyUI workflows."""
        logger.info(f"Starting LoRA extraction validation on {len(test_images)} images")
        
        passed = 0
        total = 0
        lora_extraction_details = []
        
        for image_path in test_images:
            if not os.path.exists(image_path):
                logger.warning(f"Image not found: {image_path}")
                continue
            
            try:
                total += 1
                
                # Extract workflow from image
                with Image.open(image_path) as img:
                    info = image_info(img, image_path, enhanced_prompts=True)
                
                # Look for workflow data
                workflow_json = None
                for key in ['workflow', 'Workflow', 'ComfyUI_workflow']:
                    if key in info and info[key]:
                        workflow_json = info[key]
                        break
                
                if not workflow_json:
                    logger.debug(f"No workflow found in {image_path}")
                    continue
                
                # Parse workflow
                workflow = json.loads(workflow_json)
                
                # Method 1: Find all LoRA nodes in raw workflow
                raw_lora_nodes = self._find_raw_lora_nodes(workflow)
                
                # Method 2: Use graph traversal to find connected LoRAs
                try:
                    connected_nodes = traverse_workflow_graph(workflow)
                    connected_loras = extract_connected_lora_nodes(workflow, connected_nodes)
                except Exception as e:
                    logger.warning(f"Graph traversal failed for {image_path}: {e}")
                    connected_loras = []
                
                # Method 3: Use enhanced processor extraction
                processor_loras = self.processor._extract_workflow_parameters(image_path)
                processor_lora_tags = [tag for tag, weight in processor_loras if tag.startswith('lora_')]
                
                # Validate extraction
                validation_passed = self._validate_lora_consistency(
                    raw_lora_nodes, connected_loras, processor_lora_tags
                )
                
                if validation_passed:
                    passed += 1
                
                lora_extraction_details.append({
                    'image': os.path.basename(image_path),
                    'raw_loras': len(raw_lora_nodes),
                    'connected_loras': len(connected_loras),
                    'processor_loras': len(processor_lora_tags),
                    'validation_passed': validation_passed
                })
                
                logger.debug(f"LoRA extraction for {os.path.basename(image_path)}: "
                           f"Raw={len(raw_lora_nodes)}, Connected={len(connected_loras)}, "
                           f"Processor={len(processor_lora_tags)}, Passed={validation_passed}")
                
            except Exception as e:
                error_msg = f"Error processing {image_path}: {e}"
                logger.error(error_msg)
                self.validation_results['error_details'].append(error_msg)
        
        accuracy = (passed / total) * 100 if total > 0 else 0
        self.validation_results['lora_extraction_accuracy'] = accuracy
        
        logger.info(f"LoRA extraction accuracy: {accuracy:.1f}% ({passed}/{total})")
        return {
            'accuracy': accuracy, 
            'passed': passed, 
            'total': total,
            'details': lora_extraction_details
        }
    
    def validate_dataset_sample(self, dataset_file: str, sample_size: int = 1000) -> Dict[str, Any]:
        """Validate parsing on a random sample from the full dataset."""
        logger.info(f"Starting dataset sample validation with {sample_size} samples")
        
        try:
            # Load dataset
            with open(dataset_file, 'rb') as f:
                full_dataset = dill.load(f)
            
            # Random sample
            if len(full_dataset) > sample_size:
                sample_dataset = random.sample(full_dataset, sample_size)
            else:
                sample_dataset = full_dataset
            
            # Process sample
            passed = 0
            total = len(sample_dataset)
            processing_errors = 0
            
            for i, entry in enumerate(sample_dataset):
                try:
                    # Process entry
                    result = self.processor.process_single_entry(entry)
                    filename, tags, goodness = result
                    
                    # Basic validation
                    if (isinstance(filename, str) and 
                        isinstance(tags, list) and 
                        isinstance(goodness, (int, float)) and
                        len(tags) > 0):
                        passed += 1
                    
                    # Log progress
                    if (i + 1) % 100 == 0:
                        logger.info(f"Processed {i + 1}/{total} samples")
                        
                except Exception as e:
                    processing_errors += 1
                    logger.warning(f"Processing error for entry {i}: {e}")
            
            accuracy = (passed / total) * 100
            self.validation_results['parsing_accuracy'] = accuracy
            
            logger.info(f"Dataset sample validation: {accuracy:.1f}% ({passed}/{total})")
            logger.info(f"Processing errors: {processing_errors}")
            
            return {
                'accuracy': accuracy,
                'passed': passed,
                'total': total,
                'processing_errors': processing_errors
            }
            
        except Exception as e:
            error_msg = f"Dataset validation failed: {e}"
            logger.error(error_msg)
            self.validation_results['error_details'].append(error_msg)
            return {'accuracy': 0.0, 'passed': 0, 'total': 0, 'processing_errors': 1}
    
    def _find_raw_lora_nodes(self, workflow: Dict) -> List[str]:
        """Find all LoRA nodes in raw workflow JSON."""
        lora_nodes = []
        
        for node_id, node in workflow.items():
            if isinstance(node, dict):
                class_type = node.get('class_type', '')
                if 'lora' in class_type.lower():
                    inputs = node.get('inputs', {})
                    text = inputs.get('text', '')
                    if 'safetensors' in text.lower():
                        lora_nodes.append(text)
        
        return lora_nodes
    
    def _validate_lora_consistency(self, raw_loras: List[str], connected_loras: List[str], 
                                  processor_loras: List[str]) -> bool:
        """Validate consistency between different LoRA extraction methods."""
        # Basic consistency check: processor should not extract more LoRAs than connected
        if len(processor_loras) > len(connected_loras) and len(connected_loras) > 0:
            return False
        
        # If no LoRAs found by any method, that's consistent
        if len(raw_loras) == 0 and len(connected_loras) == 0 and len(processor_loras) == 0:
            return True
        
        # If raw LoRAs exist but connected is empty, processor should also be empty
        if len(raw_loras) > 0 and len(connected_loras) == 0:
            return len(processor_loras) == 0
        
        return True
    
    def _is_workflow_tag(self, tag: str) -> bool:
        """Check if tag is a workflow-related technical parameter."""
        workflow_prefixes = ['lora_', 'cfg_', 'sampling_', 'sampler_name_', 'scheduler_', 'model_']
        workflow_tags = ['width', 'height', 'seed']
        
        return any(tag.startswith(prefix) for prefix in workflow_prefixes) or tag in workflow_tags
    
    def _compare_tag_lists(self, actual: List[Tuple[str, float]], expected: List[Tuple[str, float]]) -> bool:
        """Compare two tag lists with normalization tolerance."""
        if len(actual) != len(expected):
            return False
        
        # Convert to normalized dictionaries
        actual_dict = {self._normalize_tag(tag): weight for tag, weight in actual}
        expected_dict = {self._normalize_tag(tag): weight for tag, weight in expected}
        
        # Compare each expected tag
        for tag, weight in expected_dict.items():
            if tag not in actual_dict:
                return False
            if abs(actual_dict[tag] - weight) > 0.01:  # Allow small floating point differences
                return False
        
        return True
    
    def _normalize_tag(self, tag: str) -> str:
        """Normalize tag for comparison."""
        return tag.lower().replace(' ', '_').replace('-', '_')
    
    def run_comprehensive_validation(self, dataset_file: str, test_images: List[str] = None) -> Dict[str, Any]:
        """Run all validation tests and generate comprehensive report."""
        logger.info("Starting comprehensive validation of prompt parsing system")
        
        # Test 1: Tag weight parsing
        weight_results = self.validate_tag_weight_parsing()
        
        # Test 2: LoRA extraction (if test images provided)
        if test_images:
            lora_results = self.validate_lora_extraction(test_images)
        else:
            lora_results = {'accuracy': 0.0, 'passed': 0, 'total': 0}
        
        # Test 3: Dataset sample validation
        if os.path.exists(dataset_file):
            dataset_results = self.validate_dataset_sample(dataset_file)
        else:
            logger.warning(f"Dataset file not found: {dataset_file}")
            dataset_results = {'accuracy': 0.0, 'passed': 0, 'total': 0}
        
        # Calculate overall results
        self.validation_results.update({
            'total_tests': weight_results['total'] + lora_results['total'] + dataset_results['total'],
            'passed_tests': weight_results['passed'] + lora_results['passed'] + dataset_results['passed'],
            'weight_syntax_results': weight_results,
            'lora_extraction_results': lora_results,
            'dataset_sample_results': dataset_results
        })
        
        if self.validation_results['total_tests'] > 0:
            overall_accuracy = (self.validation_results['passed_tests'] / 
                              self.validation_results['total_tests']) * 100
            self.validation_results['overall_accuracy'] = overall_accuracy
        else:
            self.validation_results['overall_accuracy'] = 0.0
        
        return self.validation_results


def find_test_images(base_dirs: List[str], max_images: int = 10) -> List[str]:
    """Find test images from different directories."""
    test_images = []

    for base_dir in base_dirs:
        if not os.path.exists(base_dir):
            continue

        # Find image files
        for root, dirs, files in os.walk(base_dir):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    test_images.append(os.path.join(root, file))
                    if len(test_images) >= max_images:
                        return test_images

    return test_images


def generate_validation_report(results: Dict[str, Any]) -> str:
    """Generate comprehensive validation report."""
    report = []
    report.append("=" * 80)
    report.append("PROMPT PARSING SYSTEM - COMPREHENSIVE VALIDATION REPORT")
    report.append("=" * 80)
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")

    # Overall Results
    report.append("📊 OVERALL RESULTS")
    report.append("-" * 40)
    report.append(f"Total Tests: {results.get('total_tests', 0)}")
    report.append(f"Passed Tests: {results.get('passed_tests', 0)}")
    report.append(f"Failed Tests: {results.get('failed_tests', 0)}")
    report.append(f"Overall Accuracy: {results.get('overall_accuracy', 0):.1f}%")
    report.append("")

    # Weight Syntax Validation
    weight_results = results.get('weight_syntax_results', {})
    report.append("🏷️  TAG WEIGHT PARSING VALIDATION")
    report.append("-" * 40)
    report.append(f"Accuracy: {weight_results.get('accuracy', 0):.1f}%")
    report.append(f"Tests Passed: {weight_results.get('passed', 0)}/{weight_results.get('total', 0)}")
    report.append("")

    # LoRA Extraction Validation
    lora_results = results.get('lora_extraction_results', {})
    report.append("🔗 LORA EXTRACTION VALIDATION")
    report.append("-" * 40)
    report.append(f"Accuracy: {lora_results.get('accuracy', 0):.1f}%")
    report.append(f"Tests Passed: {lora_results.get('passed', 0)}/{lora_results.get('total', 0)}")

    if 'details' in lora_results:
        report.append("LoRA Extraction Details:")
        for detail in lora_results['details'][:5]:  # Show first 5
            report.append(f"  {detail['image']}: Raw={detail['raw_loras']}, "
                         f"Connected={detail['connected_loras']}, "
                         f"Processor={detail['processor_loras']}, "
                         f"✅" if detail['validation_passed'] else "❌")
    report.append("")

    # Dataset Sample Validation
    dataset_results = results.get('dataset_sample_results', {})
    report.append("📁 DATASET SAMPLE VALIDATION")
    report.append("-" * 40)
    report.append(f"Accuracy: {dataset_results.get('accuracy', 0):.1f}%")
    report.append(f"Tests Passed: {dataset_results.get('passed', 0)}/{dataset_results.get('total', 0)}")
    report.append(f"Processing Errors: {dataset_results.get('processing_errors', 0)}")
    report.append("")

    # Requirements Check
    report.append("✅ REQUIREMENTS VALIDATION")
    report.append("-" * 40)

    overall_accuracy = results.get('overall_accuracy', 0)
    weight_accuracy = weight_results.get('accuracy', 0)

    if overall_accuracy >= 98.0:
        report.append("✅ Overall parsing accuracy ≥98%: PASSED")
    else:
        report.append(f"❌ Overall parsing accuracy ≥98%: FAILED ({overall_accuracy:.1f}%)")

    if weight_accuracy >= 98.0:
        report.append("✅ Weight syntax parsing ≥98%: PASSED")
    else:
        report.append(f"❌ Weight syntax parsing ≥98%: FAILED ({weight_accuracy:.1f}%)")

    if len(results.get('error_details', [])) == 0:
        report.append("✅ No critical errors: PASSED")
    else:
        report.append(f"⚠️  {len(results.get('error_details', []))} errors found")

    report.append("")

    # Error Details
    if results.get('error_details'):
        report.append("❌ ERROR DETAILS")
        report.append("-" * 40)
        for i, error in enumerate(results['error_details'][:10], 1):  # Show first 10 errors
            report.append(f"{i}. {error}")
        if len(results['error_details']) > 10:
            report.append(f"... and {len(results['error_details']) - 10} more errors")
        report.append("")

    # Final Verdict
    report.append("🎯 FINAL VERDICT")
    report.append("-" * 40)

    if (overall_accuracy >= 98.0 and
        weight_accuracy >= 98.0 and
        len(results.get('error_details', [])) < 5):
        report.append("🎉 VALIDATION PASSED!")
        report.append("✅ System is ready for full dataset processing")
    else:
        report.append("⚠️  VALIDATION FAILED!")
        report.append("❌ System needs improvements before full processing")

    report.append("=" * 80)

    return "\n".join(report)


def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(description='Validate Prompt Parsing System')
    parser.add_argument('--dataset', required=True, help='Path to dataset pickle file')
    parser.add_argument('--test-dirs', nargs='+', help='Directories to search for test images')
    parser.add_argument('--sample-size', type=int, default=1000, help='Dataset sample size for validation')
    parser.add_argument('--output-report', help='Path to save validation report')

    args = parser.parse_args()

    # Find test images
    test_images = []
    if args.test_dirs:
        test_images = find_test_images(args.test_dirs, max_images=10)
        logger.info(f"Found {len(test_images)} test images")

    # Create validator
    validator = PromptParsingValidator()

    # Run comprehensive validation
    try:
        results = validator.run_comprehensive_validation(args.dataset, test_images)

        # Generate report
        report = generate_validation_report(results)

        # Print report
        print(report)

        # Save report if requested
        if args.output_report:
            with open(args.output_report, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"Validation report saved to: {args.output_report}")

        # Return appropriate exit code
        overall_accuracy = results.get('overall_accuracy', 0)
        if overall_accuracy >= 98.0:
            logger.info("🎉 Validation passed! System ready for production.")
            return 0
        else:
            logger.warning("⚠️  Validation failed. System needs improvements.")
            return 1

    except Exception as e:
        logger.error(f"Validation failed with error: {e}")
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())

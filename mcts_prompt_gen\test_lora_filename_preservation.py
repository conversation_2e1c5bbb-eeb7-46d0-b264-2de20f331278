#!/usr/bin/env python3
"""
Test script to verify that LoRA filename preservation fixes are working correctly.

This script tests:
1. LoRA extraction from different ComfyUI workflow formats
2. LoRA filename preservation without corruption
3. Regular tag normalization (should still work)
4. Integration with existing parsing pipeline
"""

import os
import sys
import json
from pathlib import Path

# Add src directory to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Import the fixed parsers
from corrected_prompt_parser import CorrectedPromptParser, parse_prompt_with_weights_corrected
from fixed_prompt_parser import FixedPromptParser, parse_prompt_with_weights_fixed


def test_lora_filename_preservation():
    """Test that LoRA filenames are preserved exactly without normalization."""
    print("TESTING LORA FILENAME PRESERVATION")
    print("=" * 60)
    
    # Test cases with complex LoRA filenames that should be preserved
    test_cases = [
        {
            "name": "Complex LoRA filenames",
            "prompt": "1girl, masterpiece --lora a31_style_koni-000010.safetensors:0.8 --lora <PERSON>ki_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9",
            "expected_loras": [
                "lora_a31_style_koni-000010",
                "lora_Hoseki_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1"
            ]
        },
        {
            "name": "LoRA with spaces and special chars",
            "prompt": "beautiful art --lora style-model_v2.safetensors:1.2 --lora character-lora_final.safetensors:0.7",
            "expected_loras": [
                "lora_style-model_v2",
                "lora_character-lora_final"
            ]
        },
        {
            "name": "Mixed case LoRA names",
            "prompt": "portrait --lora MyCustomStyle.safetensors:0.6 --lora ANIME_CHARACTER_V3.safetensors:1.1",
            "expected_loras": [
                "lora_MyCustomStyle",
                "lora_ANIME_CHARACTER_V3"
            ]
        }
    ]
    
    # Test both parsers
    parsers = [
        ("CorrectedPromptParser", CorrectedPromptParser()),
        ("FixedPromptParser", FixedPromptParser())
    ]
    
    for parser_name, parser in parsers:
        print(f"\n{'-'*40}")
        print(f"Testing {parser_name}")
        print(f"{'-'*40}")
        
        for test_case in test_cases:
            print(f"\nTest: {test_case['name']}")
            print(f"Prompt: {test_case['prompt']}")
            
            # Parse the prompt
            if parser_name == "CorrectedPromptParser":
                result = parser.parse_prompt_corrected(test_case['prompt'])
            else:
                result = parser.parse_prompt_with_fixed_logic(test_case['prompt'])
            
            # Extract LoRA tags from result
            found_loras = [tag for tag, weight in result if tag.startswith('lora_')]
            
            print(f"Expected LoRAs: {test_case['expected_loras']}")
            print(f"Found LoRAs: {found_loras}")
            
            # Check if all expected LoRAs are found with exact filenames
            success = True
            for expected_lora in test_case['expected_loras']:
                if expected_lora not in found_loras:
                    print(f"❌ MISSING: {expected_lora}")
                    success = False
                else:
                    print(f"✅ FOUND: {expected_lora}")
            
            # Check for unexpected LoRAs
            unexpected = set(found_loras) - set(test_case['expected_loras'])
            if unexpected:
                print(f"⚠️  UNEXPECTED: {unexpected}")
                success = False
            
            if success:
                print("✅ SUCCESS: All LoRA filenames preserved correctly")
            else:
                print("❌ FAILURE: LoRA filename preservation failed")


def test_regular_tag_normalization():
    """Test that regular tags are still normalized correctly."""
    print("\n\nTESTING REGULAR TAG NORMALIZATION")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "Space/underscore normalization",
            "prompt": "floral_print, floral print, very_aesthetic, very aesthetic",
            "expected_normalized": ["floral_print", "very_aesthetic"]
        },
        {
            "name": "Weight syntax parsing",
            "prompt": "{masterpiece}, [low quality], (detailed:1.2)",
            "expected_tags": [
                ("masterpiece", 1.1),
                ("low_quality", 0.9),
                ("detailed", 1.2)
            ]
        }
    ]
    
    parser = CorrectedPromptParser()
    
    for test_case in test_cases:
        print(f"\nTest: {test_case['name']}")
        print(f"Prompt: {test_case['prompt']}")
        
        result = parser.parse_prompt_corrected(test_case['prompt'])
        
        if 'expected_normalized' in test_case:
            # Check normalization
            found_tags = [tag for tag, weight in result if not tag.startswith('lora_')]
            print(f"Expected normalized: {test_case['expected_normalized']}")
            print(f"Found tags: {found_tags}")
            
            # Check if normalization worked (should have deduplicated)
            if len(found_tags) == len(test_case['expected_normalized']):
                print("✅ SUCCESS: Tag normalization working correctly")
            else:
                print("❌ FAILURE: Tag normalization failed")
        
        elif 'expected_tags' in test_case:
            # Check weight parsing
            print(f"Expected: {test_case['expected_tags']}")
            print(f"Found: {result}")
            
            success = True
            for expected_tag, expected_weight in test_case['expected_tags']:
                found = False
                for tag, weight in result:
                    if tag == expected_tag and abs(weight - expected_weight) < 0.01:
                        found = True
                        break
                if not found:
                    print(f"❌ MISSING: {expected_tag}:{expected_weight}")
                    success = False
            
            if success:
                print("✅ SUCCESS: Weight parsing working correctly")
            else:
                print("❌ FAILURE: Weight parsing failed")


def test_integration_with_workflow_extraction():
    """Test integration with actual ComfyUI workflow extraction."""
    print("\n\nTESTING INTEGRATION WITH WORKFLOW EXTRACTION")
    print("=" * 60)
    
    # Test with one of the actual failing images if available
    test_image = r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png"
    
    if os.path.exists(test_image):
        try:
            from PIL import Image
            from utils import image_info, extract_comfyui_workflow_params
            
            with Image.open(test_image) as img:
                print(f"Testing with: {test_image}")
                
                # Extract image info with enhanced prompts
                info = image_info(img, test_image, enhanced_prompts=True)
                
                if 'prompt' in info:
                    prompt = info['prompt']
                    print(f"Extracted prompt length: {len(prompt)}")
                    
                    # Test parsing with corrected parser
                    parser = CorrectedPromptParser()
                    result = parser.parse_prompt_corrected(prompt)
                    
                    # Look for LoRA tags
                    lora_tags = [(tag, weight) for tag, weight in result if tag.startswith('lora_')]
                    
                    print(f"Found LoRA tags: {len(lora_tags)}")
                    for tag, weight in lora_tags:
                        print(f"  - {tag}: {weight}")
                    
                    # Expected LoRA for this image
                    expected = "lora_luce2_Noob75XL"
                    found_expected = any(tag == expected for tag, weight in lora_tags)
                    
                    if found_expected:
                        print(f"✅ SUCCESS: Found expected LoRA '{expected}' with preserved filename")
                    else:
                        print(f"❌ FAILURE: Expected LoRA '{expected}' not found or filename corrupted")
                        print(f"   Available LoRAs: {[tag for tag, weight in lora_tags]}")
                
                else:
                    print("❌ No prompt found in image info")
                    
        except Exception as e:
            print(f"❌ Error testing with real image: {e}")
    else:
        print(f"⚠️  Test image not found: {test_image}")
        print("Skipping integration test")


def main():
    """Run all tests."""
    print("LORA FILENAME PRESERVATION TEST SUITE")
    print("=" * 80)
    
    test_lora_filename_preservation()
    test_regular_tag_normalization()
    test_integration_with_workflow_extraction()
    
    print("\n" + "=" * 80)
    print("TEST SUITE COMPLETE")
    print("=" * 80)


if __name__ == "__main__":
    main()

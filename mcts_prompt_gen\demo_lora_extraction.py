#!/usr/bin/env python3
"""
Demonstration of Graph-Based vs Naive LoRA Extraction

This script demonstrates the difference between the old naive LoRA extraction
and the new graph-based approach using a sample workflow with both connected
and disconnected LoRA nodes.
"""

import json
import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import extract_comfyui_workflow_params, traverse_workflow_graph, extract_connected_lora_nodes


def naive_lora_extraction(workflow):
    """
    Simulate the old naive LoRA extraction approach.
    This extracts ALL LoRA nodes regardless of connection status.
    """
    loras = []
    for node_id, node in workflow.items():
        if node.get('class_type') in ['LoraTagLoader', 'LoraLoader']:
            inputs = node.get('inputs', {})
            lora_text = inputs.get('text', '')
            if lora_text:
                import re
                lora_matches = re.findall(r'<lora:([^>]+)>', lora_text)
                for match in lora_matches:
                    if ':' in match:
                        parts = match.split(':')
                        if len(parts) >= 2:
                            filename = parts[0].replace('\\', '/').split('/')[-1]
                            weight = parts[1] if len(parts) > 1 else '1.0'
                            loras.append(f"{filename}:{weight}")
                        else:
                            loras.append(match)
                    else:
                        loras.append(match)
    return loras


def create_demo_workflow():
    """Create a demonstration workflow with both connected and disconnected LoRA nodes."""
    return {
        # CONNECTED EXECUTION PATH
        "9": {  # SaveImage (output)
            "inputs": {"filename_prefix": "demo", "images": ["28", 0]},
            "class_type": "SaveImage",
            "_meta": {"title": "Save Image"}
        },
        "28": {  # VAEDecode
            "inputs": {"samples": ["12", 0], "vae": ["4", 2]},
            "class_type": "VAEDecodeTiled",
            "_meta": {"title": "VAE Decode"}
        },
        "12": {  # KSampler
            "inputs": {
                "seed": 12345, "steps": 30, "cfg": 7.5,
                "model": ["41", 0], "positive": ["6", 0], "negative": ["7", 0],
                "latent_image": ["30", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "41": {  # CONNECTED LoRA node
            "inputs": {
                "text": "<lora:style_anime.safetensors:1.0>, <lora:character_girl.safetensors:0.8>",
                "model": ["4", 0], "clip": ["4", 1]
            },
            "class_type": "LoraTagLoader",
            "_meta": {"title": "Connected LoRA"}
        },
        "4": {  # CheckpointLoader
            "inputs": {"ckpt_name": "base_model.safetensors"},
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "6": {  # Positive prompt
            "inputs": {"text": "1girl, masterpiece", "clip": ["41", 1]},
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Positive Prompt"}
        },
        "7": {  # Negative prompt
            "inputs": {"text": "low quality", "clip": ["41", 1]},
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Negative Prompt"}
        },
        "30": {  # EmptyLatentImage
            "inputs": {"width": 1024, "height": 1024, "batch_size": 1},
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Empty Latent"}
        },
        
        # DISCONNECTED NODES (should be ignored)
        "99": {  # DISCONNECTED LoRA node
            "inputs": {
                "text": "<lora:unused_style.safetensors:1.2>, <lora:experimental.safetensors:0.5>",
                "model": ["100", 0], "clip": ["100", 1]
            },
            "class_type": "LoraTagLoader",
            "_meta": {"title": "Disconnected LoRA"}
        },
        "100": {  # Disconnected checkpoint
            "inputs": {"ckpt_name": "unused_model.safetensors"},
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Unused Checkpoint"}
        },
        "101": {  # Another disconnected LoRA
            "inputs": {
                "text": "<lora:orphaned_lora.safetensors:0.9>",
                "model": ["100", 0], "clip": ["100", 1]
            },
            "class_type": "LoraLoader",
            "_meta": {"title": "Orphaned LoRA"}
        }
    }


def main():
    """Demonstrate the difference between naive and graph-based LoRA extraction."""
    print("=" * 70)
    print("LoRA Extraction Comparison: Naive vs Graph-Based")
    print("=" * 70)
    
    # Create demo workflow
    workflow = create_demo_workflow()
    workflow_json = json.dumps(workflow, indent=2)
    
    print("Demo Workflow Structure:")
    print("-" * 30)
    print("CONNECTED PATH:")
    print("  SaveImage(9) → VAEDecode(28) → KSampler(12) → LoraTagLoader(41) → Checkpoint(4)")
    print("  LoRAs in connected node 41: style_anime.safetensors:1.0, character_girl.safetensors:0.8")
    print()
    print("DISCONNECTED NODES:")
    print("  LoraTagLoader(99) → Checkpoint(100) [unused_style.safetensors:1.2, experimental.safetensors:0.5]")
    print("  LoraLoader(101) → Checkpoint(100) [orphaned_lora.safetensors:0.9]")
    print()
    
    # Method 1: Naive extraction (old approach)
    print("1. NAIVE EXTRACTION (Old Approach)")
    print("-" * 40)
    naive_loras = naive_lora_extraction(workflow)
    print(f"LoRAs found: {len(naive_loras)}")
    for lora in naive_loras:
        print(f"  - {lora}")
    print(f"❌ Problem: Includes {len(naive_loras) - 2} disconnected LoRAs that don't affect the image!")
    print()
    
    # Method 2: Graph-based extraction (new approach)
    print("2. GRAPH-BASED EXTRACTION (New Approach)")
    print("-" * 45)
    
    # Step 2a: Find connected nodes
    connected_nodes = traverse_workflow_graph(workflow)
    print(f"Connected nodes found: {sorted(connected_nodes)}")
    
    # Step 2b: Extract only connected LoRAs
    connected_loras = extract_connected_lora_nodes(workflow, connected_nodes)
    print(f"Connected LoRAs: {len(connected_loras)}")
    for lora in connected_loras:
        print(f"  - {lora}")
    print(f"✅ Correct: Only includes LoRAs that actually affect the final image!")
    print()
    
    # Method 3: Full workflow extraction (integrated approach)
    print("3. FULL WORKFLOW EXTRACTION (Integrated)")
    print("-" * 45)
    params = extract_comfyui_workflow_params(workflow_json)
    extracted_loras = params.get('loras', [])
    print(f"Extracted LoRAs: {len(extracted_loras)}")
    for lora in extracted_loras:
        print(f"  - {lora}")
    print()
    
    # Summary comparison
    print("=" * 70)
    print("COMPARISON SUMMARY")
    print("=" * 70)
    print(f"Naive extraction:      {len(naive_loras)} LoRAs (includes disconnected)")
    print(f"Graph-based extraction: {len(connected_loras)} LoRAs (connected only)")
    print(f"Reduction in noise:     {len(naive_loras) - len(connected_loras)} irrelevant LoRAs removed")
    print(f"Accuracy improvement:   {(1 - len(connected_loras)/len(naive_loras))*100:.1f}% noise reduction")
    print()
    
    # Impact on ML training
    print("IMPACT ON ML TRAINING:")
    print("-" * 25)
    print("✅ Before: ML models trained on incorrect LoRA associations")
    print("✅ After:  ML models trained only on LoRAs that actually affect image quality")
    print("✅ Result: Better prompt quality prediction and generation")
    print()
    
    # Verification
    if connected_loras == extracted_loras:
        print("🎉 SUCCESS: Graph-based extraction working correctly!")
    else:
        print("❌ ERROR: Mismatch between graph-based and integrated extraction")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
Comprehensive Training Script for Goodness Prediction Models

This script trains and evaluates both LightGBM and ResNet models for predicting
prompt goodness scores from structured prompt data.

Features:
- Modular training pipeline
- Data augmentation for robustness
- Comprehensive evaluation with NDCG and precision@k
- Model comparison and selection
- Timestamped results organization
- Checkpointing and model persistence

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional
import dill
from pathlib import Path
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# Import our models
from goodness_predictor_models import LightGBMGoodnessPredictorModel
from resnet_goodness_model import ResNetGoodnessPredictorModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataAugmentor:
    """
    Data augmentation for improving model robustness.
    
    Generates variants of prompts by selectively removing technical parameters
    while preserving LoRA tags and content tags.
    """
    
    def __init__(self, augmentation_factor: int = 3):
        """
        Initialize augmentor.
        
        Args:
            augmentation_factor: Number of augmented versions per original prompt
        """
        self.augmentation_factor = augmentation_factor
        self.technical_params = {'cfg', 'steps', 'width', 'height', 'sampler', 'scheduler', 'seed'}
    
    def augment_dataset(self, dataset: List[Tuple[str, List[Tuple[str, float]], float]]) -> List[Tuple[str, List[Tuple[str, float]], float]]:
        """
        Augment dataset by creating variants with different technical parameter combinations.
        
        Args:
            dataset: Original dataset
            
        Returns:
            Augmented dataset including original and variant prompts
        """
        logger.info(f"Augmenting dataset with factor {self.augmentation_factor}...")
        
        augmented_data = []
        
        for filename, tags, goodness in dataset:
            # Add original prompt
            augmented_data.append((filename, tags, goodness))
            
            # Create augmented variants
            for i in range(self.augmentation_factor):
                augmented_tags = self._create_variant(tags, i)
                augmented_filename = f"{filename}_aug_{i}"
                augmented_data.append((augmented_filename, augmented_tags, goodness))
        
        logger.info(f"Dataset augmented: {len(dataset)} -> {len(augmented_data)} entries")
        return augmented_data
    
    def _create_variant(self, tags: List[Tuple[str, float]], variant_id: int) -> List[Tuple[str, float]]:
        """Create a variant by selectively removing technical parameters."""
        np.random.seed(variant_id)  # Deterministic variants
        
        variant_tags = []
        
        for tag, weight in tags:
            # Always preserve LoRA tags and content tags
            if self._is_lora_tag(tag) or not self._is_technical_param(tag):
                variant_tags.append((tag, weight))
            else:
                # Randomly keep or remove technical parameters
                if np.random.random() > 0.3:  # 70% chance to keep
                    variant_tags.append((tag, weight))
        
        return variant_tags
    
    def _is_technical_param(self, tag: str) -> bool:
        """Check if tag is a technical parameter."""
        return tag.lower() in self.technical_params
    
    def _is_lora_tag(self, tag: str) -> bool:
        """Check if tag is a LoRA model reference."""
        return (tag.startswith('<lora:') and tag.endswith('>')) or tag.startswith('lora_')


class GoodnessModelTrainer:
    """
    Comprehensive trainer for goodness prediction models.
    """
    
    def __init__(self, dataset_path: str, output_dir: str = "goodness_model_training"):
        """
        Initialize trainer.
        
        Args:
            dataset_path: Path to structured prompt dataset
            output_dir: Output directory for results
        """
        self.dataset_path = dataset_path
        self.output_dir = output_dir
        self.dataset = None
        self.results = {}
        
        # Create timestamped output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = f"{output_dir}_{timestamp}"
        os.makedirs(self.run_dir, exist_ok=True)
        
        logger.info(f"Training results will be saved to: {self.run_dir}")
    
    def load_dataset(self) -> bool:
        """Load and validate the dataset."""
        try:
            logger.info(f"Loading dataset from {self.dataset_path}")
            with open(self.dataset_path, 'rb') as f:
                self.dataset = dill.load(f)
            
            logger.info(f"Successfully loaded {len(self.dataset)} entries")
            
            # Validate structure
            if not self.dataset or not isinstance(self.dataset[0], tuple) or len(self.dataset[0]) != 3:
                raise ValueError("Invalid dataset structure")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            return False
    
    def prepare_data(self, test_size: float = 0.15, val_size: float = 0.15, 
                    augment_data: bool = True, random_state: int = 42) -> Tuple[List, List, List, List, List, List]:
        """
        Prepare train/validation/test splits with optional augmentation.
        
        Args:
            test_size: Fraction for test set
            val_size: Fraction for validation set
            augment_data: Whether to augment training data
            random_state: Random seed
            
        Returns:
            X_train, X_val, X_test, y_train, y_val, y_test
        """
        logger.info("Preparing data splits...")
        
        # Extract features and labels
        X = [(filename, tags, goodness) for filename, tags, goodness in self.dataset]
        y = [goodness for filename, tags, goodness in self.dataset]
        
        # First split: separate test set
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )
        
        # Second split: separate train and validation
        val_size_adjusted = val_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, random_state=random_state, stratify=y_temp
        )
        
        logger.info(f"Data splits: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")
        
        # Data augmentation for training set only
        if augment_data:
            augmentor = DataAugmentor(augmentation_factor=3)
            X_train_augmented = augmentor.augment_dataset(X_train)
            y_train_augmented = [goodness for _, _, goodness in X_train_augmented]
            
            logger.info(f"Training data augmented: {len(X_train)} -> {len(X_train_augmented)}")
            X_train, y_train = X_train_augmented, y_train_augmented
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def train_lightgbm_model(self, X_train, y_train, X_val, y_val, 
                           optimize_hyperparams: bool = True) -> LightGBMGoodnessPredictorModel:
        """Train LightGBM model."""
        logger.info("Training LightGBM model...")
        
        model = LightGBMGoodnessPredictorModel(max_features=5000, random_state=42)
        
        training_history = model.train_model(
            X_train, y_train, X_val, y_val,
            optimize_hyperparams=optimize_hyperparams,
            n_trials=50
        )
        
        # Save model
        model_path = os.path.join(self.run_dir, "lightgbm_model.pkl")
        model.save_model(model_path)
        
        # Save training history
        history_path = os.path.join(self.run_dir, "lightgbm_training_history.json")
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)
        
        logger.info("LightGBM training complete")
        return model
    
    def train_resnet_model(self, X_train, y_train, X_val, y_val, 
                          epochs: int = 50) -> ResNetGoodnessPredictorModel:
        """Train ResNet model."""
        logger.info("Training ResNet model...")
        
        model = ResNetGoodnessPredictorModel(
            vocab_size=10000,
            embedding_dim=128,
            hidden_dim=256,
            num_blocks=3,
            max_length=100,
            dropout=0.1
        )
        
        training_history = model.train_model(
            X_train, y_train, X_val, y_val,
            batch_size=32,
            epochs=epochs,
            learning_rate=0.001,
            patience=10
        )
        
        # Save model
        model_path = os.path.join(self.run_dir, "resnet_model.pkl")
        model.save_model(model_path)
        
        # Save training history
        history_path = os.path.join(self.run_dir, "resnet_training_history.json")
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)
        
        logger.info("ResNet training complete")
        return model
    
    def evaluate_models(self, models: Dict[str, Any], X_test, y_test) -> Dict[str, Dict[str, float]]:
        """Evaluate all models on test set."""
        logger.info("Evaluating models on test set...")
        
        evaluation_results = {}
        
        for model_name, model in models.items():
            logger.info(f"Evaluating {model_name}...")
            metrics = model.evaluate(X_test, y_test)
            evaluation_results[model_name] = metrics
            
            logger.info(f"{model_name} Results:")
            for metric, value in metrics.items():
                logger.info(f"  {metric}: {value:.4f}")
        
        return evaluation_results
    
    def create_comparison_visualizations(self, evaluation_results: Dict[str, Dict[str, float]]):
        """Create comprehensive comparison visualizations."""
        logger.info("Creating comparison visualizations...")
        
        # Prepare data for plotting
        models = list(evaluation_results.keys())
        metrics = ['rmse', 'mae', 'ndcg', 'precision_at_10', 'precision_at_20', 'auc', 'score_difference']
        
        # Create comparison dashboard
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        fig.suptitle('Model Performance Comparison', fontsize=16, fontweight='bold')
        
        for i, metric in enumerate(metrics):
            if i >= 8:  # Only plot first 8 metrics
                break
                
            row, col = i // 4, i % 4
            ax = axes[row, col]
            
            values = [evaluation_results[model].get(metric, 0) for model in models]
            colors = ['blue', 'red', 'green', 'orange'][:len(models)]
            
            bars = ax.bar(models, values, color=colors[:len(models)])
            ax.set_title(f'{metric.upper()}')
            ax.set_ylabel('Score')
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.3f}', ha='center', va='bottom')
        
        # Remove empty subplots
        for i in range(len(metrics), 8):
            row, col = i // 4, i % 4
            fig.delaxes(axes[row, col])
        
        plt.tight_layout()
        
        # Save visualization
        viz_path = os.path.join(self.run_dir, "model_comparison.png")
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Comparison visualization saved to {viz_path}")
    
    def save_results_summary(self, evaluation_results: Dict[str, Dict[str, float]]):
        """Save comprehensive results summary."""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'dataset_path': self.dataset_path,
            'dataset_size': len(self.dataset),
            'evaluation_results': evaluation_results,
            'best_model': self._select_best_model(evaluation_results)
        }
        
        # Save JSON summary
        summary_path = os.path.join(self.run_dir, "training_summary.json")
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Save human-readable report
        report_path = os.path.join(self.run_dir, "training_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("GOODNESS PREDICTION MODEL TRAINING REPORT\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"Training Date: {summary['timestamp']}\n")
            f.write(f"Dataset: {summary['dataset_path']}\n")
            f.write(f"Dataset Size: {summary['dataset_size']:,} entries\n\n")
            
            f.write("MODEL PERFORMANCE COMPARISON\n")
            f.write("-" * 40 + "\n")
            
            for model_name, metrics in evaluation_results.items():
                f.write(f"\n{model_name}:\n")
                for metric, value in metrics.items():
                    f.write(f"  {metric}: {value:.4f}\n")
            
            f.write(f"\nBEST MODEL: {summary['best_model']}\n")
            f.write("\n" + "=" * 80 + "\n")
        
        logger.info(f"Results summary saved to {self.run_dir}")
    
    def _select_best_model(self, evaluation_results: Dict[str, Dict[str, float]]) -> str:
        """Select best model based on NDCG score."""
        best_model = None
        best_ndcg = -1
        
        for model_name, metrics in evaluation_results.items():
            ndcg = metrics.get('ndcg', 0)
            if ndcg > best_ndcg:
                best_ndcg = ndcg
                best_model = model_name
        
        return best_model or "Unknown"

    def run_complete_training(self, test_size: float = 0.15, val_size: float = 0.15,
                            augment_data: bool = True, optimize_hyperparams: bool = True,
                            resnet_epochs: int = 50) -> Dict[str, Any]:
        """
        Run complete training pipeline for both models.

        Args:
            test_size: Test set fraction
            val_size: Validation set fraction
            augment_data: Whether to augment training data
            optimize_hyperparams: Whether to optimize LightGBM hyperparameters
            resnet_epochs: Number of epochs for ResNet training

        Returns:
            Complete training results
        """
        logger.info("Starting complete training pipeline...")

        # Load dataset
        if not self.load_dataset():
            raise ValueError("Failed to load dataset")

        # Prepare data
        X_train, X_val, X_test, y_train, y_val, y_test = self.prepare_data(
            test_size=test_size, val_size=val_size, augment_data=augment_data
        )

        # Train models
        models = {}

        try:
            # Train LightGBM
            lightgbm_model = self.train_lightgbm_model(
                X_train, y_train, X_val, y_val, optimize_hyperparams
            )
            models['LightGBM'] = lightgbm_model
        except Exception as e:
            logger.error(f"LightGBM training failed: {e}")

        try:
            # Train ResNet
            resnet_model = self.train_resnet_model(
                X_train, y_train, X_val, y_val, resnet_epochs
            )
            models['ResNet'] = resnet_model
        except Exception as e:
            logger.error(f"ResNet training failed: {e}")

        if not models:
            raise ValueError("All model training failed")

        # Evaluate models
        evaluation_results = self.evaluate_models(models, X_test, y_test)

        # Create visualizations
        self.create_comparison_visualizations(evaluation_results)

        # Save results
        self.save_results_summary(evaluation_results)

        results = {
            'models': models,
            'evaluation_results': evaluation_results,
            'data_splits': {
                'train_size': len(X_train),
                'val_size': len(X_val),
                'test_size': len(X_test)
            },
            'run_directory': self.run_dir
        }

        logger.info("Complete training pipeline finished successfully!")
        return results


def main():
    """Main execution function with CLI interface."""
    parser = argparse.ArgumentParser(description='Train Goodness Prediction Models')
    parser.add_argument('--dataset', default='production_dataset.pkl',
                       help='Path to structured prompt dataset')
    parser.add_argument('--output-dir', default='goodness_model_training',
                       help='Output directory for results')
    parser.add_argument('--test-size', type=float, default=0.15,
                       help='Test set fraction')
    parser.add_argument('--val-size', type=float, default=0.15,
                       help='Validation set fraction')
    parser.add_argument('--no-augmentation', action='store_true',
                       help='Skip data augmentation')
    parser.add_argument('--no-optimization', action='store_true',
                       help='Skip hyperparameter optimization')
    parser.add_argument('--resnet-epochs', type=int, default=50,
                       help='Number of epochs for ResNet training')
    parser.add_argument('--lightgbm-only', action='store_true',
                       help='Train only LightGBM model')
    parser.add_argument('--resnet-only', action='store_true',
                       help='Train only ResNet model')

    args = parser.parse_args()

    try:
        # Initialize trainer
        trainer = GoodnessModelTrainer(args.dataset, args.output_dir)

        # Run training
        results = trainer.run_complete_training(
            test_size=args.test_size,
            val_size=args.val_size,
            augment_data=not args.no_augmentation,
            optimize_hyperparams=not args.no_optimization,
            resnet_epochs=args.resnet_epochs
        )

        # Print summary
        print("\n" + "="*80)
        print("TRAINING COMPLETE")
        print("="*80)
        print(f"Results saved to: {results['run_directory']}")
        print(f"Models trained: {list(results['models'].keys())}")

        print("\nModel Performance Summary:")
        for model_name, metrics in results['evaluation_results'].items():
            print(f"\n{model_name}:")
            key_metrics = ['rmse', 'ndcg', 'precision_at_10', 'auc']
            for metric in key_metrics:
                if metric in metrics:
                    print(f"  {metric.upper()}: {metrics[metric]:.4f}")

        # Determine best model
        best_model = max(results['evaluation_results'].items(),
                        key=lambda x: x[1].get('ndcg', 0))
        print(f"\nBest Model: {best_model[0]} (NDCG: {best_model[1].get('ndcg', 0):.4f})")

    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise


if __name__ == "__main__":
    main()

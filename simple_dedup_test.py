#!/usr/bin/env python3
"""
Simple test for deduplication plan
"""

import dill
import logging
import re
from pathlib import Path
from collections import defaultdict, Counter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simple_dedup():
    """Test deduplication logic directly."""
    
    # Load dataset
    logger.info("Loading dataset...")
    with open("production_dataset.pkl", 'rb') as f:
        dataset = dill.load(f)
    logger.info(f"Loaded {len(dataset)} entries")
    
    # Find duplicates
    duplicate_groups = defaultdict(list)
    irrelevant_entries = []
    
    for i, (filename, tags, goodness_score) in enumerate(dataset):
        base_filename = Path(filename).name
        duplicate_groups[base_filename].append({
            'index': i,
            'full_path': filename,
            'goodness_score': goodness_score
        })
        
        # Check relevance
        tag_names = [tag.lower() for tag, weight in tags]
        is_relevant = any(tag in tag_names for tag in ['1girl', '2girls'])
        if not is_relevant:
            irrelevant_entries.append({
                'index': i,
                'full_path': filename
            })
    
    # Count duplicates
    actual_duplicates = {name: entries for name, entries in duplicate_groups.items() if len(entries) > 1}
    
    logger.info(f"Found {len(actual_duplicates)} duplicate groups")
    logger.info(f"Found {len(irrelevant_entries)} irrelevant entries")
    
    # Generate deduplication plan
    entries_to_remove = []
    
    # Remove duplicates (keep base-level)
    for filename, duplicate_entries in actual_duplicates.items():
        base_level_entries = []
        subfolder_entries = []
        
        for entry in duplicate_entries:
            path_parts = Path(entry['full_path']).parts
            
            # Find date folder
            date_index = -1
            for i, part in enumerate(path_parts):
                if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                    date_index = i
                    break
            
            if date_index >= 0:
                remaining_parts = path_parts[date_index+1:]
                if len(remaining_parts) == 1:
                    base_level_entries.append(entry)
                else:
                    subfolder_entries.append(entry)
        
        # Keep base-level, remove subfolder
        if base_level_entries:
            # Remove all but first base-level entry
            entries_to_remove.extend(base_level_entries[1:])
            entries_to_remove.extend(subfolder_entries)
        else:
            # No base-level entries, keep first subfolder entry
            entries_to_remove.extend(subfolder_entries[1:])
    
    # Add irrelevant entries to removal list
    entries_to_remove.extend(irrelevant_entries)
    
    # Calculate final statistics
    original_size = len(dataset)
    to_remove = len(entries_to_remove)
    final_size = original_size - to_remove
    reduction_percent = (to_remove / original_size) * 100
    
    logger.info(f"\nDEDUPLICATION PLAN RESULTS:")
    logger.info(f"  Original dataset: {original_size:,} entries")
    logger.info(f"  Entries to remove: {to_remove:,}")
    logger.info(f"  Final dataset: {final_size:,} entries")
    logger.info(f"  Reduction: {reduction_percent:.1f}%")
    
    return final_size

if __name__ == "__main__":
    test_simple_dedup()

2025-06-23 14:07:51,851 - MainThread - INFO - ================================================================================
2025-06-23 14:07:51,853 - MainThread - INFO - COMPREHENSIVE VALIDATION TEST SUITE
2025-06-23 14:07:51,854 - MainThread - INFO - ================================================================================
2025-06-23 14:07:51,854 - MainThread - INFO - Finding input directories...
2025-06-23 14:07:52,002 - MainThread - INFO - Found 67 input directories
2025-06-23 14:07:52,002 - MainThread - INFO - Sampling images from directories...
2025-06-23 14:07:52,177 - MainThread - INFO - Sampled 335 images for validation
2025-06-23 14:07:52,178 - MainThread - INFO - Starting validation with 4 workers...
2025-06-23 14:07:52,730 - ThreadPoolExecutor-0_0 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00004_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,732 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00004_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,768 - ThreadPoolExecutor-0_3 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00002_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,771 - ThreadPoolExecutor-0_2 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00041_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,778 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00002_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,781 - ThreadPoolExecutor-0_0 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00041_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,783 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00041_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,786 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00041_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,805 - ThreadPoolExecutor-0_1 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00020_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,809 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\ComfyUI_00020_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,839 - ThreadPoolExecutor-0_0 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00060_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,846 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00060_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,851 - ThreadPoolExecutor-0_2 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00052_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,854 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00052_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,865 - ThreadPoolExecutor-0_3 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00064_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,868 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00064_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,888 - ThreadPoolExecutor-0_1 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00023_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,890 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00023_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,914 - ThreadPoolExecutor-0_0 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00051_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,918 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-09-29\sel\ComfyUI_00051_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,920 - ThreadPoolExecutor-0_3 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00195_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,924 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00195_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,939 - ThreadPoolExecutor-0_2 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00048_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,941 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00048_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,966 - ThreadPoolExecutor-0_1 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00050_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,969 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00050_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,970 - ThreadPoolExecutor-0_0 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00184_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,971 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00184_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,981 - ThreadPoolExecutor-0_3 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00191_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:52,985 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\ComfyUI_00191_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,019 - ThreadPoolExecutor-0_1 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00189_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,025 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00189_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,045 - ThreadPoolExecutor-0_2 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00031_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,047 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00031_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,062 - ThreadPoolExecutor-0_3 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00068_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,064 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00068_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,078 - ThreadPoolExecutor-0_0 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00029_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,080 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00029_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,107 - ThreadPoolExecutor-0_1 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00181_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,110 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-10-05\sel\ComfyUI_00181_.png: 'list' object has no attribute 'lower'
2025-06-23 14:07:53,907 - ThreadPoolExecutor-0_0 - ERROR - Failed to validate F:\SD-webui\ComfyUI\output\2024-12-16\ComfyUI_061039_449802917750001_00001.png: Failed to traverse workflow graph: No output nodes found in workflow. Available node types: {'ImpactWildcardProcessor', 'TilePreprocessor', 'easy imageColorMatch', 'Reroute', 'Number to String', 'ImpactImageInfo', 'ImageScale', 'RescaleCFG', 'JoinStrings', 'ControlNetLoader', 'ModelSamplingSD3', 'SDParameterGenerator', 'KSampler Adv. (Efficient)', 'ShowText|pysssss', 'SAMLoader', 'TIPO', 'ControlNetApplySD3', 'String to Text', 'DPRandomGenerator', 'UltimateSDUpscale', 'easy loraStackApply', 'LoadImage', 'ImpactInt', 'LoRA Stack to String converter', 'Load Text File', 'FaceDetailer', 'UpscaleModelLoader', 'UltralyticsDetectorProvider', 'easy loraStack', 'easy float', 'CLIPTextEncode', 'Random Number', 'HighRes-Fix Script', 'SDPromptSaver', 'easy negative', 'easy promptConcat', 'EmptyLatentImage', 'Prompts', 'SDAnyConverter', 'easy positive'}
2025-06-23 14:07:53,912 - MainThread - ERROR - Validation failed for F:\SD-webui\ComfyUI\output\2024-12-16\ComfyUI_061039_449802917750001_00001.png: Failed to traverse workflow graph: No output nodes found in workflow. Available node types: {'ImpactWildcardProcessor', 'TilePreprocessor', 'easy imageColorMatch', 'Reroute', 'Number to String', 'ImpactImageInfo', 'ImageScale', 'RescaleCFG', 'JoinStrings', 'ControlNetLoader', 'ModelSamplingSD3', 'SDParameterGenerator', 'KSampler Adv. (Efficient)', 'ShowText|pysssss', 'SAMLoader', 'TIPO', 'ControlNetApplySD3', 'String to Text', 'DPRandomGenerator', 'UltimateSDUpscale', 'easy loraStackApply', 'LoadImage', 'ImpactInt', 'LoRA Stack to String converter', 'Load Text File', 'FaceDetailer', 'UpscaleModelLoader', 'UltralyticsDetectorProvider', 'easy loraStack', 'easy float', 'CLIPTextEncode', 'Random Number', 'HighRes-Fix Script', 'SDPromptSaver', 'easy negative', 'easy promptConcat', 'EmptyLatentImage', 'Prompts', 'SDAnyConverter', 'easy positive'}
2025-06-23 14:07:54,097 - MainThread - INFO - Processed 100/335 images (79 successful)
2025-06-23 14:07:55,499 - MainThread - INFO - Processed 200/335 images (179 successful)
2025-06-23 14:07:58,699 - MainThread - INFO - Processed 300/335 images (279 successful)
2025-06-23 14:07:59,432 - MainThread - INFO - 
================================================================================
2025-06-23 14:07:59,433 - MainThread - INFO - VALIDATION RESULTS
2025-06-23 14:07:59,433 - MainThread - INFO - ================================================================================
2025-06-23 14:07:59,434 - MainThread - INFO - Total images tested: 335
2025-06-23 14:07:59,434 - MainThread - INFO - Successful validations: 314
2025-06-23 14:07:59,434 - MainThread - INFO - Failed validations: 21
2025-06-23 14:07:59,434 - MainThread - INFO - Success rate: 93.73%
2025-06-23 14:07:59,435 - MainThread - INFO - 
Workflow formats found:
2025-06-23 14:07:59,436 - MainThread - INFO -   api_format: 233
2025-06-23 14:07:59,436 - MainThread - INFO -   no_workflow: 81
2025-06-23 14:07:59,437 - MainThread - INFO - 
LoRA statistics:
2025-06-23 14:07:59,438 - MainThread - INFO -   Total LoRAs found: 362
2025-06-23 14:07:59,438 - MainThread - INFO -   Images with LoRAs: 182
2025-06-23 14:07:59,438 - MainThread - INFO -   Average LoRAs per image: 1.08
2025-06-23 14:07:59,439 - MainThread - INFO - 
Graph traversal statistics:
2025-06-23 14:07:59,441 - MainThread - INFO -   Average connected nodes: 13.0
2025-06-23 14:07:59,442 - MainThread - INFO -   Average total LoRA nodes: 3.1
2025-06-23 14:07:59,442 - MainThread - INFO -   Average connected LoRA nodes: 1.6
2025-06-23 14:07:59,443 - MainThread - INFO - 
Error analysis:
2025-06-23 14:07:59,443 - MainThread - INFO -   'list' object has no attribute 'lower': 20
2025-06-23 14:07:59,443 - MainThread - INFO -   Failed to traverse workflow graph: No output nodes found in workflow. Available node types: {'ImpactWildcardProcessor', 'TilePreprocessor', 'easy imageColorMatch', 'Reroute', 'Number to String', 'ImpactImageInfo', 'ImageScale', 'RescaleCFG', 'JoinStrings', 'ControlNetLoader', 'ModelSamplingSD3', 'SDParameterGenerator', 'KSampler Adv. (Efficient)', 'ShowText|pysssss', 'SAMLoader', 'TIPO', 'ControlNetApplySD3', 'String to Text', 'DPRandomGenerator', 'UltimateSDUpscale', 'easy loraStackApply', 'LoadImage', 'ImpactInt', 'LoRA Stack to String converter', 'Load Text File', 'FaceDetailer', 'UpscaleModelLoader', 'UltralyticsDetectorProvider', 'easy loraStack', 'easy float', 'CLIPTextEncode', 'Random Number', 'HighRes-Fix Script', 'SDPromptSaver', 'easy negative', 'easy promptConcat', 'EmptyLatentImage', 'Prompts', 'SDAnyConverter', 'easy positive'}: 1
2025-06-23 14:07:59,455 - MainThread - INFO - 
Detailed results saved to: validation_results_20250623_140751.json
2025-06-23 14:07:59,456 - MainThread - INFO - 
================================================================================

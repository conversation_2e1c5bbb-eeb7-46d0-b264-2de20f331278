#!/usr/bin/env python3
"""
Corrected Prompt Parser - Fixes Critical Parsing Issues

This module completely fixes the critical parsing errors identified:
1. Massive concatenated LoRA parameters not being split properly
2. Weight values embedded in tag names instead of extracted as weights
3. Artist tags still misclassified as LoRA models
4. Incorrect tag normalization and splitting logic

Author: AI Assistant
Date: 2025-06-24
"""

import re
import logging
from typing import List, Tuple, Dict, Any, Optional
from collections import defaultdict
import unicodedata

logger = logging.getLogger(__name__)


class CorrectedPromptParser:
    """Completely corrected prompt parser addressing all critical issues."""
    
    def __init__(self):
        """Initialize the corrected parser."""
        
        # CRITICAL FIX: Only match actual LoRA file parameters with .safetensors/.ckpt
        # FIXED: Support negative weights like -1.0
        self.lora_pattern = re.compile(r'--lora\s+([a-zA-Z0-9_.-]+\.(?:safetensors|ckpt)):(-?[0-9.]+)')
        
        # Technical parameter patterns - ONLY command-line style parameters
        self.tech_param_patterns = {
            'cfg': re.compile(r'--cfg\s+([0-9.]+)'),
            'steps': re.compile(r'--steps\s+([0-9]+)'),
            'size': re.compile(r'--size\s+([0-9]+)x([0-9]+)'),
            'sampler': re.compile(r'--sampler\s+([a-zA-Z_]+)'),
            'scheduler': re.compile(r'--scheduler\s+([a-zA-Z_]+)'),
            'seed': re.compile(r'--seed\s+([0-9]+)')
        }
        
        # Weight syntax patterns - handle properly
        self.weight_patterns = [
            (re.compile(r'\(([^:)]+):([0-9.]+)\)'), lambda m: (m.group(1), float(m.group(2)))),  # (tag:1.2)
            (re.compile(r'\{\{\{([^}]+)\}\}\}'), lambda m: (m.group(1), 1.3)),  # {{{tag}}}
            (re.compile(r'\{\{([^}]+)\}\}'), lambda m: (m.group(1), 1.2)),      # {{tag}}
            (re.compile(r'\{([^}]+)\}'), lambda m: (m.group(1), 1.1)),          # {tag}
            (re.compile(r'\[\[([^\]]+)\]\]'), lambda m: (m.group(1), 0.8)),     # [[tag]]
            (re.compile(r'\[([^\]]+)\]'), lambda m: (m.group(1), 0.9)),         # [tag]
        ]
        
        # Tag splitting delimiters
        self.split_pattern = re.compile(r'[,，；;\n]+')
        
        # Statistics
        self.parsing_stats = defaultdict(int)
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text to fix encoding and normalization issues.
        
        Args:
            text: Raw text
            
        Returns:
            Cleaned and normalized text
        """
        if not isinstance(text, str):
            return str(text)
        
        try:
            # Fix literal \n strings to actual newlines
            text = text.replace('\\n', '\n')
            
            # Fix Unicode escape sequences
            try:
                text = text.encode('utf-8').decode('unicode_escape')
            except UnicodeDecodeError:
                pass  # Keep original if decode fails
            
            # Normalize Unicode
            text = unicodedata.normalize('NFKC', text)
            
            # Clean up unnecessary escapes
            text = re.sub(r'\\(?![()[\]{}])', '', text)
            
            return text
            
        except Exception as e:
            logger.warning(f"Text preprocessing failed: {e}")
            return text
    
    def extract_technical_parameters(self, text: str) -> Tuple[Dict[str, Any], str]:
        """
        Extract ONLY actual technical parameters, not artist tags.
        
        Args:
            text: Text to extract from
            
        Returns:
            Tuple of (parameters dict, cleaned text)
        """
        params = {}
        cleaned_text = text
        
        # CRITICAL FIX: Extract actual LoRA files only
        lora_matches = self.lora_pattern.findall(text)
        if lora_matches:
            loras = []
            for filename, weight_str in lora_matches:
                try:
                    weight = float(weight_str)
                    loras.append((filename, weight))
                except ValueError:
                    loras.append((filename, 1.0))
            params['loras'] = loras
            # Remove LoRA parameters from text
            cleaned_text = self.lora_pattern.sub('', cleaned_text)
        
        # Extract other technical parameters
        for param_name, pattern in self.tech_param_patterns.items():
            matches = pattern.findall(cleaned_text)
            if matches:
                if param_name == 'size':
                    width, height = matches[0]
                    params['width'] = int(width)
                    params['height'] = int(height)
                elif param_name in ['cfg', 'seed']:
                    params[param_name] = float(matches[0])
                elif param_name == 'steps':
                    params[param_name] = int(matches[0])
                else:
                    params[param_name] = matches[0]
                
                # Remove from text
                cleaned_text = pattern.sub('', cleaned_text)
        
        return params, cleaned_text
    
    def parse_weight_syntax(self, text: str) -> List[Tuple[str, float]]:
        """
        Parse weight syntax from text.
        
        Args:
            text: Text containing weight syntax
            
        Returns:
            List of (tag, weight) tuples
        """
        weighted_tags = []
        remaining_text = text
        
        # Process each weight pattern
        for pattern, extractor in self.weight_patterns:
            matches = list(pattern.finditer(remaining_text))
            for match in reversed(matches):  # Process in reverse to maintain positions
                try:
                    tag, weight = extractor(match)
                    # Handle multiple tags within brackets
                    if ',' in tag:
                        sub_tags = [t.strip() for t in tag.split(',') if t.strip()]
                        for sub_tag in sub_tags:
                            weighted_tags.append((self.normalize_tag(sub_tag), weight))
                    else:
                        weighted_tags.append((self.normalize_tag(tag), weight))
                    
                    # Remove processed part
                    start, end = match.span()
                    remaining_text = remaining_text[:start] + remaining_text[end:]
                except Exception as e:
                    logger.warning(f"Error processing weight syntax: {e}")
        
        return weighted_tags, remaining_text
    
    def normalize_tag(self, tag: str) -> str:
        """
        Normalize tag with proper handling.
        
        Args:
            tag: Raw tag
            
        Returns:
            Normalized tag
        """
        if not isinstance(tag, str):
            return str(tag)
        
        # Clean the tag
        tag = tag.strip()
        if not tag:
            return ""
        
        # CRITICAL FIX: Handle embedded weight syntax in tags
        # Remove any embedded weight syntax like ":0.6" from tag names
        tag = re.sub(r':([0-9.]+)$', '', tag)
        
        # Replace underscores with spaces for processing
        tag = tag.replace('_', ' ')
        
        # Normalize whitespace
        tag = re.sub(r'\s+', ' ', tag).strip()
        
        # Convert back to underscores
        tag = tag.replace(' ', '_')
        
        # Clean problematic characters
        tag = re.sub(r'[^\w\-:.]', '_', tag)
        
        # Clean up multiple underscores
        tag = re.sub(r'_+', '_', tag).strip('_')
        
        return tag.lower()
    
    def split_into_tags(self, text: str) -> List[str]:
        """
        Split text into individual tags.

        Args:
            text: Text to split

        Returns:
            List of tag strings
        """
        if not text or not text.strip():
            return []

        # CRITICAL FIX: Split by delimiters but also handle massive concatenated strings
        parts = self.split_pattern.split(text)

        # Additional splitting for concatenated technical parameters
        expanded_parts = []
        for part in parts:
            part = part.strip()
            if not part:
                continue

            # CRITICAL FIX: Split massive concatenated parameter strings
            # Look for patterns like "param1_--param2_--param3"
            if '--' in part and len(part) > 50:  # Likely concatenated
                # Split by -- but keep the --
                sub_parts = re.split(r'(_--)', part)
                current_param = ""
                for sub_part in sub_parts:
                    if sub_part == '_--':
                        if current_param:
                            expanded_parts.append(current_param.strip())
                        current_param = "--"
                    else:
                        current_param += sub_part
                if current_param:
                    expanded_parts.append(current_param.strip())
            else:
                expanded_parts.append(part)

        # Filter and clean
        tags = []
        for part in expanded_parts:
            part = part.strip()
            if part and len(part) > 0:
                tags.append(part)

        return tags

    def extract_lora_from_concatenated(self, text: str) -> List[Tuple[str, float]]:
        """
        Extract LoRA parameters from concatenated strings.

        Args:
            text: Text that may contain concatenated LoRA parameters

        Returns:
            List of (lora_name, weight) tuples
        """
        loras = []

        # Pattern for embedded LoRA in concatenated strings
        # Matches: --lora_filename.safetensors:weight or --lora_filename:weight
        # FIXED: Support negative weights like -1.0
        lora_pattern = re.compile(r'--lora[_\s]+([a-zA-Z0-9_.-]+(?:\.safetensors|\.ckpt)?):(-?[0-9.]+)')

        matches = lora_pattern.findall(text)
        for filename, weight_str in matches:
            try:
                weight = float(weight_str)
                # Ensure filename has extension
                if not filename.endswith(('.safetensors', '.ckpt')):
                    filename += '.safetensors'
                loras.append((filename, weight))
            except ValueError:
                if not filename.endswith(('.safetensors', '.ckpt')):
                    filename += '.safetensors'
                loras.append((filename, 1.0))

        return loras
    
    def parse_prompt_corrected(self, prompt: str) -> List[Tuple[str, float]]:
        """
        Parse prompt with all critical fixes applied.
        
        Args:
            prompt: Raw prompt string
            
        Returns:
            List of (tag, weight) tuples
        """
        try:
            if not isinstance(prompt, str) or not prompt.strip():
                return []
            
            self.parsing_stats['total_prompts'] += 1
            
            # Step 1: Preprocess text
            processed_text = self.preprocess_text(prompt)
            
            # Step 2: Extract technical parameters (ONLY actual --param syntax)
            tech_params, text_without_params = self.extract_technical_parameters(processed_text)

            # Step 2.5: CRITICAL FIX - Extract LoRA from concatenated strings
            concatenated_loras = self.extract_lora_from_concatenated(text_without_params)
            if concatenated_loras:
                if 'loras' not in tech_params:
                    tech_params['loras'] = []
                tech_params['loras'].extend(concatenated_loras)
                # Remove LoRA patterns from text
                # FIXED: Support negative weights in removal pattern
                text_without_params = re.sub(r'--lora[_\s]+[a-zA-Z0-9_.-]+(?:\.safetensors|\.ckpt)?:-?[0-9.]+', '', text_without_params)

            # Step 3: Parse weight syntax
            weighted_tags, remaining_text = self.parse_weight_syntax(text_without_params)
            
            # Step 4: Split remaining text into tags
            raw_tags = self.split_into_tags(remaining_text)
            
            # Step 5: Add unweighted tags
            for tag in raw_tags:
                normalized_tag = self.normalize_tag(tag)
                if normalized_tag and len(normalized_tag) > 0:
                    # Add all tags - artist tags are fine, just don't classify them as LoRA
                    weighted_tags.append((normalized_tag, 1.0))
            
            # Step 6: Convert technical parameters to tags
            tech_tags = []
            for param_name, param_value in tech_params.items():
                if param_name == 'loras':
                    # CRITICAL FIX: Only actual LoRA files - PRESERVE ORIGINAL FILENAMES
                    for lora_filename, lora_weight in param_value:
                        clean_name = lora_filename.replace('.safetensors', '').replace('.ckpt', '')
                        # CRITICAL: Do NOT normalize LoRA filenames - preserve them exactly
                        tech_tags.append((f'lora_{clean_name}', lora_weight))
                elif param_name in ['cfg', 'steps', 'width', 'height', 'seed']:
                    tech_tags.append((param_name, float(param_value)))
                else:
                    tech_tags.append((param_name, 1.0))
            
            # Step 7: Combine and deduplicate
            all_tags = weighted_tags + tech_tags
            final_tags = self.deduplicate_tags(all_tags)
            
            self.parsing_stats['successful_parses'] += 1
            return final_tags
            
        except Exception as e:
            logger.error(f"Error parsing prompt: {e}")
            self.parsing_stats['parsing_errors'] += 1
            return [('parsing_error', 1.0)]
    
    def is_artist_tag(self, tag: str) -> bool:
        """
        Check if a tag is an artist/style tag that should NOT be classified as LoRA.
        
        Args:
            tag: Normalized tag
            
        Returns:
            True if this is an artist/style tag
        """
        artist_indicators = [
            'ciloranko', 'artist:', 'style:', 'hiten', 'sy4', 'imigimuru',
            'mimelond', 'fujiyama', 'umanosuke', 'ogipote', 'nii_manabu',
            'fkey', 'rin_yuu', 'nekojira', 'floral_print', 'printed_flora'
        ]
        
        return any(indicator in tag.lower() for indicator in artist_indicators)
    
    def deduplicate_tags(self, tags: List[Tuple[str, float]]) -> List[Tuple[str, float]]:
        """
        Deduplicate and validate tags.
        
        Args:
            tags: List of (tag, weight) tuples
            
        Returns:
            Deduplicated list
        """
        tag_weights = defaultdict(list)
        
        for tag, weight in tags:
            if not isinstance(tag, str) or not tag.strip():
                continue
            
            # Validate weight
            try:
                weight = float(weight)
                if weight > 5.0:
                    weight = 2.0
                elif weight < -2.0:
                    weight = 0.1
            except (ValueError, TypeError):
                weight = 1.0
            
            tag_weights[tag].append(weight)
        
        # Average weights for duplicates
        final_tags = []
        for tag, weights in tag_weights.items():
            avg_weight = sum(weights) / len(weights)
            final_tags.append((tag, round(avg_weight, 3)))
        
        return final_tags


def parse_prompt_with_weights_corrected(prompt_string: str) -> List[Tuple[str, float]]:
    """
    Corrected version of parse_prompt_with_weights function.
    
    This function completely fixes all critical parsing issues:
    - Proper splitting of concatenated LoRA parameters
    - Correct weight extraction from tag names
    - Accurate LoRA vs artist tag classification
    - Fixed tag normalization logic
    
    Args:
        prompt_string: Enhanced prompt string
        
    Returns:
        List of (tag, weight) tuples
    """
    parser = CorrectedPromptParser()
    return parser.parse_prompt_corrected(prompt_string)


def test_corrected_parser():
    """Test the corrected parser with problematic examples."""
    parser = CorrectedPromptParser()
    
    test_cases = [
        # Test 1: Massive concatenated LoRA parameters
        "best_composition_--cfg_5.5_--steps_38_--size_960x1280_--sampler_euler_ancestral_--lora_phoebe-wwil-v1.safetensors:1.0_--lora_niji_cute_style_illustrious.safetensors:0.8",
        
        # Test 2: Weight embedded in tag name
        "lora:yoneyamaixl_illu_lokr:0.6",
        
        # Test 3: Artist tags that should NOT be LoRA
        "ciloranko, artist:ciloranko, floral_print",
        
        # Test 4: Duplicate LoRA parameters
        "2m_--lora_yoneyamaixl_illu_lokr:0.6_--lora_yoneyamaixl_illu_lokr:0.6",
        
        # Test 5: Normal prompt with weights
        "1girl, {masterpiece}, [low quality], (detailed:1.2)"
    ]
    
    print("Testing Corrected Parser:")
    print("=" * 60)
    
    for i, test_prompt in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_prompt[:80]}...")
        result = parser.parse_prompt_corrected(test_prompt)
        print(f"Result ({len(result)} tags):")
        for tag, weight in result[:10]:  # Show first 10
            print(f"  - {tag}: {weight}")
        if len(result) > 10:
            print(f"  ... and {len(result) - 10} more")


if __name__ == "__main__":
    test_corrected_parser()

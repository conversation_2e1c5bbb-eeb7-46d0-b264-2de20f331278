#!/usr/bin/env python3
"""
Prompt Uniqueness Analysis and Ratio-Based Labeling

This script analyzes the uniqueness of prompts in the cleaned dataset by:
1. Converting tag lists to comma-joined strings
2. Counting good vs normal instances for each unique prompt combination
3. Calculating quality ratios for improved model training labels
4. Generating statistics and recommendations for ratio-based labeling

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
from typing import List, Tuple, Dict, Set, Any
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'prompt_uniqueness_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PromptUniquenessAnalyzer:
    """Analyzes prompt uniqueness and calculates quality ratios."""
    
    def __init__(self, dataset_path: str = "cleaned_dataset.pkl"):
        """Initialize the analyzer."""
        self.dataset_path = dataset_path
        self.dataset = None
        self.analysis_results = {}
        
    def load_dataset(self) -> bool:
        """Load the dataset from pickle file."""
        try:
            logger.info(f"Loading dataset from {self.dataset_path}...")
            with open(self.dataset_path, 'rb') as f:
                self.dataset = dill.load(f)
            logger.info(f"Successfully loaded {len(self.dataset)} entries")
            return True
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            return False
    
    def create_prompt_signatures(self) -> Dict[str, Any]:
        """Create unique prompt signatures from tag combinations."""
        logger.info("Creating prompt signatures from tag combinations...")
        
        prompt_signatures = defaultdict(lambda: {'good_count': 0, 'normal_count': 0, 'filenames': []})
        goodness_distribution = Counter()
        
        for filename, tags, goodness_score in self.dataset:
            # Create prompt signature by joining tag names (ignore weights for uniqueness)
            tag_names = [tag for tag, weight in tags]
            prompt_signature = ','.join(sorted(tag_names))  # Sort for consistency
            
            # Count good vs normal instances
            if goodness_score > 0.5:  # Assuming > 0.5 is "good"
                prompt_signatures[prompt_signature]['good_count'] += 1
            else:
                prompt_signatures[prompt_signature]['normal_count'] += 1
            
            prompt_signatures[prompt_signature]['filenames'].append(filename)
            goodness_distribution[goodness_score] += 1
        
        # Calculate ratios and statistics
        signature_stats = {}
        for signature, counts in prompt_signatures.items():
            total_count = counts['good_count'] + counts['normal_count']
            quality_ratio = counts['good_count'] / total_count if total_count > 0 else 0.0
            
            signature_stats[signature] = {
                'good_count': counts['good_count'],
                'normal_count': counts['normal_count'],
                'total_count': total_count,
                'quality_ratio': quality_ratio,
                'filenames': counts['filenames']
            }
        
        analysis = {
            'total_entries': len(self.dataset),
            'unique_signatures': len(signature_stats),
            'signature_stats': signature_stats,
            'goodness_distribution': dict(goodness_distribution),
            'duplicate_signatures': {sig: stats for sig, stats in signature_stats.items() if stats['total_count'] > 1}
        }
        
        logger.info(f"Created {analysis['unique_signatures']} unique prompt signatures")
        logger.info(f"Found {len(analysis['duplicate_signatures'])} signatures with multiple instances")
        
        self.analysis_results['signature_analysis'] = analysis
        return analysis
    
    def analyze_quality_ratios(self) -> Dict[str, Any]:
        """Analyze the distribution of quality ratios."""
        logger.info("Analyzing quality ratio distributions...")
        
        signature_analysis = self.analysis_results.get('signature_analysis', {})
        signature_stats = signature_analysis.get('signature_stats', {})
        
        # Extract ratio statistics
        ratios = [stats['quality_ratio'] for stats in signature_stats.values()]
        total_counts = [stats['total_count'] for stats in signature_stats.values()]
        
        # Analyze duplicates (signatures with multiple instances)
        duplicate_stats = signature_analysis.get('duplicate_signatures', {})
        duplicate_ratios = [stats['quality_ratio'] for stats in duplicate_stats.values()]
        duplicate_counts = [stats['total_count'] for stats in duplicate_stats.values()]
        
        ratio_analysis = {
            'total_signatures': len(ratios),
            'duplicate_signatures': len(duplicate_ratios),
            'single_instance_signatures': len(ratios) - len(duplicate_ratios),
            
            # Overall ratio statistics
            'ratio_mean': np.mean(ratios),
            'ratio_std': np.std(ratios),
            'ratio_min': np.min(ratios),
            'ratio_max': np.max(ratios),
            'ratio_median': np.median(ratios),
            
            # Duplicate-specific statistics
            'duplicate_ratio_mean': np.mean(duplicate_ratios) if duplicate_ratios else 0,
            'duplicate_ratio_std': np.std(duplicate_ratios) if duplicate_ratios else 0,
            'duplicate_count_mean': np.mean(duplicate_counts) if duplicate_counts else 0,
            'duplicate_count_max': np.max(duplicate_counts) if duplicate_counts else 0,
            
            # Distribution bins
            'ratio_bins': np.histogram(ratios, bins=10)[0].tolist(),
            'ratio_bin_edges': np.histogram(ratios, bins=10)[1].tolist(),
            
            # Raw data for further analysis
            'all_ratios': ratios,
            'duplicate_ratios': duplicate_ratios,
            'total_counts': total_counts,
            'duplicate_counts': duplicate_counts
        }
        
        logger.info(f"Ratio statistics:")
        logger.info(f"  Mean ratio: {ratio_analysis['ratio_mean']:.4f}")
        logger.info(f"  Std ratio: {ratio_analysis['ratio_std']:.4f}")
        logger.info(f"  Signatures with duplicates: {ratio_analysis['duplicate_signatures']}")
        logger.info(f"  Max duplicate count: {ratio_analysis['duplicate_count_max']}")
        
        self.analysis_results['ratio_analysis'] = ratio_analysis
        return ratio_analysis
    
    def find_interesting_cases(self) -> Dict[str, Any]:
        """Find interesting cases for analysis."""
        logger.info("Finding interesting cases...")
        
        signature_stats = self.analysis_results['signature_analysis']['signature_stats']
        
        # Find cases with high variance (mixed good/bad ratings)
        mixed_quality_cases = []
        pure_good_cases = []
        pure_bad_cases = []
        high_frequency_cases = []
        
        for signature, stats in signature_stats.items():
            if stats['total_count'] > 1:  # Only consider duplicates
                ratio = stats['quality_ratio']
                
                if 0.2 < ratio < 0.8:  # Mixed quality
                    mixed_quality_cases.append((signature, stats))
                elif ratio == 1.0:  # Pure good
                    pure_good_cases.append((signature, stats))
                elif ratio == 0.0:  # Pure bad
                    pure_bad_cases.append((signature, stats))
                
                if stats['total_count'] >= 5:  # High frequency
                    high_frequency_cases.append((signature, stats))
        
        # Sort by interesting metrics
        mixed_quality_cases.sort(key=lambda x: x[1]['total_count'], reverse=True)
        pure_good_cases.sort(key=lambda x: x[1]['total_count'], reverse=True)
        pure_bad_cases.sort(key=lambda x: x[1]['total_count'], reverse=True)
        high_frequency_cases.sort(key=lambda x: x[1]['total_count'], reverse=True)
        
        interesting_cases = {
            'mixed_quality': mixed_quality_cases[:10],  # Top 10
            'pure_good': pure_good_cases[:10],
            'pure_bad': pure_bad_cases[:10],
            'high_frequency': high_frequency_cases[:10]
        }
        
        logger.info(f"Found interesting cases:")
        logger.info(f"  Mixed quality: {len(mixed_quality_cases)}")
        logger.info(f"  Pure good: {len(pure_good_cases)}")
        logger.info(f"  Pure bad: {len(pure_bad_cases)}")
        logger.info(f"  High frequency: {len(high_frequency_cases)}")
        
        self.analysis_results['interesting_cases'] = interesting_cases
        return interesting_cases
    
    def create_ratio_based_dataset(self) -> List[Tuple[str, List[Tuple[str, float]], float]]:
        """Create a new dataset with ratio-based labels."""
        logger.info("Creating ratio-based dataset...")
        
        signature_stats = self.analysis_results['signature_analysis']['signature_stats']
        ratio_based_dataset = []
        
        # Create mapping from filename to quality ratio
        filename_to_ratio = {}
        for signature, stats in signature_stats.items():
            quality_ratio = stats['quality_ratio']
            for filename in stats['filenames']:
                filename_to_ratio[filename] = quality_ratio
        
        # Create new dataset with ratio labels
        for filename, tags, original_goodness in self.dataset:
            new_goodness = filename_to_ratio.get(filename, original_goodness)
            ratio_based_dataset.append((filename, tags, new_goodness))
        
        logger.info(f"Created ratio-based dataset with {len(ratio_based_dataset)} entries")
        
        # Calculate improvement statistics
        original_unique_labels = len(set(goodness for _, _, goodness in self.dataset))
        new_unique_labels = len(set(goodness for _, _, goodness in ratio_based_dataset))
        
        logger.info(f"Label diversity improvement:")
        logger.info(f"  Original unique labels: {original_unique_labels}")
        logger.info(f"  New unique labels: {new_unique_labels}")
        logger.info(f"  Improvement factor: {new_unique_labels / original_unique_labels:.2f}x")
        
        return ratio_based_dataset
    
    def create_visualizations(self):
        """Create comprehensive visualizations."""
        logger.info("Creating visualizations...")
        
        ratio_analysis = self.analysis_results.get('ratio_analysis', {})
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Prompt Uniqueness and Quality Ratio Analysis', fontsize=16, fontweight='bold')
        
        # 1. Quality ratio distribution
        ax1 = axes[0, 0]
        ratios = ratio_analysis['all_ratios']
        ax1.hist(ratios, bins=20, alpha=0.7, color='blue', edgecolor='black')
        ax1.set_title('Distribution of Quality Ratios')
        ax1.set_xlabel('Quality Ratio (Good / Total)')
        ax1.set_ylabel('Frequency')
        ax1.axvline(ratio_analysis['ratio_mean'], color='red', linestyle='--', label=f'Mean: {ratio_analysis["ratio_mean"]:.3f}')
        ax1.legend()
        
        # 2. Duplicate count distribution
        ax2 = axes[0, 1]
        duplicate_counts = ratio_analysis['duplicate_counts']
        if duplicate_counts:
            ax2.hist(duplicate_counts, bins=min(20, max(duplicate_counts)), alpha=0.7, color='green', edgecolor='black')
            ax2.set_title('Distribution of Duplicate Counts')
            ax2.set_xlabel('Number of Instances per Signature')
            ax2.set_ylabel('Frequency')
            ax2.axvline(ratio_analysis['duplicate_count_mean'], color='red', linestyle='--', 
                       label=f'Mean: {ratio_analysis["duplicate_count_mean"]:.1f}')
            ax2.legend()
        else:
            ax2.text(0.5, 0.5, 'No duplicates found', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('Distribution of Duplicate Counts')
        
        # 3. Ratio vs Count scatter plot
        ax3 = axes[1, 0]
        if ratio_analysis['duplicate_ratios']:
            ax3.scatter(ratio_analysis['duplicate_counts'], ratio_analysis['duplicate_ratios'], 
                       alpha=0.6, color='purple')
            ax3.set_title('Quality Ratio vs Duplicate Count')
            ax3.set_xlabel('Number of Instances')
            ax3.set_ylabel('Quality Ratio')
            ax3.grid(True, alpha=0.3)
        else:
            ax3.text(0.5, 0.5, 'No duplicates to plot', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('Quality Ratio vs Duplicate Count')
        
        # 4. Summary statistics
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        # Create summary text
        summary_text = f"""
        SUMMARY STATISTICS
        
        Total Entries: {ratio_analysis['total_signatures']:,}
        Unique Signatures: {ratio_analysis['total_signatures']:,}
        Duplicate Signatures: {ratio_analysis['duplicate_signatures']:,}
        Single Instance: {ratio_analysis['single_instance_signatures']:,}
        
        Quality Ratio Stats:
        Mean: {ratio_analysis['ratio_mean']:.4f}
        Std: {ratio_analysis['ratio_std']:.4f}
        Median: {ratio_analysis['ratio_median']:.4f}
        Range: [{ratio_analysis['ratio_min']:.3f}, {ratio_analysis['ratio_max']:.3f}]
        
        Duplicate Stats:
        Mean Ratio: {ratio_analysis['duplicate_ratio_mean']:.4f}
        Mean Count: {ratio_analysis['duplicate_count_mean']:.1f}
        Max Count: {ratio_analysis['duplicate_count_max']}
        """
        
        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, fontsize=10, 
                verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        # Save visualization
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        viz_path = f"prompt_uniqueness_analysis_{timestamp}.png"
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Visualization saved to {viz_path}")
        return viz_path
    
    def print_analysis_summary(self):
        """Print comprehensive analysis summary."""
        logger.info("\n" + "="*80)
        logger.info("PROMPT UNIQUENESS ANALYSIS SUMMARY")
        logger.info("="*80)
        
        # Signature analysis
        if 'signature_analysis' in self.analysis_results:
            sig_analysis = self.analysis_results['signature_analysis']
            logger.info(f"\nSIGNATURE ANALYSIS:")
            logger.info(f"  Total entries: {sig_analysis['total_entries']:,}")
            logger.info(f"  Unique signatures: {sig_analysis['unique_signatures']:,}")
            logger.info(f"  Duplicate signatures: {len(sig_analysis['duplicate_signatures']):,}")
            logger.info(f"  Uniqueness ratio: {sig_analysis['unique_signatures'] / sig_analysis['total_entries']:.4f}")
        
        # Ratio analysis
        if 'ratio_analysis' in self.analysis_results:
            ratio_analysis = self.analysis_results['ratio_analysis']
            logger.info(f"\nQUALITY RATIO ANALYSIS:")
            logger.info(f"  Mean quality ratio: {ratio_analysis['ratio_mean']:.4f}")
            logger.info(f"  Std quality ratio: {ratio_analysis['ratio_std']:.4f}")
            logger.info(f"  Median quality ratio: {ratio_analysis['ratio_median']:.4f}")
            logger.info(f"  Max duplicate count: {ratio_analysis['duplicate_count_max']}")
        
        # Interesting cases
        if 'interesting_cases' in self.analysis_results:
            cases = self.analysis_results['interesting_cases']
            logger.info(f"\nINTERESTING CASES:")
            logger.info(f"  Mixed quality cases: {len(cases['mixed_quality'])}")
            logger.info(f"  Pure good cases: {len(cases['pure_good'])}")
            logger.info(f"  Pure bad cases: {len(cases['pure_bad'])}")
            logger.info(f"  High frequency cases: {len(cases['high_frequency'])}")
            
            # Show top mixed quality case
            if cases['mixed_quality']:
                top_mixed = cases['mixed_quality'][0]
                signature, stats = top_mixed
                logger.info(f"\nTOP MIXED QUALITY CASE:")
                logger.info(f"  Signature: {signature[:100]}...")
                logger.info(f"  Good: {stats['good_count']}, Normal: {stats['normal_count']}")
                logger.info(f"  Ratio: {stats['quality_ratio']:.4f}")
    
    def run_full_analysis(self) -> Dict[str, Any]:
        """Run the complete uniqueness analysis."""
        logger.info("Starting comprehensive prompt uniqueness analysis...")
        
        if not self.load_dataset():
            return {}
        
        # Run all analyses
        self.create_prompt_signatures()
        self.analyze_quality_ratios()
        self.find_interesting_cases()
        
        # Create ratio-based dataset
        ratio_based_dataset = self.create_ratio_based_dataset()
        
        # Create visualizations
        viz_path = self.create_visualizations()
        
        # Print summary
        self.print_analysis_summary()
        
        results = {
            'analysis_results': self.analysis_results,
            'ratio_based_dataset': ratio_based_dataset,
            'visualization_path': viz_path
        }
        
        logger.info("\nPrompt uniqueness analysis completed successfully!")
        return results


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Analyze prompt uniqueness and calculate quality ratios")
    parser.add_argument("--dataset", default="cleaned_dataset.pkl", help="Path to dataset pickle file")
    parser.add_argument("--save-ratio-dataset", help="Path to save ratio-based dataset")
    
    args = parser.parse_args()
    
    analyzer = PromptUniquenessAnalyzer(args.dataset)
    results = analyzer.run_full_analysis()
    
    # Save ratio-based dataset if requested
    if args.save_ratio_dataset and 'ratio_based_dataset' in results:
        logger.info(f"Saving ratio-based dataset to {args.save_ratio_dataset}...")
        with open(args.save_ratio_dataset, 'wb') as f:
            dill.dump(results['ratio_based_dataset'], f)
        logger.info("Ratio-based dataset saved successfully!")
    
    return 0 if results else 1


if __name__ == "__main__":
    sys.exit(main())

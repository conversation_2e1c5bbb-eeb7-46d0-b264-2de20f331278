#!/usr/bin/env python3
"""
Goodness Prediction CLI Tool

Command-line interface for predicting prompt goodness scores using trained models.

Features:
- Support for both LightGBM and ResNet models
- Raw prompt string input or structured data input
- Batch prediction capability
- Confidence intervals and prediction explanations
- Model comparison mode

Usage Examples:
    # Single prompt prediction
    python predict_goodness.py --prompt "1girl, masterpiece, best quality" --model lightgbm_model.pkl
    
    # Batch prediction from file
    python predict_goodness.py --input-file prompts.txt --model resnet_model.pkl --output predictions.json
    
    # Compare models
    python predict_goodness.py --prompt "amazing artwork" --compare-models lightgbm_model.pkl resnet_model.pkl

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import argparse
import logging
from typing import List, Tuple, Dict, Any, Optional, Union
import numpy as np
import dill

# Import models
from goodness_predictor_models import LightGBMGoodnessPredictorModel
from resnet_goodness_model import ResNetGoodnessPredictorModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PromptParser:
    """
    Parser for converting raw prompt strings to structured format.
    """
    
    def __init__(self):
        """Initialize the parser."""
        self.technical_params = {'cfg', 'steps', 'width', 'height', 'sampler', 'scheduler', 'seed'}
    
    def parse_prompt(self, prompt_text: str) -> List[Tuple[str, float]]:
        """
        Parse raw prompt text into structured (tag, weight) format.
        
        Args:
            prompt_text: Raw prompt string
            
        Returns:
            List of (tag, weight) tuples
        """
        if not prompt_text or not prompt_text.strip():
            return []
        
        # Simple parsing - split by commas and extract weights
        tags = []
        
        # Split by common delimiters
        parts = prompt_text.replace('\n', ',').split(',')
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # Extract weight from various formats
            weight = 1.0
            tag = part
            
            # Handle (tag:weight) format
            if ':' in part and '(' in part and ')' in part:
                try:
                    # Extract from (tag:weight) format
                    inner = part.strip('()')
                    if ':' in inner:
                        tag_part, weight_part = inner.rsplit(':', 1)
                        tag = tag_part.strip()
                        weight = float(weight_part.strip())
                except:
                    pass
            
            # Handle {tag} and [tag] formats
            elif part.startswith('{') and part.endswith('}'):
                tag = part[1:-1].strip()
                weight = 1.1  # Slight emphasis
            elif part.startswith('[') and part.endswith(']'):
                tag = part[1:-1].strip()
                weight = 0.9  # Slight de-emphasis
            
            # Clean tag
            tag = tag.strip().lower().replace(' ', '_')
            
            if tag:
                tags.append((tag, weight))
        
        return tags


class GoodnessPredictor:
    """
    Main prediction interface for goodness scores.
    """
    
    def __init__(self):
        """Initialize the predictor."""
        self.models = {}
        self.parser = PromptParser()
    
    def load_model(self, model_path: str, model_name: Optional[str] = None) -> str:
        """
        Load a trained model.
        
        Args:
            model_path: Path to the model file
            model_name: Optional name for the model
            
        Returns:
            Model identifier
        """
        if model_name is None:
            model_name = os.path.basename(model_path).replace('.pkl', '')
        
        try:
            logger.info(f"Loading model from {model_path}")
            
            # Try to determine model type and load appropriately
            with open(model_path, 'rb') as f:
                model_data = dill.load(f)
            
            if isinstance(model_data, dict) and 'model_name' in model_data:
                # New format with metadata
                if 'LightGBM' in model_data['model_name']:
                    model = LightGBMGoodnessPredictorModel()
                elif 'ResNet' in model_data['model_name']:
                    model = ResNetGoodnessPredictorModel()
                else:
                    raise ValueError(f"Unknown model type: {model_data['model_name']}")
                
                model.load_model(model_path)
            else:
                # Legacy format - try to infer type
                if 'lightgbm' in model_path.lower():
                    model = LightGBMGoodnessPredictorModel()
                    model.load_model(model_path)
                elif 'resnet' in model_path.lower():
                    model = ResNetGoodnessPredictorModel()
                    model.load_model(model_path)
                else:
                    raise ValueError("Cannot determine model type from path")
            
            self.models[model_name] = model
            logger.info(f"Model loaded successfully: {model_name}")
            return model_name
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def predict_single(self, prompt: str, model_name: str) -> Dict[str, Any]:
        """
        Predict goodness score for a single prompt.
        
        Args:
            prompt: Raw prompt string
            model_name: Name of the model to use
            
        Returns:
            Prediction results with score and metadata
        """
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not loaded")
        
        model = self.models[model_name]
        
        # Parse prompt
        tags = self.parser.parse_prompt(prompt)
        
        if not tags:
            logger.warning("No valid tags found in prompt")
            return {
                'prompt': prompt,
                'score': 0.0,
                'confidence': 'low',
                'tags_found': 0,
                'warning': 'No valid tags found'
            }
        
        # Create structured data format
        structured_prompt = ("input_prompt", tags, 0.0)  # Dummy goodness for prediction
        
        # Predict
        try:
            score = model.predict(structured_prompt)
            
            # Calculate confidence based on score and tag count
            confidence = self._calculate_confidence(score, len(tags))
            
            # Get feature importance if available (for LightGBM)
            feature_explanation = None
            if hasattr(model, 'get_feature_importance'):
                try:
                    feature_explanation = self._explain_prediction(model, tags)
                except:
                    pass
            
            result = {
                'prompt': prompt,
                'score': float(score),
                'confidence': confidence,
                'tags_found': len(tags),
                'parsed_tags': tags[:10],  # Show first 10 tags
                'model_used': model_name
            }
            
            if feature_explanation:
                result['explanation'] = feature_explanation
            
            return result
            
        except Exception as e:
            logger.error(f"Prediction error: {e}")
            return {
                'prompt': prompt,
                'score': 0.0,
                'confidence': 'error',
                'tags_found': len(tags),
                'error': str(e)
            }
    
    def predict_batch(self, prompts: List[str], model_name: str) -> List[Dict[str, Any]]:
        """
        Predict goodness scores for multiple prompts.
        
        Args:
            prompts: List of raw prompt strings
            model_name: Name of the model to use
            
        Returns:
            List of prediction results
        """
        logger.info(f"Predicting {len(prompts)} prompts with {model_name}")
        
        results = []
        for i, prompt in enumerate(prompts):
            if (i + 1) % 100 == 0:
                logger.info(f"Processed {i + 1}/{len(prompts)} prompts")
            
            result = self.predict_single(prompt, model_name)
            results.append(result)
        
        return results
    
    def compare_models(self, prompt: str, model_names: List[str]) -> Dict[str, Any]:
        """
        Compare predictions from multiple models.
        
        Args:
            prompt: Raw prompt string
            model_names: List of model names to compare
            
        Returns:
            Comparison results
        """
        results = {}
        
        for model_name in model_names:
            if model_name in self.models:
                results[model_name] = self.predict_single(prompt, model_name)
            else:
                logger.warning(f"Model {model_name} not loaded")
        
        # Calculate agreement metrics
        scores = [result['score'] for result in results.values() if 'score' in result]
        
        comparison = {
            'prompt': prompt,
            'model_predictions': results,
            'score_statistics': {
                'mean': np.mean(scores) if scores else 0.0,
                'std': np.std(scores) if len(scores) > 1 else 0.0,
                'min': np.min(scores) if scores else 0.0,
                'max': np.max(scores) if scores else 0.0,
                'agreement': 'high' if (np.std(scores) < 0.1 if len(scores) > 1 else True) else 'low'
            }
        }
        
        return comparison
    
    def _calculate_confidence(self, score: float, tag_count: int) -> str:
        """Calculate confidence level based on score and tag count."""
        if tag_count < 3:
            return 'low'
        elif tag_count < 10:
            confidence_base = 'medium'
        else:
            confidence_base = 'high'
        
        # Adjust based on score extremes
        if 0.1 < score < 0.9:
            return confidence_base
        else:
            return 'high' if confidence_base == 'high' else 'medium'
    
    def _explain_prediction(self, model, tags: List[Tuple[str, float]]) -> Dict[str, Any]:
        """Generate explanation for prediction (LightGBM only)."""
        try:
            if not hasattr(model, 'get_feature_importance'):
                return None
            
            importance = model.get_feature_importance()
            
            # Find relevant features for this prompt
            tag_names = [tag for tag, weight in tags]
            relevant_features = {}
            
            for feature_name, importance_score in list(importance.items())[:20]:
                # Check if any of our tags match this feature
                for tag in tag_names:
                    if tag in feature_name.lower() or feature_name.lower() in tag:
                        relevant_features[feature_name] = importance_score
                        break
            
            return {
                'top_influential_features': dict(list(importance.items())[:10]),
                'relevant_to_prompt': relevant_features
            }
            
        except Exception as e:
            logger.warning(f"Could not generate explanation: {e}")
            return None


def load_prompts_from_file(file_path: str) -> List[str]:
    """Load prompts from text file (one per line)."""
    prompts = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):  # Skip empty lines and comments
                    prompts.append(line)
        
        logger.info(f"Loaded {len(prompts)} prompts from {file_path}")
        return prompts
        
    except Exception as e:
        logger.error(f"Error loading prompts from file: {e}")
        raise


def save_results(results: Union[Dict, List[Dict]], output_path: str):
    """Save prediction results to JSON file."""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Results saved to {output_path}")
        
    except Exception as e:
        logger.error(f"Error saving results: {e}")
        raise


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description='Predict Prompt Goodness Scores')

    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--prompt', type=str, help='Single prompt to predict')
    input_group.add_argument('--input-file', type=str, help='File containing prompts (one per line)')

    # Model options
    model_group = parser.add_mutually_exclusive_group(required=True)
    model_group.add_argument('--model', type=str, help='Path to trained model file')
    model_group.add_argument('--compare-models', nargs='+', help='Paths to multiple models for comparison')

    # Output options
    parser.add_argument('--output', type=str, help='Output file for results (JSON format)')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--show-explanation', action='store_true', help='Show prediction explanations')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Initialize predictor
        predictor = GoodnessPredictor()

        # Load models
        if args.model:
            model_name = predictor.load_model(args.model)
            model_names = [model_name]
        else:
            model_names = []
            for i, model_path in enumerate(args.compare_models):
                model_name = predictor.load_model(model_path, f"model_{i+1}")
                model_names.append(model_name)

        # Get prompts
        if args.prompt:
            prompts = [args.prompt]
            single_prompt = True
        else:
            prompts = load_prompts_from_file(args.input_file)
            single_prompt = False

        # Make predictions
        if len(model_names) == 1:
            # Single model prediction
            model_name = model_names[0]

            if single_prompt:
                result = predictor.predict_single(prompts[0], model_name)
                results = result
            else:
                results = predictor.predict_batch(prompts, model_name)
        else:
            # Model comparison
            if single_prompt:
                result = predictor.compare_models(prompts[0], model_names)
                results = result
            else:
                # Compare models for each prompt
                results = []
                for prompt in prompts:
                    comparison = predictor.compare_models(prompt, model_names)
                    results.append(comparison)

        # Display results
        if single_prompt and len(model_names) == 1:
            # Single prompt, single model
            print("\n" + "="*60)
            print("GOODNESS PREDICTION RESULT")
            print("="*60)
            print(f"Prompt: {results['prompt']}")
            print(f"Goodness Score: {results['score']:.4f}")
            print(f"Confidence: {results['confidence']}")
            print(f"Tags Found: {results['tags_found']}")

            if 'parsed_tags' in results:
                print(f"\nParsed Tags (first 10):")
                for tag, weight in results['parsed_tags']:
                    print(f"  {tag}: {weight:.2f}")

            if args.show_explanation and 'explanation' in results:
                print(f"\nPrediction Explanation:")
                exp = results['explanation']
                if 'top_influential_features' in exp:
                    print("Top Influential Features:")
                    for feature, importance in list(exp['top_influential_features'].items())[:5]:
                        print(f"  {feature}: {importance:.4f}")

        elif single_prompt and len(model_names) > 1:
            # Single prompt, multiple models
            print("\n" + "="*60)
            print("MODEL COMPARISON RESULT")
            print("="*60)
            print(f"Prompt: {results['prompt']}")

            print(f"\nModel Predictions:")
            for model_name, prediction in results['model_predictions'].items():
                print(f"  {model_name}: {prediction['score']:.4f} (confidence: {prediction['confidence']})")

            stats = results['score_statistics']
            print(f"\nScore Statistics:")
            print(f"  Mean: {stats['mean']:.4f}")
            print(f"  Std Dev: {stats['std']:.4f}")
            print(f"  Range: {stats['min']:.4f} - {stats['max']:.4f}")
            print(f"  Agreement: {stats['agreement']}")

        else:
            # Multiple prompts
            print(f"\nProcessed {len(prompts)} prompts")

            if len(model_names) == 1:
                scores = [r['score'] for r in results]
                print(f"Average Score: {np.mean(scores):.4f}")
                print(f"Score Range: {np.min(scores):.4f} - {np.max(scores):.4f}")

                # Show top and bottom predictions
                sorted_results = sorted(results, key=lambda x: x['score'], reverse=True)

                print(f"\nTop 3 Predictions:")
                for i, result in enumerate(sorted_results[:3], 1):
                    print(f"  {i}. {result['prompt'][:50]}... -> {result['score']:.4f}")

                print(f"\nBottom 3 Predictions:")
                for i, result in enumerate(sorted_results[-3:], 1):
                    print(f"  {i}. {result['prompt'][:50]}... -> {result['score']:.4f}")

        # Save results if requested
        if args.output:
            save_results(results, args.output)
            print(f"\nResults saved to: {args.output}")

    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

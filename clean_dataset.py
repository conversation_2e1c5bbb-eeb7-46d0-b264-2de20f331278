#!/usr/bin/env python3
"""
Dataset Cleaning Script

This script cleans the dataset by:
1. Removing duplicate images (keeping base-level, removing subfolder copies)
2. Filtering out irrelevant prompts (no "1girl" or "2girls" tags)
3. Generating a cleaned dataset with statistics

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import logging
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import List, Tuple, Dict, Set, Any
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'dataset_cleaning_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DatasetCleaner:
    """Cleans dataset by removing duplicates and irrelevant prompts."""
    
    def __init__(self, input_path: str = "production_dataset.pkl", output_path: str = "cleaned_dataset.pkl"):
        """Initialize the cleaner."""
        self.input_path = input_path
        self.output_path = output_path
        self.dataset = None
        self.cleaning_stats = {}
        
    def load_dataset(self) -> bool:
        """Load the dataset from pickle file."""
        try:
            logger.info(f"Loading dataset from {self.input_path}...")
            with open(self.input_path, 'rb') as f:
                self.dataset = dill.load(f)
            logger.info(f"Successfully loaded {len(self.dataset)} entries")
            return True
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            return False
    
    def analyze_duplicates(self) -> Dict[str, Any]:
        """Analyze duplicate patterns in the dataset."""
        logger.info("Analyzing duplicate patterns...")
        
        duplicate_groups = defaultdict(list)
        base_level_images = []
        subfolder_images = []
        
        for i, (filename, tags, goodness_score) in enumerate(self.dataset):
            base_filename = Path(filename).name
            
            # Group by base filename
            duplicate_groups[base_filename].append({
                'index': i,
                'full_path': filename,
                'goodness_score': goodness_score,
                'tags_count': len(tags)
            })
            
            # Analyze path structure
            path_parts = Path(filename).parts
            
            # Look for date folder patterns
            date_pattern = None
            for part in path_parts:
                if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                    date_pattern = part
                    break
            
            if date_pattern and date_pattern in path_parts:
                date_index = path_parts.index(date_pattern)
                remaining_parts = path_parts[date_index+1:]
                
                # Check if it's a base level image
                if len(remaining_parts) == 1 and remaining_parts[0].startswith('ComfyUI_'):
                    base_level_images.append(i)
                else:
                    subfolder_images.append(i)
        
        # Find actual duplicates
        actual_duplicates = {name: entries for name, entries in duplicate_groups.items() if len(entries) > 1}
        
        analysis = {
            'total_entries': len(self.dataset),
            'unique_filenames': len(duplicate_groups),
            'duplicate_groups': actual_duplicates,
            'duplicate_count': len(actual_duplicates),
            'base_level_count': len(base_level_images),
            'subfolder_count': len(subfolder_images),
            'base_level_indices': set(base_level_images),
            'subfolder_indices': set(subfolder_images)
        }
        
        logger.info(f"Found {analysis['duplicate_count']} duplicate groups")
        logger.info(f"Base-level images: {analysis['base_level_count']}")
        logger.info(f"Subfolder images: {analysis['subfolder_count']}")
        
        return analysis
    
    def analyze_relevance(self) -> Dict[str, Any]:
        """Analyze tag relevance (1girl/2girls filter)."""
        logger.info("Analyzing tag relevance...")
        
        relevant_indices = []
        irrelevant_indices = []
        
        for i, (filename, tags, goodness_score) in enumerate(self.dataset):
            # Extract tag names (ignore weights)
            tag_names = [tag.lower() for tag, weight in tags]
            
            # Check for relevance
            is_relevant = any(tag in tag_names for tag in ['1girl', '2girls'])
            
            if is_relevant:
                relevant_indices.append(i)
            else:
                irrelevant_indices.append(i)
        
        analysis = {
            'relevant_indices': set(relevant_indices),
            'irrelevant_indices': set(irrelevant_indices),
            'relevant_count': len(relevant_indices),
            'irrelevant_count': len(irrelevant_indices)
        }
        
        logger.info(f"Relevant entries: {analysis['relevant_count']}")
        logger.info(f"Irrelevant entries: {analysis['irrelevant_count']}")
        
        return analysis
    
    def generate_cleaning_plan(self, duplicate_analysis: Dict, relevance_analysis: Dict) -> Set[int]:
        """Generate indices of entries to remove."""
        logger.info("Generating cleaning plan...")
        
        indices_to_remove = set()
        
        # Step 1: Handle duplicates - keep base-level, remove subfolder
        for filename, duplicate_entries in duplicate_analysis['duplicate_groups'].items():
            base_level_entries = []
            subfolder_entries = []
            
            for entry in duplicate_entries:
                if entry['index'] in duplicate_analysis['base_level_indices']:
                    base_level_entries.append(entry)
                else:
                    subfolder_entries.append(entry)
            
            # Keep base-level, remove subfolder
            if base_level_entries:
                # Keep the first base-level entry, remove the rest
                for entry in base_level_entries[1:]:
                    indices_to_remove.add(entry['index'])
                for entry in subfolder_entries:
                    indices_to_remove.add(entry['index'])
            else:
                # No base-level entries, keep the first subfolder entry
                for entry in subfolder_entries[1:]:
                    indices_to_remove.add(entry['index'])
        
        # Step 2: Remove irrelevant entries (but don't double-count)
        irrelevant_to_remove = relevance_analysis['irrelevant_indices'] - indices_to_remove
        indices_to_remove.update(irrelevant_to_remove)
        
        logger.info(f"Total entries to remove: {len(indices_to_remove)}")
        
        return indices_to_remove
    
    def create_cleaned_dataset(self, indices_to_remove: Set[int]) -> List[Tuple]:
        """Create the cleaned dataset by removing specified indices."""
        logger.info("Creating cleaned dataset...")
        
        cleaned_dataset = []
        
        for i, entry in enumerate(self.dataset):
            if i not in indices_to_remove:
                cleaned_dataset.append(entry)
        
        logger.info(f"Cleaned dataset size: {len(cleaned_dataset)}")
        
        return cleaned_dataset
    
    def save_cleaned_dataset(self, cleaned_dataset: List[Tuple]) -> bool:
        """Save the cleaned dataset to file."""
        try:
            # Create backup if output file exists
            if os.path.exists(self.output_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{self.output_path}.backup_{timestamp}"
                os.rename(self.output_path, backup_path)
                logger.info(f"Created backup: {backup_path}")
            
            # Save cleaned dataset
            logger.info(f"Saving cleaned dataset to {self.output_path}...")
            with open(self.output_path, 'wb') as f:
                dill.dump(cleaned_dataset, f)
            
            logger.info(f"Successfully saved {len(cleaned_dataset)} entries")
            return True
            
        except Exception as e:
            logger.error(f"Error saving cleaned dataset: {e}")
            return False
    
    def print_cleaning_summary(self, duplicate_analysis: Dict, relevance_analysis: Dict, indices_to_remove: Set[int], final_size: int):
        """Print a comprehensive cleaning summary."""
        logger.info("\n" + "="*80)
        logger.info("DATASET CLEANING SUMMARY")
        logger.info("="*80)
        
        original_size = duplicate_analysis['total_entries']
        removed_count = len(indices_to_remove)
        reduction_percent = (removed_count / original_size) * 100
        
        logger.info(f"\nORIGINAL DATASET:")
        logger.info(f"  Total entries: {original_size:,}")
        logger.info(f"  Unique filenames: {duplicate_analysis['unique_filenames']:,}")
        logger.info(f"  Duplicate groups: {duplicate_analysis['duplicate_count']:,}")
        logger.info(f"  Base-level images: {duplicate_analysis['base_level_count']:,}")
        logger.info(f"  Subfolder images: {duplicate_analysis['subfolder_count']:,}")
        
        logger.info(f"\nRELEVANCE ANALYSIS:")
        logger.info(f"  Relevant entries (1girl/2girls): {relevance_analysis['relevant_count']:,}")
        logger.info(f"  Irrelevant entries: {relevance_analysis['irrelevant_count']:,}")
        
        logger.info(f"\nCLEANING RESULTS:")
        logger.info(f"  Entries removed: {removed_count:,}")
        logger.info(f"  Final dataset size: {final_size:,}")
        logger.info(f"  Reduction: {reduction_percent:.1f}%")
        
        # Calculate breakdown of removals
        duplicate_removals = 0
        for filename, duplicate_entries in duplicate_analysis['duplicate_groups'].items():
            duplicate_removals += len(duplicate_entries) - 1  # Keep one, remove the rest
        
        irrelevant_removals = len(relevance_analysis['irrelevant_indices'])
        overlap = duplicate_removals + irrelevant_removals - removed_count
        
        logger.info(f"\nREMOVAL BREAKDOWN:")
        logger.info(f"  Duplicate removals: {duplicate_removals:,}")
        logger.info(f"  Irrelevant removals: {irrelevant_removals:,}")
        logger.info(f"  Overlap (duplicates that were also irrelevant): {overlap:,}")
    
    def clean_dataset(self) -> bool:
        """Run the complete dataset cleaning process."""
        logger.info("Starting dataset cleaning process...")
        
        # Load dataset
        if not self.load_dataset():
            return False
        
        # Analyze duplicates and relevance
        duplicate_analysis = self.analyze_duplicates()
        relevance_analysis = self.analyze_relevance()
        
        # Generate cleaning plan
        indices_to_remove = self.generate_cleaning_plan(duplicate_analysis, relevance_analysis)
        
        # Create cleaned dataset
        cleaned_dataset = self.create_cleaned_dataset(indices_to_remove)
        
        # Save cleaned dataset
        if not self.save_cleaned_dataset(cleaned_dataset):
            return False
        
        # Print summary
        self.print_cleaning_summary(duplicate_analysis, relevance_analysis, indices_to_remove, len(cleaned_dataset))
        
        logger.info("\nDataset cleaning completed successfully!")
        return True


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clean dataset by removing duplicates and irrelevant prompts")
    parser.add_argument("--input", default="production_dataset.pkl", help="Input dataset pickle file")
    parser.add_argument("--output", default="cleaned_dataset.pkl", help="Output cleaned dataset pickle file")
    
    args = parser.parse_args()
    
    cleaner = DatasetCleaner(args.input, args.output)
    success = cleaner.clean_dataset()
    
    if success:
        logger.info("Dataset cleaning completed successfully!")
        return 0
    else:
        logger.error("Dataset cleaning failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())

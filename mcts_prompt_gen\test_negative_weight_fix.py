#!/usr/bin/env python3
"""
Test script to verify that negative weight parsing is working correctly.
"""

import os
import sys

# Add src directory to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

from corrected_prompt_parser import CorrectedPromptParser
from fixed_prompt_parser import FixedPromptParser


def test_negative_weight_parsing():
    """Test that negative weights in LoRA parameters are parsed correctly."""
    print("TESTING NEGATIVE WEIGHT PARSING")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "Negative LoRA weight",
            "prompt": "1girl, masterpiece --lora outline_xl_kohaku_delta_spv5x.safetensors:-1.0",
            "expected_lora": "lora_outline_xl_kohaku_delta_spv5x",
            "expected_weight": -1.0
        },
        {
            "name": "Positive LoRA weight",
            "prompt": "portrait --lora character_model.safetensors:0.8",
            "expected_lora": "lora_character_model",
            "expected_weight": 0.8
        },
        {
            "name": "Multiple LoRAs with mixed weights",
            "prompt": "art --lora style.safetensors:1.2 --lora negative_style.safetensors:-0.5",
            "expected_loras": [
                ("lora_style", 1.2),
                ("lora_negative_style", -0.5)
            ]
        }
    ]
    
    parsers = [
        ("CorrectedPromptParser", CorrectedPromptParser()),
        ("FixedPromptParser", FixedPromptParser())
    ]
    
    for parser_name, parser in parsers:
        print(f"\n{'-'*40}")
        print(f"Testing {parser_name}")
        print(f"{'-'*40}")
        
        for test_case in test_cases:
            print(f"\nTest: {test_case['name']}")
            print(f"Prompt: {test_case['prompt']}")
            
            # Parse the prompt
            if parser_name == "CorrectedPromptParser":
                result = parser.parse_prompt_corrected(test_case['prompt'])
            else:
                result = parser.parse_prompt_with_fixed_logic(test_case['prompt'])
            
            # Extract LoRA tags from result
            lora_tags = [(tag, weight) for tag, weight in result if tag.startswith('lora_')]
            
            print(f"Found LoRA tags: {lora_tags}")
            
            if 'expected_loras' in test_case:
                # Multiple LoRAs test
                success = True
                for expected_tag, expected_weight in test_case['expected_loras']:
                    found = False
                    for tag, weight in lora_tags:
                        if tag == expected_tag and abs(weight - expected_weight) < 0.01:
                            print(f"✅ FOUND: {tag} with weight {weight}")
                            found = True
                            break
                    if not found:
                        print(f"❌ MISSING: {expected_tag} with weight {expected_weight}")
                        success = False
                
                if success:
                    print("✅ SUCCESS: All LoRAs found with correct weights")
                else:
                    print("❌ FAILURE: Some LoRAs missing or incorrect weights")
            
            else:
                # Single LoRA test
                expected_tag = test_case['expected_lora']
                expected_weight = test_case['expected_weight']
                
                found = False
                for tag, weight in lora_tags:
                    if tag == expected_tag and abs(weight - expected_weight) < 0.01:
                        print(f"✅ SUCCESS: Found {tag} with correct weight {weight}")
                        found = True
                        break
                
                if not found:
                    print(f"❌ FAILURE: Expected {expected_tag} with weight {expected_weight}")
                    print(f"   Available: {lora_tags}")


if __name__ == "__main__":
    test_negative_weight_parsing()

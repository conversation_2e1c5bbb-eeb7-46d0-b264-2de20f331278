import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import argparse
import os
from transformers import AutoTokenizer

from models import ResNetModel, LightGBMModel, LLMWrapper
from mcts import MCTS
from environment import PromptEnvironment
from data import load_data, RewardModel

def train_resnet(model, train_loader, val_loader, epochs=10, lr=0.001, device="cuda"):
    """Train ResNet model on prompt data."""
    model = model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=lr)
    policy_criterion = nn.CrossEntropyLoss()
    value_criterion = nn.MSELoss()
    
    for epoch in range(epochs):
        model.train()
        train_policy_loss = 0
        train_value_loss = 0
        
        for batch in train_loader:
            features = batch['features'].to(device)
            tokens = batch['tokens'].to(device)
            scores = batch['score'].to(device)
            
            # Forward pass
            policy, value = model(features)
            
            # Calculate policy loss (using next token as target)
            policy_targets = tokens[:, 1:]  # Next tokens are targets
            policy_loss = policy_criterion(policy.view(-1, policy.size(-1)), policy_targets.view(-1))
            
            # Calculate value loss
            value_loss = value_criterion(value.squeeze(), scores)
            
            # Total loss
            loss = policy_loss + value_loss
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_policy_loss += policy_loss.item()
            train_value_loss += value_loss.item()
        
        # Validation
        model.eval()
        val_policy_loss = 0
        val_value_loss = 0
        
        with torch.no_grad():
            for batch in val_loader:
                features = batch['features'].to(device)
                tokens = batch['tokens'].to(device)
                scores = batch['score'].to(device)
                
                policy, value = model(features)
                
                policy_targets = tokens[:, 1:]
                policy_loss = policy_criterion(policy.view(-1, policy.size(-1)), policy_targets.view(-1))
                value_loss = value_criterion(value.squeeze(), scores)
                
                val_policy_loss += policy_loss.item()
                val_value_loss += value_loss.item()
        
        print(f"Epoch {epoch+1}/{epochs}")
        print(f"Train - Policy Loss: {train_policy_loss/len(train_loader):.4f}, Value Loss: {train_value_loss/len(train_loader):.4f}")
        print(f"Val - Policy Loss: {val_policy_loss/len(val_loader):.4f}, Value Loss: {val_value_loss/len(val_loader):.4f}")
    
    return model

def train_lightgbm(train_loader, val_loader):
    """Train LightGBM model on prompt data."""
    # Prepare data
    X_train, policy_train, value_train = [], [], []
    X_val, policy_val, value_val = [], [], []
    
    for batch in train_loader:
        features = batch['features'].numpy()
        tokens = batch['tokens'].numpy()
        scores = batch['score'].numpy()
        
        X_train.extend(features)
        policy_train.extend(tokens[:, 1:])  # Next tokens as targets
        value_train.extend(scores)
    
    for batch in val_loader:
        features = batch['features'].numpy()
        tokens = batch['tokens'].numpy()
        scores = batch['score'].numpy()
        
        X_val.extend(features)
        policy_val.extend(tokens[:, 1:])
        value_val.extend(scores)
    
    # Convert to numpy arrays
    X_train = np.array(X_train)
    policy_train = np.array(policy_train)
    value_train = np.array(value_train)
    X_val = np.array(X_val)
    policy_val = np.array(policy_val)
    value_val = np.array(value_val)
    
    # Train model
    model = LightGBMModel()
    model.train(X_train, policy_train, value_train)
    
    # Evaluate
    policy_pred, value_pred = model.predict(X_val)
    policy_accuracy = np.mean(np.argmax(policy_pred, axis=1) == np.argmax(policy_val, axis=1))
    value_mse = np.mean((value_pred - value_val) ** 2)
    
    print(f"LightGBM - Policy Accuracy: {policy_accuracy:.4f}, Value MSE: {value_mse:.4f}")
    
    return model

def main():
    parser = argparse.ArgumentParser(description="Train prompt generation models")
    parser.add_argument("--data_path", type=str, required=True, help="Path to prompt dataset")
    parser.add_argument("--model_type", type=str, default="resnet", choices=["resnet", "lightgbm", "llm"], 
                        help="Type of model to train")
    parser.add_argument("--use_mcts", action="store_true", help="Use MCTS for generation")
    parser.add_argument("--epochs", type=int, default=10, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=32, help="Batch size for training")
    parser.add_argument("--lr", type=float, default=0.001, help="Learning rate")
    parser.add_argument("--max_length", type=int, default=50, help="Maximum prompt length")
    parser.add_argument("--output_dir", type=str, default="./output", help="Output directory for models")
    parser.add_argument("--llm_model", type=str, default="gpt2", help="Pretrained LLM model name")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained(args.llm_model)
    
    # Load data
    train_loader, val_loader = load_data(
        args.data_path, tokenizer, args.batch_size, args.max_length
    )
    
    # Initialize model
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    if args.model_type == "resnet":
        # Assuming input dimension is max_length * vocab_size
        input_dim = args.max_length * tokenizer.vocab_size
        model = ResNetModel(input_dim)
        model = train_resnet(model, train_loader, val_loader, args.epochs, args.lr, device)
        torch.save(model.state_dict(), os.path.join(args.output_dir, "resnet_model.pt"))
    
    elif args.model_type == "lightgbm":
        model = train_lightgbm(train_loader, val_loader)
        # LightGBM models are saved within the class
    
    elif args.model_type == "llm":
        model = LLMWrapper(args.llm_model, device)
        # No training needed for pretrained LLM
    
    # Initialize environment and reward model
    reward_model = RewardModel()
    env = PromptEnvironment(tokenizer, reward_model, args.max_length)
    
    # Generate sample prompts
    if args.use_mcts:
        mcts = MCTS(model, num_simulations=100)
        prompt = env.generate_prompt(model, mcts=mcts, temperature=0.8)
    else:
        prompt = env.generate_prompt(model, temperature=0.8)
    
    print("Generated prompt:", prompt)

if __name__ == "__main__":
    main()
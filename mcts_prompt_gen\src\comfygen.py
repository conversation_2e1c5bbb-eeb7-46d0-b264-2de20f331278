import typer
from typing_extensions import Annotated

import json
from urllib import request, parse
import random
import os
import sys
from PIL import Image
import psd_tools
# from psd_tools.api.layers import Pixel<PERSON>ayer
# from psd_tools.composite import composite, composite_pil
import numpy
import copy
import utils
import re

def queue_prompt(prompt_workflow):
    p = {"prompt": prompt_workflow}
    data = json.dumps(p).encode('utf-8')
    req =  request.Request("http://127.0.0.1:8188/prompt", data=data)
    request.urlopen(req)

def prepare_workflow(prompt, lora, output, model, w=1024, h=1024):
    memos = {"sampler":"12", "model":"4", "prompt":"6", "neg_prompt":"7", "resolution":"30", "lora":"41", "output":"9"}
    script_directory = os.path.dirname(os.path.abspath(sys.argv[0]))
    workflow = json.load(open(script_directory + '/res/' + "comfygen.json"))
    workflow[memos["model"]]["inputs"]["ckpt_name"] = model
    workflow[memos["resolution"]]["inputs"]["width"] = w
    workflow[memos["resolution"]]["inputs"]["height"] = h
    workflow[memos["prompt"]]["inputs"]["text"] = prompt
    workflow[memos["output"]]["inputs"]["filename_prefix"] = output
    workflow[memos["resolution"]]["inputs"]["batch_size"] = 1
    workflow[memos["lora"]]["inputs"]["text"] = lora
    workflow[memos["sampler"]]["inputs"]["seed"] = random.randint(0, 18446744073709551614)
    workflow[memos["sampler"]]["inputs"]["steps"] = random.randint(30, 45)
    workflow[memos["sampler"]]["inputs"]["cfg"] = random.normalvariate(5, 1.0).__round__(1)
    return workflow

parts = {
    "character": {},
    "pose": {},
    "expression": {},
    "clothing": {},
    "composition": {},
    "background": {},
    "style": {},
    "quality": {},
}
parts["character"]['yixuan'] = (["<lora:ch\\Yi_Xuan_IL_v2.safetensors:1.0>"], ["yellow eyes", "white hair", "mole", "long hair", "bangs", "hair ornament"], ["YiXuan"])
parts["character"]['cartethyia'] = (["<lora:ch\\cartethyia-wwIL-v1F.safetensors:1.0>"], ["braid", "earrings"], ["cartethyia \\(kr1st\\)"])
parts["character"]['phoebe'] = (["<lora:ch\phoebe-wwIL-v1.safetensors:1.0>"], ["blond hair", "white hat", "white pantyhose"], ["phoebe"])

parts["pose"]['standing'] = ([], ["standing"], [])
parts["pose"]['sitting'] = ([], ["sitting"], [])
parts["pose"]['lying'] = ([], ["lying"], [])
parts["pose"]['foot'] = ([], ["lying", "no shoes", "foreshortening", "on side", "index finger raised", "finger to mouth"], [])
parts["pose"]['vv'] = ([], [":d", "one eye closed", "v over eye", "hand on own hip", "star \\(symbol\\)", "contrapposto"], [])
parts["expression"]['ssmile'] = ([], ["seductive_smile", "come_hither"], [])
parts["expression"]['exp'] = ([], ["expressionless"], [])
parts["clothing"]['yixuan'] = ([], ["jacket", "yellow jacket", "gloves", "black gloves", "fingerless gloves", "black shorts"], [])
parts["clothing"]['suits'] = ([], ["suits", "black suits", "tie", "necktie", "long pants", "frills"], [])
parts["composition"]['full'] = ([], ["full body"], [])
parts["composition"]['upper'] = ([], ["upper body"], [])
parts["composition"]['portrait'] = ([], ["portrait"], [])
parts["composition"]['cowboy'] = ([], ["cowboy shot"], [])
parts["background"]['street'] = ([], ["street", "outdoors"], [])
parts["background"]['bedroom'] = ([], ["bedroom", "indoors"], [])
parts["background"]['grey'] = ([], ["grey background", "polka_dot_background"], [])
parts["style"]['watercolor'] = (["<lora:sty\\ILwatercolor.safetensors:1.0>", "<lora:sty\\shuicai.safetensors:1.0>"], [], ["watercolor (medium)", "shuicai", "watercolor"])
parts["style"]['t1kosewad'] = (["<lora:art\\t1kosewad_style.safetensors:0.6>"], [], ["t1kosewad"])
parts["style"]['a31'] = (["<lora:a31_style_koni-000010.safetensors:1.0>"], [], [])
parts["style"]['ink'] = (["<lora:sty\\yi-noob-000005.safetensors:1.0>"], [], ["ink wash painting"])
parts["style"]['cute'] = (["<lora:sty\\Niji_cute_style_illustrious.safetensors:1.0>"], [], ["Cute_niji_style"])
parts["quality"]['high'] = ([], ["masterpiece, best quality, absurdres, amazing quality, very aesthetic, best composition"], [])
parts["quality"]['spo'] = (["<lora:acc\\spo_sdxl_10ep_4k-data_lora_webui.safetensors:1.0>"], ["masterpiece, best quality, absurdres, amazing quality, very aesthetic, best composition"], [])

def add_part(lora, prompt, part, idx=-1, cnt=1):
    if cnt == -1:
        cnt = random.randint(1, len(parts[part]))
    cnt = min(cnt, len(parts[part]))
    ids = random.sample(range(len(parts[part])), cnt)
    for idx in ids:
        idx = sorted(parts[part].keys())[idx]
        for l in parts[part][idx][0]:
            w = random.normalvariate(1, 0.1)
            w = max(0.01, min(2.0, w))
            if l not in lora:
                lora.append(l[:-4] + f"{w:.2f}>")
        prompt = parts[part][idx][2] + prompt + parts[part][idx][1]
    return lora, prompt

def main(
    output: Annotated[str, typer.Option("-o")] = "", 
    model: Annotated[str, typer.Option("-m")] = "shiitakeMix_v20.safetensors",
    count: Annotated[int, typer.Option("-n")] = 1
    ):

    if len(output) == 0:
        # "2025-06-21/ComfyUI"
        import datetime
        output = datetime.datetime.now().strftime("%Y-%m-%d") + "/" + "ComfyUI"

    for i in range(count):
        lora, prompt = [], ['1girl']

        lora, prompt = add_part(lora, prompt, "character")
        lora, prompt = add_part(lora, prompt, "pose")
        lora, prompt = add_part(lora, prompt, "expression")
        lora, prompt = add_part(lora, prompt, "clothing")
        lora, prompt = add_part(lora, prompt, "composition")
        lora, prompt = add_part(lora, prompt, "background")
        lora, prompt = add_part(lora, prompt, "style", cnt=-1)
        lora, prompt = add_part(lora, prompt, "quality", cnt=2)

        lora, prompt = ','.join(lora), ', '.join(prompt)
        workflow = prepare_workflow(prompt, lora, output, model)
        queue_prompt(workflow)


if __name__ == "__main__":
    import time
    random.seed(time.time())
    typer.run(main)
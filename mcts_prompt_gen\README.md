# MCTS Prompt Generation

This project implements an AlphaZero-style approach to prompt generation using Monte Carlo Tree Search (MCTS) and various model architectures. The system can train models to generate high-quality prompts, evaluate their performance, and generate new prompts using different approaches.

## Features

- Multiple model architectures:
  - ResNet-based neural network
  - LightGBM gradient boosting
  - Pretrained language models (LLM)
- Monte Carlo Tree Search (MCTS) for improved prompt generation
- Training, evaluation, and generation modes
- Customizable parameters for prompt length, temperature, etc.

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/mcts-prompt-gen.git
cd mcts-prompt-gen
```

2. Create a virtual environment and install dependencies:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

## Requirements

The following packages are required:
- torch
- transformers
- numpy
- lightgbm
- tqdm
- matplotlib (for visualization)

You can install them using the provided `requirements.txt` file.

## Usage

The system has three main modes of operation:

1. **Training**: Train a model on a dataset of prompts
2. **Evaluation**: Evaluate model performance with or without MCTS
3. **Generation**: Generate new prompts using a trained model

### Training a Model

To train a ResNet model:

```bash
python main.py --mode train --model_type resnet --data_path data/prompts.json --epochs 20 --batch_size 64 --lr 0.001 --max_length 50 --model_dir ./models
```

To train a LightGBM model:

```bash
python main.py --mode train --model_type lightgbm --data_path data/prompts.json --batch_size 64 --max_length 50 --model_dir ./models
```

### Evaluating Models

To evaluate a ResNet model with MCTS:

```bash
python main.py --mode evaluate --model_type resnet --model_dir ./models --use_mcts --num_prompts 20 --temperature 0.8 --output_file results/resnet_mcts.json
```

To evaluate a LightGBM model without MCTS:

```bash
python main.py --mode evaluate --model_type lightgbm --model_dir ./models --num_prompts 20 --temperature 0.8 --output_file results/lightgbm_no_mcts.json
```

To evaluate a pretrained LLM:

```bash
python main.py --mode evaluate --model_type llm --llm_model gpt2 --num_prompts 10 --temperature 0.8 --output_file results/llm_results.json
```

### Generating Prompts

To generate prompts using a ResNet model with MCTS:

```bash
python main.py --mode generate --model_type resnet --model_dir ./models --use_mcts --num_prompts 5 --temperature 0.8 --output_file generated/resnet_mcts_prompts.json
```

To generate prompts using a pretrained LLM without MCTS:

```bash
python main.py --mode generate --model_type llm --llm_model gpt2 --num_prompts 5 --temperature 1.0 --output_file generated/llm_prompts.json
```

To generate prompts with an initial prompt:

```bash
python main.py --mode generate --model_type llm --llm_model gpt2 --initial_prompt "Once upon a time" --num_prompts 3 --temperature 0.9 --output_file generated/story_prompts.json
```

## Data Format

The training data should be a JSON file with the following structure:

```json
[
  {
    "prompt": "This is a sample prompt",
    "score": 0.85
  },
  {
    "prompt": "Another example prompt",
    "score": 0.72
  },
  ...
]
```

Each entry contains a prompt and its associated quality score (between 0 and 1).

## How It Works

### MCTS Approach

The Monte Carlo Tree Search (MCTS) algorithm is used to explore the space of possible prompts more effectively:

1. **Selection**: Starting from the root state (empty or partial prompt), the algorithm traverses the tree by selecting actions that maximize the Upper Confidence Bound (UCB) score.
2. **Expansion**: When a leaf node is reached, it's expanded by adding child nodes for possible next tokens.
3. **Simulation**: The value of the new state is estimated using the trained model.
4. **Backpropagation**: The value is propagated back up the tree to update node statistics.

This approach allows the system to balance exploration and exploitation, leading to higher-quality prompts compared to greedy or random sampling.

### Model Architectures

1. **ResNet**: A neural network with residual connections that predicts both policy (next token probabilities) and value (prompt quality).
2. **LightGBM**: A gradient boosting framework that can handle high-dimensional sparse data efficiently.
3. **LLM**: Pretrained language models like GPT-2 that can generate coherent text based on context.

## Extending the System

You can extend the system in several ways:

- Add new model architectures
- Implement more sophisticated reward models
- Add support for different types of prompts (e.g., image generation, code generation)
- Implement parallel MCTS for faster search

## License

This project is licensed under the MIT License - see the LICENSE file for details.
#!/usr/bin/env python3
"""
Production-Ready Dataset Construction Script

This script implements a comprehensive, production-ready dataset construction system
that reads directly from original image folders and constructs prompt data from scratch
using all the critical fixes developed.

Key Features:
- Clean implementation reading from original image folders
- Corrected LoRA extraction with filename preservation
- Special LoRA format: <lora:filename>:weight syntax
- Negative weight support for LoRA parameters
- Comprehensive validation against test cases
- Parallel processing with progress tracking
- Final format: (filename, [(tag, weight) tuples], goodness_score)

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import logging
import argparse
import pickle
import dill
from datetime import datetime
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional, Set
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import threading
import time

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PIL import Image
from utils import image_info, extract_comfyui_workflow_params, traverse_workflow_graph
from corrected_prompt_parser import CorrectedPromptParser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_dataset_builder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ProductionDatasetBuilder:
    """
    Production-ready dataset builder with comprehensive error handling and validation.
    """
    
    def __init__(self, max_workers: int = 8):
        """Initialize the dataset builder."""
        self.max_workers = max_workers
        self.parser = CorrectedPromptParser()
        
        # Default input directories
        self.input_directories = [
            r"F:\SD-webui\ComfyUI\output",
            r"F:\SD-webui\gallery\server",
            r"F:\SD-webui\ComfyUI2\output",
            r"F:\SD-webui\ComfyUI_windows_portable_old\ComfyUI\output",
            r"F:\SD-webui\ComfyUI_windows_portable_nvidia_cu121_or_cpu_03_03_2024\ComfyUI_windows_portable\ComfyUI\output",
            r"F:\Code\PlayGround\yeflib\results"
        ]
        
        # Statistics tracking
        self.stats = {
            'total_images_found': 0,
            'total_images_processed': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'good_quality_images': 0,
            'normal_quality_images': 0,
            'lora_extractions': 0,
            'negative_weight_loras': 0,
            'processing_errors': 0
        }
        
        # Thread-safe logging
        self.log_lock = threading.Lock()
        self.error_log = []
        
        # Test cases for validation
        self.validation_test_cases = [
            {
                "path": r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png",
                "expected_loras": ["luce2_Noob75XL.safetensors:0.6"],
                "description": "ComfyUI format - single LoRA"
            },
            {
                "path": r"F:\SD-webui\ComfyUI\output\2025-04-22\ComfyUI_00212_.png", 
                "expected_loras": ["prts_sn59.safetensors:0.8"],
                "description": "ComfyUI format - character LoRA"
            },
            {
                "path": r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png",
                "expected_loras": [
                    "a31_style_koni-000010.safetensors:0.8",
                    "outline_xl_kohaku_delta_spv5x.safetensors:-1.0", 
                    "Hoseki_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9"
                ],
                "description": "ComfyUI format - multiple complex LoRAs with negative weights"
            }
        ]
    
    def is_image_file(self, filename: str) -> bool:
        """Check if file is a supported image format."""
        return filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp', '.bmp'))
    
    def determine_quality_label(self, image_path: str) -> str:
        """
        Determine quality label based on directory structure.
        Images in 'sel*' subdirectories are marked as 'good', others as 'normal'.
        """
        path_parts = Path(image_path).parts
        
        # Check if any parent directory starts with 'sel'
        for part in path_parts:
            if part.lower().startswith('sel'):
                return "good"
        
        return "normal"
    
    def collect_all_images_with_quality(self) -> List[Tuple[str, str]]:
        """
        Collect all images from input directories with quality labels.
        
        Returns:
            List of (image_path, quality_label) tuples
        """
        logger.info("Phase 1: Collecting images and determining quality labels...")
        images_with_quality = []
        
        for input_dir in self.input_directories:
            if not os.path.exists(input_dir):
                logger.warning(f"Input directory does not exist: {input_dir}")
                continue
            
            logger.info(f"Scanning directory: {input_dir}")
            
            # Walk through all subdirectories
            for root, dirs, files in os.walk(input_dir):
                for file in files:
                    if self.is_image_file(file):
                        image_path = os.path.join(root, file)
                        quality = self.determine_quality_label(image_path)
                        images_with_quality.append((image_path, quality))
        
        # Update statistics
        self.stats['total_images_found'] = len(images_with_quality)
        good_count = sum(1 for _, quality in images_with_quality if quality == "good")
        normal_count = len(images_with_quality) - good_count
        
        self.stats['good_quality_images'] = good_count
        self.stats['normal_quality_images'] = normal_count
        
        logger.info(f"Found {len(images_with_quality)} images total")
        logger.info(f"Quality distribution: {good_count} good, {normal_count} normal")
        
        return images_with_quality
    
    def extract_prompt_with_special_lora_format(self, image_path: str) -> Tuple[List[Tuple[str, float]], Dict[str, Any]]:
        """
        Extract prompt data with special LoRA format: <lora:filename>:weight
        
        Args:
            image_path: Path to image file
            
        Returns:
            Tuple of ([(tag, weight) tuples], extraction_info)
        """
        extraction_info = {
            'success': False,
            'error': None,
            'lora_count': 0,
            'negative_weights': 0,
            'total_tags': 0
        }
        
        try:
            # Load image and extract metadata
            with Image.open(image_path) as img:
                info = image_info(img, image_path, enhanced_prompts=True)
                prompt = info.get('prompt', '')
                
                if not prompt:
                    extraction_info['error'] = 'No prompt found'
                    return [], extraction_info
                
                # Parse prompt using corrected parser
                parsed_tags = self.parser.parse_prompt_corrected(prompt)
                
                # Convert LoRA tags to special format: <lora:filename>:weight
                final_tags = []
                lora_count = 0
                negative_weights = 0
                
                for tag, weight in parsed_tags:
                    if tag.startswith('lora_'):
                        # Convert to special LoRA format
                        lora_filename = tag[5:]  # Remove 'lora_' prefix
                        special_lora_tag = f"<lora:{lora_filename}>"
                        final_tags.append((special_lora_tag, weight))
                        lora_count += 1
                        if weight < 0:
                            negative_weights += 1
                    else:
                        final_tags.append((tag, weight))
                
                extraction_info.update({
                    'success': True,
                    'lora_count': lora_count,
                    'negative_weights': negative_weights,
                    'total_tags': len(final_tags)
                })
                
                return final_tags, extraction_info
                
        except Exception as e:
            extraction_info['error'] = str(e)
            return [], extraction_info
    
    def process_single_image(self, image_data: Tuple[str, str]) -> Tuple[str, List[Tuple[str, float]], float]:
        """
        Process a single image and return dataset entry.
        
        Args:
            image_data: Tuple of (image_path, quality_label)
            
        Returns:
            Tuple of (filename, [(tag, weight) tuples], goodness_score)
        """
        image_path, quality_label = image_data
        
        try:
            # Extract prompt data with special LoRA format
            tags, extraction_info = self.extract_prompt_with_special_lora_format(image_path)
            
            # Convert quality label to numeric score
            goodness_score = 1.0 if quality_label == "good" else 0.0
            
            # Update statistics (thread-safe)
            with self.log_lock:
                self.stats['total_images_processed'] += 1
                if extraction_info['success']:
                    self.stats['successful_extractions'] += 1
                    self.stats['lora_extractions'] += extraction_info['lora_count']
                    self.stats['negative_weight_loras'] += extraction_info['negative_weights']
                else:
                    self.stats['failed_extractions'] += 1
                    self.error_log.append({
                        'image_path': image_path,
                        'error': extraction_info['error']
                    })
            
            return image_path, tags, goodness_score
            
        except Exception as e:
            with self.log_lock:
                self.stats['processing_errors'] += 1
                self.error_log.append({
                    'image_path': image_path,
                    'error': str(e)
                })
            
            # Return empty entry for failed processing
            goodness_score = 1.0 if quality_label == "good" else 0.0
            return image_path, [], goodness_score

    def validate_test_cases(self) -> bool:
        """
        Validate extraction against specific test cases.

        Returns:
            True if all test cases pass, False otherwise
        """
        logger.info("Validating against test cases...")
        all_passed = True

        for i, test_case in enumerate(self.validation_test_cases, 1):
            logger.info(f"Test Case {i}: {test_case['description']}")

            if not os.path.exists(test_case['path']):
                logger.warning(f"Test image not found: {test_case['path']}")
                continue

            try:
                # Extract tags using our method
                tags, extraction_info = self.extract_prompt_with_special_lora_format(test_case['path'])

                # Find LoRA tags
                found_loras = []
                for tag, weight in tags:
                    if tag.startswith('<lora:') and tag.endswith('>'):
                        # Convert back to expected format for comparison
                        lora_name = tag[6:-1]  # Remove <lora: and >
                        found_loras.append(f"{lora_name}.safetensors:{weight}")

                logger.info(f"Expected LoRAs: {test_case['expected_loras']}")
                logger.info(f"Found LoRAs: {found_loras}")

                # Check if all expected LoRAs are found
                expected_set = set(test_case['expected_loras'])
                found_set = set(found_loras)

                if expected_set == found_set:
                    logger.info("✅ SUCCESS: Test case passed")
                else:
                    logger.error("❌ FAILURE: Test case failed")
                    missing = expected_set - found_set
                    extra = found_set - expected_set
                    if missing:
                        logger.error(f"   Missing: {missing}")
                    if extra:
                        logger.error(f"   Extra: {extra}")
                    all_passed = False

            except Exception as e:
                logger.error(f"❌ Error processing test case: {e}")
                all_passed = False

        return all_passed

    def process_images_parallel(self, images_with_quality: List[Tuple[str, str]]) -> List[Tuple[str, List[Tuple[str, float]], float]]:
        """
        Process images in parallel with progress tracking.

        Args:
            images_with_quality: List of (image_path, quality_label) tuples

        Returns:
            List of (filename, [(tag, weight) tuples], goodness_score) tuples
        """
        logger.info(f"Phase 2: Processing {len(images_with_quality)} images with {self.max_workers} workers...")

        results = []
        total_images = len(images_with_quality)
        processed_count = 0

        # Process in batches for better progress tracking
        batch_size = max(1, total_images // 100)  # 100 progress updates

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_image = {
                executor.submit(self.process_single_image, image_data): image_data
                for image_data in images_with_quality
            }

            # Collect results with progress tracking
            for future in as_completed(future_to_image):
                try:
                    result = future.result()
                    results.append(result)
                    processed_count += 1

                    # Progress reporting
                    if processed_count % batch_size == 0 or processed_count == total_images:
                        progress = (processed_count / total_images) * 100
                        logger.info(f"Progress: {processed_count}/{total_images} ({progress:.1f}%)")

                except Exception as e:
                    image_data = future_to_image[future]
                    logger.error(f"Error processing {image_data[0]}: {e}")
                    with self.log_lock:
                        self.stats['processing_errors'] += 1

        logger.info(f"Parallel processing complete. Processed {len(results)} images.")
        return results

    def save_dataset(self, dataset: List[Tuple[str, List[Tuple[str, float]], float]], output_path: str):
        """
        Save the dataset to file with backup.

        Args:
            dataset: List of (filename, [(tag, weight) tuples], goodness_score) tuples
            output_path: Path to save the dataset
        """
        logger.info(f"Saving dataset to {output_path}...")

        # Create backup if file exists
        if os.path.exists(output_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{output_path}.backup_{timestamp}"
            os.rename(output_path, backup_path)
            logger.info(f"Created backup: {backup_path}")

        # Save using dill for better compatibility
        try:
            with open(output_path, 'wb') as f:
                dill.dump(dataset, f)
            logger.info(f"Dataset saved successfully: {len(dataset)} entries")
        except Exception as e:
            logger.error(f"Error saving dataset: {e}")
            raise

    def print_statistics(self):
        """Print comprehensive statistics."""
        logger.info("\n" + "="*60)
        logger.info("DATASET CONSTRUCTION STATISTICS")
        logger.info("="*60)

        for key, value in self.stats.items():
            logger.info(f"{key.replace('_', ' ').title()}: {value:,}")

        # Calculate success rates
        if self.stats['total_images_processed'] > 0:
            success_rate = (self.stats['successful_extractions'] / self.stats['total_images_processed']) * 100
            logger.info(f"Extraction Success Rate: {success_rate:.2f}%")

        # LoRA statistics
        if self.stats['lora_extractions'] > 0:
            negative_rate = (self.stats['negative_weight_loras'] / self.stats['lora_extractions']) * 100
            logger.info(f"Negative Weight LoRAs: {negative_rate:.2f}% of total LoRAs")

        logger.info("="*60)

    def run_comprehensive_validation(self) -> bool:
        """
        Run comprehensive validation including test cases and data quality checks.

        Returns:
            True if all validations pass, False otherwise
        """
        logger.info("Running comprehensive validation...")

        # Test case validation
        test_cases_passed = self.validate_test_cases()

        if test_cases_passed:
            logger.info("✅ All test cases passed!")
            return True
        else:
            logger.error("❌ Some test cases failed!")
            return False

    def build_production_dataset(self, output_path: str = "production_dataset.pkl",
                                validate_first: bool = True) -> bool:
        """
        Build the complete production dataset.

        Args:
            output_path: Path to save the final dataset
            validate_first: Whether to run validation before full processing

        Returns:
            True if successful, False otherwise
        """
        try:
            start_time = time.time()
            logger.info("Starting production dataset construction...")

            # Step 1: Validation (optional)
            if validate_first:
                logger.info("Step 1: Running validation...")
                if not self.run_comprehensive_validation():
                    logger.error("Validation failed! Aborting dataset construction.")
                    return False
                logger.info("✅ Validation passed!")

            # Step 2: Collect all images
            logger.info("Step 2: Collecting images...")
            images_with_quality = self.collect_all_images_with_quality()

            if not images_with_quality:
                logger.error("No images found to process!")
                return False

            # Step 3: Process images in parallel
            logger.info("Step 3: Processing images...")
            dataset = self.process_images_parallel(images_with_quality)

            # Step 4: Save dataset
            logger.info("Step 4: Saving dataset...")
            self.save_dataset(dataset, output_path)

            # Step 5: Print statistics
            self.print_statistics()

            # Calculate total time
            total_time = time.time() - start_time
            logger.info(f"Dataset construction completed in {total_time:.2f} seconds")
            logger.info(f"Final dataset: {len(dataset)} entries saved to {output_path}")

            return True

        except Exception as e:
            logger.error(f"Error during dataset construction: {e}")
            return False


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Production-ready dataset construction script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --build                                    # Build full dataset
  %(prog)s --validate                                 # Run validation only
  %(prog)s --build --output my_dataset.pkl           # Custom output file
  %(prog)s --build --workers 16                      # Use 16 parallel workers
  %(prog)s --build --no-validation                   # Skip validation step
  %(prog)s --test-cases                              # Test specific cases only
        """
    )

    # Main actions
    parser.add_argument('--build', action='store_true',
                       help='Build the complete production dataset')
    parser.add_argument('--validate', action='store_true',
                       help='Run comprehensive validation only')
    parser.add_argument('--test-cases', action='store_true',
                       help='Test specific validation cases only')

    # Options
    parser.add_argument('--output', '-o', type=str, default='production_dataset.pkl',
                       help='Output dataset file path (default: production_dataset.pkl)')
    parser.add_argument('--workers', '-w', type=int, default=8,
                       help='Number of parallel workers (default: 8)')
    parser.add_argument('--no-validation', action='store_true',
                       help='Skip validation step when building dataset')
    parser.add_argument('--custom-dirs', nargs='+', metavar='DIR',
                       help='Custom input directories (overrides defaults)')

    args = parser.parse_args()

    # Create builder instance
    builder = ProductionDatasetBuilder(max_workers=args.workers)

    # Override input directories if specified
    if args.custom_dirs:
        builder.input_directories = args.custom_dirs
        logger.info(f"Using custom input directories: {args.custom_dirs}")

    # Execute requested action
    success = True

    if args.build:
        # Build complete dataset
        validate_first = not args.no_validation
        success = builder.build_production_dataset(args.output, validate_first)

    elif args.validate:
        # Run validation only
        success = builder.run_comprehensive_validation()

    elif args.test_cases:
        # Test specific cases only
        success = builder.validate_test_cases()

    else:
        # No action specified, show help
        parser.print_help()
        return

    # Exit with appropriate code
    if success:
        logger.info("✅ Operation completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Operation failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()

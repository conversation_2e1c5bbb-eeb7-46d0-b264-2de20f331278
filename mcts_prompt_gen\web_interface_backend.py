#!/usr/bin/env python3
"""
Interactive Web Interface Backend for Prompt Goodness Prediction

This module provides a Flask-based web API for real-time prompt goodness prediction
and optimization using trained LightGBM models and the corrected parsing pipeline.

Features:
- Real-time goodness prediction with confidence intervals
- Prompt parsing and preprocessing using corrected logic
- Tag/LoRA frequency analysis for optimization
- Caching system for performance
- Multi-model support with comparison

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import logging
import pickle
import dill
from typing import List, Tuple, Dict, Any, Optional
from datetime import datetime
import numpy as np
import pandas as pd
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import threading
import time
from functools import lru_cache

# Import existing models and parsers
try:
    from goodness_predictor_models import LightGBMGoodnessPredictorModel, PromptFeatureExtractor
    from corrected_prompt_parser import parse_prompt_with_weights_corrected
    from model_loader import ModelManager
except ImportError as e:
    logger.error(f"Import error: {e}")
    # Fallback imports
    from corrected_prompt_parser import parse_prompt_with_weights_corrected
    from model_loader import ModelManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Flask app configuration
app = Flask(__name__)
CORS(app)  # Enable CORS for frontend integration

class PromptGoodnessPredictor:
    """
    Main prediction engine that loads models and provides real-time predictions.
    """
    
    def __init__(self, model_dir: str = None):
        """
        Initialize the prediction engine.

        Args:
            model_dir: Directory containing trained models
        """
        self.model_dir = model_dir
        self.model_manager = ModelManager(model_dir)
        self.tag_frequency = {}
        self.lora_frequency = {}
        self.prediction_cache = {}
        self.cache_lock = threading.Lock()

        # Load frequency data
        self._load_frequency_data()

        logger.info(f"Prediction engine initialized with {len(self.model_manager.models)} models")
    

    
    def _load_frequency_data(self):
        """Load tag and LoRA frequency data for optimization."""
        try:
            # Load dataset to build frequency tables
            dataset_files = [
                'corrected_ratio_based_dataset.pkl',
                'production_dataset.pkl',
                'promptlabels_truly_fixed.pkl'
            ]
            
            dataset_path = None
            for filename in dataset_files:
                full_path = os.path.join(os.path.dirname(__file__), filename)
                if os.path.exists(full_path):
                    dataset_path = full_path
                    break
            
            if not dataset_path:
                logger.warning("No dataset file found for frequency analysis")
                return
            
            logger.info(f"Loading frequency data from: {dataset_path}")
            with open(dataset_path, 'rb') as f:
                dataset = dill.load(f)
            
            # Build frequency tables
            tag_counts = {}
            lora_counts = {}
            good_tag_counts = {}
            
            for entry in dataset:
                if len(entry) >= 3:
                    filename, tags, goodness = entry[:3]
                    is_good = goodness > 0.5  # Consider > 0.5 as good
                    
                    for tag, weight in tags:
                        tag_counts[tag] = tag_counts.get(tag, 0) + 1
                        
                        if is_good:
                            good_tag_counts[tag] = good_tag_counts.get(tag, 0) + 1
                        
                        if tag.startswith('lora_'):
                            lora_counts[tag] = lora_counts.get(tag, 0) + 1
            
            # Calculate goodness ratios for tags
            self.tag_frequency = {}
            for tag, count in tag_counts.items():
                good_count = good_tag_counts.get(tag, 0)
                goodness_ratio = good_count / count if count > 0 else 0
                self.tag_frequency[tag] = {
                    'count': count,
                    'good_count': good_count,
                    'goodness_ratio': goodness_ratio
                }
            
            self.lora_frequency = {tag: count for tag, count in lora_counts.items()}
            
            logger.info(f"Loaded frequency data: {len(self.tag_frequency)} tags, {len(self.lora_frequency)} LoRAs")
            
        except Exception as e:
            logger.error(f"Error loading frequency data: {e}")
    
    @lru_cache(maxsize=1000)
    def parse_prompt(self, prompt_text: str) -> List[Tuple[str, float]]:
        """
        Parse prompt text into (tag, weight) tuples with caching.
        
        Args:
            prompt_text: Raw prompt text
            
        Returns:
            List of (tag, weight) tuples
        """
        try:
            return parse_prompt_with_weights_corrected(prompt_text)
        except Exception as e:
            logger.error(f"Error parsing prompt: {e}")
            return [('parsing_error', 1.0)]
    
    def predict_goodness(self, prompt_text: str, model_names: List[str] = None) -> Dict[str, Any]:
        """
        Predict goodness score for a prompt using specified models.
        
        Args:
            prompt_text: Raw prompt text
            model_names: List of model names to use (default: all available)
            
        Returns:
            Dictionary with predictions, parsed prompt, and metadata
        """
        if model_names is None:
            model_names = list(self.model_manager.models.keys())

        # Check cache first
        cache_key = f"{prompt_text}:{':'.join(sorted(model_names))}"
        with self.cache_lock:
            if cache_key in self.prediction_cache:
                return self.prediction_cache[cache_key]

        try:
            # Parse prompt
            parsed_tags = self.parse_prompt(prompt_text)

            # Get predictions from model manager
            predictions = self.model_manager.predict(prompt_text, model_names)
            
            # Prepare result
            result = {
                'prompt_text': prompt_text,
                'parsed_tags': parsed_tags,
                'predictions': predictions,
                'tag_count': len(parsed_tags),
                'avg_weight': sum(w for _, w in parsed_tags) / len(parsed_tags) if parsed_tags else 1.0,
                'timestamp': datetime.now().isoformat()
            }
            
            # Cache result
            with self.cache_lock:
                self.prediction_cache[cache_key] = result
                # Limit cache size
                if len(self.prediction_cache) > 10000:
                    # Remove oldest 1000 entries
                    keys_to_remove = list(self.prediction_cache.keys())[:1000]
                    for key in keys_to_remove:
                        del self.prediction_cache[key]
            
            return result
            
        except Exception as e:
            logger.error(f"Error in predict_goodness: {e}")
            return {
                'prompt_text': prompt_text,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def optimize_prompt(self, prompt_text: str, time_limit: int = 10) -> Dict[str, Any]:
        """
        Optimize a prompt by suggesting improvements based on frequency analysis.

        Args:
            prompt_text: Original prompt text
            time_limit: Time limit in seconds for optimization

        Returns:
            Dictionary with optimization suggestions
        """
        try:
            start_time = time.time()

            # Parse original prompt
            original_tags = self.parse_prompt(prompt_text)
            original_prediction = self.predict_goodness(prompt_text)
            original_score = self._get_best_score(original_prediction.get('predictions', {}))

            suggestions = []

            # Strategy 1: Add high-quality tags
            quality_tags = self._get_high_quality_tags(limit=10)
            for tag, stats in quality_tags:
                if time.time() - start_time > time_limit:
                    break

                # Skip if tag already exists
                if any(existing_tag == tag for existing_tag, _ in original_tags):
                    continue

                # Test adding this tag
                test_tags = original_tags + [(tag, 1.0)]
                test_prompt = self._tags_to_prompt(test_tags)
                test_prediction = self.predict_goodness(test_prompt)
                test_score = self._get_best_score(test_prediction.get('predictions', {}))

                improvement = test_score - original_score
                if improvement > 0.01:  # Minimum improvement threshold
                    suggestions.append({
                        'type': 'add_tag',
                        'action': f'Add tag: {tag}',
                        'improvement': improvement,
                        'confidence': stats['goodness_ratio'],
                        'modified_prompt': test_prompt
                    })

            # Strategy 2: Adjust weights of existing tags
            for tag, weight in original_tags:
                if time.time() - start_time > time_limit:
                    break

                if tag in self.tag_frequency:
                    stats = self.tag_frequency[tag]
                    if stats['goodness_ratio'] > 0.6:  # High-quality tag
                        # Try increasing weight
                        new_weight = min(weight + 0.2, 2.0)
                        if new_weight != weight:
                            test_tags = [(t, new_weight if t == tag else w) for t, w in original_tags]
                            test_prompt = self._tags_to_prompt(test_tags)
                            test_prediction = self.predict_goodness(test_prompt)
                            test_score = self._get_best_score(test_prediction.get('predictions', {}))

                            improvement = test_score - original_score
                            if improvement > 0.005:
                                suggestions.append({
                                    'type': 'adjust_weight',
                                    'action': f'Increase weight of {tag} to {new_weight:.1f}',
                                    'improvement': improvement,
                                    'confidence': 0.8,
                                    'modified_prompt': test_prompt
                                })

            # Strategy 3: Suggest popular LoRA models
            if time.time() - start_time < time_limit:
                popular_loras = self._get_popular_loras(limit=5)
                for lora_tag, count in popular_loras:
                    if time.time() - start_time > time_limit:
                        break

                    # Skip if LoRA already exists
                    if any(existing_tag == lora_tag for existing_tag, _ in original_tags):
                        continue

                    # Test adding this LoRA
                    test_tags = original_tags + [(lora_tag, 0.8)]
                    test_prompt = self._tags_to_prompt(test_tags)
                    test_prediction = self.predict_goodness(test_prompt)
                    test_score = self._get_best_score(test_prediction.get('predictions', {}))

                    improvement = test_score - original_score
                    if improvement > 0.005:
                        suggestions.append({
                            'type': 'add_lora',
                            'action': f'Add LoRA: {lora_tag.replace("lora_", "")}',
                            'improvement': improvement,
                            'confidence': min(count / 100, 1.0),
                            'modified_prompt': test_prompt
                        })

            # Sort suggestions by improvement
            suggestions.sort(key=lambda x: x['improvement'], reverse=True)

            return {
                'original_prompt': prompt_text,
                'original_score': original_score,
                'suggestions': suggestions[:10],  # Top 10 suggestions
                'optimization_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in optimize_prompt: {e}")
            return {
                'original_prompt': prompt_text,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _get_best_score(self, predictions: Dict[str, Any]) -> float:
        """Get the best score from predictions."""
        return self.model_manager.get_best_score(predictions)

    def _get_high_quality_tags(self, limit: int = 10) -> List[Tuple[str, Dict]]:
        """Get tags with high goodness ratios."""
        quality_tags = []
        for tag, stats in self.tag_frequency.items():
            if (stats['count'] >= 10 and  # Minimum frequency
                stats['goodness_ratio'] > 0.5 and  # Good quality
                not tag.startswith(('lora_', 'cfg', 'steps', 'width', 'height'))):  # Not technical
                quality_tags.append((tag, stats))

        # Sort by goodness ratio and frequency
        quality_tags.sort(key=lambda x: (x[1]['goodness_ratio'], x[1]['count']), reverse=True)
        return quality_tags[:limit]

    def _get_popular_loras(self, limit: int = 5) -> List[Tuple[str, int]]:
        """Get popular LoRA models."""
        lora_items = list(self.lora_frequency.items())
        lora_items.sort(key=lambda x: x[1], reverse=True)
        return lora_items[:limit]

    def _tags_to_prompt(self, tags: List[Tuple[str, float]]) -> str:
        """Convert tags back to prompt string."""
        tag_strings = []
        for tag, weight in tags:
            if weight == 1.0:
                tag_strings.append(tag)
            else:
                tag_strings.append(f"({tag}:{weight:.1f})")
        return ', '.join(tag_strings)

# Global prediction engine instance
predictor = None

def initialize_predictor():
    """Initialize the global predictor instance."""
    global predictor
    try:
        predictor = PromptGoodnessPredictor()
        logger.info("Predictor initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize predictor: {e}")
        raise

# API Routes
@app.route('/')
def index():
    """Serve the main interface."""
    return render_template('index.html')

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint for goodness prediction."""
    try:
        data = request.get_json()
        if not data or 'prompt' not in data:
            return jsonify({'error': 'Missing prompt in request'}), 400
        
        prompt_text = data['prompt']
        model_names = data.get('models', None)
        
        if not predictor:
            return jsonify({'error': 'Predictor not initialized'}), 500
        
        result = predictor.predict_goodness(prompt_text, model_names)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in API predict: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/parse', methods=['POST'])
def api_parse():
    """API endpoint for prompt parsing only."""
    try:
        data = request.get_json()
        if not data or 'prompt' not in data:
            return jsonify({'error': 'Missing prompt in request'}), 400

        prompt_text = data['prompt']

        if not predictor:
            return jsonify({'error': 'Predictor not initialized'}), 500

        parsed_tags = predictor.parse_prompt(prompt_text)

        return jsonify({
            'prompt_text': prompt_text,
            'parsed_tags': parsed_tags,
            'tag_count': len(parsed_tags),
            'avg_weight': sum(w for _, w in parsed_tags) / len(parsed_tags) if parsed_tags else 1.0
        })

    except Exception as e:
        logger.error(f"Error in API parse: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/optimize', methods=['POST'])
def api_optimize():
    """API endpoint for prompt optimization."""
    try:
        data = request.get_json()
        if not data or 'prompt' not in data:
            return jsonify({'error': 'Missing prompt in request'}), 400

        prompt_text = data['prompt']
        time_limit = data.get('time_limit', 10)  # Default 10 seconds

        if not predictor:
            return jsonify({'error': 'Predictor not initialized'}), 500

        suggestions = predictor.optimize_prompt(prompt_text, time_limit)
        return jsonify(suggestions)

    except Exception as e:
        logger.error(f"Error in API optimize: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/models', methods=['GET'])
def api_models():
    """API endpoint to get available models."""
    try:
        if not predictor:
            return jsonify({'error': 'Predictor not initialized'}), 500

        models_info = {}
        for model_name in predictor.model_manager.models.keys():
            models_info[model_name] = {
                'name': model_name,
                'type': model_name.split('_')[0],
                'available': True
            }

        return jsonify({'models': models_info})

    except Exception as e:
        logger.error(f"Error in API models: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Initialize predictor
    initialize_predictor()

    # Run Flask app
    app.run(host='0.0.0.0', port=5000, debug=True, threaded=True)

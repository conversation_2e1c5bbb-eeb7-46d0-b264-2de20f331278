#!/usr/bin/env python3
"""
Fix Structured Dataset - Repair Parsing Errors in Existing Structured Data

This script fixes the critical parsing errors in the existing structured dataset
by re-parsing problematic tags while preserving the overall structure.

Critical Issues Fixed:
1. Tags with embedded newlines and escape characters (e.g., '\\n(watercolor:0.7)')
2. Incorrect LoRA classification (artist tags vs actual LoRA models)
3. Complex bracket expressions not properly parsed
4. Tag normalization inconsistencies
5. Unicode escape sequences in tags

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import re
import logging
from typing import List, Tuple, Dict, Any, Optional
from datetime import datetime
import traceback
from collections import Counter, defaultdict

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(__file__))

from fixed_prompt_parser import FixedPromptParser

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'dataset_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StructuredDatasetFixer:
    """Fix parsing errors in existing structured dataset."""
    
    def __init__(self):
        """Initialize the fixer."""
        self.parser = FixedPromptParser()
        self.fix_stats = {
            'total_entries': 0,
            'entries_fixed': 0,
            'tags_fixed': 0,
            'lora_reclassified': 0,
            'complex_brackets_fixed': 0,
            'unicode_issues_fixed': 0,
            'normalization_fixes': 0
        }
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def is_problematic_tag(self, tag: str) -> bool:
        """Check if a tag has parsing issues that need fixing."""
        if not isinstance(tag, str):
            return False
        
        # Check for various parsing issues
        issues = [
            '\\n' in tag,                    # Literal newlines
            '\\u' in tag,                    # Unicode escapes
            tag.startswith('\\'),            # Escape characters
            '(' in tag and ':' in tag and ')' in tag,  # Embedded weight syntax
            '[' in tag and ']' in tag,      # Embedded bracket syntax
            '{' in tag and '}' in tag,      # Embedded curly syntax
            tag.count('(') != tag.count(')'),  # Unbalanced brackets
            tag.count('[') != tag.count(']'),  # Unbalanced brackets
            tag.count('{') != tag.count('}'),  # Unbalanced brackets
        ]
        
        return any(issues)
    
    def fix_single_tag(self, tag: str, weight: float) -> List[Tuple[str, float]]:
        """
        Fix a single problematic tag.
        
        Args:
            tag: Problematic tag string
            weight: Original weight
            
        Returns:
            List of fixed (tag, weight) tuples
        """
        if not self.is_problematic_tag(tag):
            # Just normalize the tag
            normalized = self.parser.normalize_tag(tag)
            return [(normalized, weight)] if normalized else []
        
        try:
            # If tag contains embedded syntax, re-parse it
            if any(char in tag for char in '()[]{}:'):
                # Re-parse the tag as if it were a mini-prompt
                parsed_tags = self.parser.parse_prompt_with_fixed_logic(tag)
                if parsed_tags:
                    self.fix_stats['complex_brackets_fixed'] += 1
                    return parsed_tags
            
            # Fix text preprocessing issues
            fixed_tag = self.parser.preprocess_text(tag)
            if fixed_tag != tag:
                self.fix_stats['unicode_issues_fixed'] += 1
            
            # Normalize the fixed tag
            normalized = self.parser.normalize_tag(fixed_tag)
            if normalized:
                if normalized != tag.lower().replace(' ', '_'):
                    self.fix_stats['normalization_fixes'] += 1
                return [(normalized, weight)]
            
            return []
            
        except Exception as e:
            logger.warning(f"Failed to fix tag '{tag}': {e}")
            # Return normalized version as fallback
            normalized = self.parser.normalize_tag(tag)
            return [(normalized, weight)] if normalized else []
    
    def reclassify_lora_tags(self, tags: List[Tuple[str, float]]) -> List[Tuple[str, float]]:
        """
        Reclassify incorrectly identified LoRA tags.
        
        Args:
            tags: List of (tag, weight) tuples
            
        Returns:
            List with LoRA tags properly classified
        """
        fixed_tags = []
        
        for tag, weight in tags:
            # Check if this is incorrectly classified as LoRA
            if tag.startswith('lora_'):
                # Remove lora_ prefix and check if it's actually an artist/style tag
                base_tag = tag[5:]  # Remove 'lora_' prefix
                
                # Common artist/style tags that were misclassified
                artist_indicators = [
                    'ciloranko', 'artist:', 'style:', 'hiten', 'sy4', 'imigimuru',
                    'mimelond', 'fujiyama', 'umanosuke', 'ogipote', 'nii_manabu',
                    'fkey', 'rin_yuu', 'nekojira'
                ]
                
                is_artist_tag = any(indicator in base_tag.lower() for indicator in artist_indicators)
                
                if is_artist_tag:
                    # This is actually an artist/style tag, not a LoRA
                    fixed_tags.append((base_tag, weight))
                    self.fix_stats['lora_reclassified'] += 1
                else:
                    # This might be a legitimate LoRA, keep it
                    fixed_tags.append((tag, weight))
            else:
                fixed_tags.append((tag, weight))
        
        return fixed_tags
    
    def fix_entry_tags(self, tags: List[Tuple[str, float]]) -> List[Tuple[str, float]]:
        """
        Fix all tags in a single entry.
        
        Args:
            tags: List of (tag, weight) tuples from entry
            
        Returns:
            List of fixed (tag, weight) tuples
        """
        if not isinstance(tags, list):
            return [('invalid_tags_format', 1.0)]
        
        fixed_tags = []
        entry_had_fixes = False
        
        for tag, weight in tags:
            if self.is_problematic_tag(tag):
                entry_had_fixes = True
                fixed_tag_list = self.fix_single_tag(tag, weight)
                fixed_tags.extend(fixed_tag_list)
                self.fix_stats['tags_fixed'] += 1
            else:
                # Just normalize
                normalized = self.parser.normalize_tag(tag)
                if normalized:
                    fixed_tags.append((normalized, weight))
        
        # Reclassify LoRA tags
        fixed_tags = self.reclassify_lora_tags(fixed_tags)
        
        # Deduplicate tags
        fixed_tags = self.parser.deduplicate_and_validate_tags(fixed_tags)
        
        if entry_had_fixes:
            self.fix_stats['entries_fixed'] += 1
        
        return fixed_tags
    
    def fix_dataset(self, input_file: str, output_file: str) -> Dict[str, Any]:
        """
        Fix the entire structured dataset.
        
        Args:
            input_file: Path to problematic structured dataset
            output_file: Path to output fixed dataset
            
        Returns:
            Fix statistics
        """
        logger.info(f"Starting dataset fix: {input_file} -> {output_file}")
        
        # Load problematic dataset
        try:
            with open(input_file, 'rb') as f:
                problematic_data = dill.load(f)
            logger.info(f"Loaded {len(problematic_data)} entries from {input_file}")
            self.fix_stats['total_entries'] = len(problematic_data)
        except Exception as e:
            logger.error(f"Failed to load input file {input_file}: {e}")
            raise
        
        # Fix each entry
        fixed_data = []
        
        for i, entry in enumerate(problematic_data):
            try:
                if not isinstance(entry, (tuple, list)) or len(entry) != 3:
                    logger.warning(f"Invalid entry format at index {i}")
                    fixed_data.append(entry)
                    continue
                
                filename, tags, goodness = entry
                
                # Fix the tags
                fixed_tags = self.fix_entry_tags(tags)
                
                # Create fixed entry
                fixed_entry = (filename, fixed_tags, goodness)
                fixed_data.append(fixed_entry)
                
                # Log progress
                if (i + 1) % 1000 == 0:
                    logger.info(f"Fixed {i + 1}/{len(problematic_data)} entries")
                    
            except Exception as e:
                logger.error(f"Error fixing entry {i}: {e}")
                # Keep original entry on error
                fixed_data.append(entry)
        
        # Save fixed dataset
        try:
            with open(output_file, 'wb') as f:
                dill.dump(fixed_data, f)
            logger.info(f"Saved {len(fixed_data)} fixed entries to {output_file}")
        except Exception as e:
            logger.error(f"Failed to save output file {output_file}: {e}")
            raise
        
        # Generate comprehensive statistics
        final_stats = self._generate_fix_statistics(fixed_data)
        
        return final_stats
    
    def _generate_fix_statistics(self, fixed_data: List[Tuple]) -> Dict[str, Any]:
        """Generate comprehensive statistics for the fixed dataset."""
        stats = self.fix_stats.copy()
        
        # Analyze fixed data
        total_tags = 0
        unique_tags = set()
        tag_frequency = Counter()
        lora_tags = []
        problematic_tags_remaining = 0
        
        for filename, tags, goodness in fixed_data:
            total_tags += len(tags)
            
            for tag, weight in tags:
                unique_tags.add(tag)
                tag_frequency[tag] += 1
                
                if tag.startswith('lora_'):
                    lora_tags.append((tag, weight))
                
                if self.is_problematic_tag(tag):
                    problematic_tags_remaining += 1
        
        stats.update({
            'fixed_entries': len(fixed_data),
            'total_tags_after_fix': total_tags,
            'unique_tags_after_fix': len(unique_tags),
            'avg_tags_per_entry': total_tags / len(fixed_data) if fixed_data else 0,
            'lora_tags_remaining': len(lora_tags),
            'unique_lora_models': len(set(tag for tag, _ in lora_tags)),
            'problematic_tags_remaining': problematic_tags_remaining,
            'fix_success_rate': ((stats['total_entries'] - problematic_tags_remaining) / stats['total_entries']) * 100 if stats['total_entries'] > 0 else 0,
            'most_common_tags': tag_frequency.most_common(50),
            'lora_frequency': Counter(tag for tag, _ in lora_tags).most_common(20)
        })
        
        return stats


def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Fix parsing errors in existing structured prompt dataset",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python fix_structured_dataset.py --input promptlabels_structured.pkl --output promptlabels_fixed.pkl
  python fix_structured_dataset.py --input promptlabels_structured.pkl --output promptlabels_fixed.pkl --test-sample 1000
        """
    )

    parser.add_argument("--input", required=True,
                       help="Input structured pickle file with parsing errors")
    parser.add_argument("--output", required=True,
                       help="Output pickle file for fixed dataset")
    parser.add_argument("--test-sample", type=int,
                       help="Test on a small sample first (number of entries)")
    parser.add_argument("--backup", action="store_true",
                       help="Create backup of original file")

    args = parser.parse_args()

    # Validate input file
    if not os.path.exists(args.input):
        logger.error(f"Input file not found: {args.input}")
        return 1

    # Create backup if requested
    if args.backup:
        backup_file = f"{args.input}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            import shutil
            shutil.copy2(args.input, backup_file)
            logger.info(f"Created backup: {backup_file}")
        except Exception as e:
            logger.warning(f"Failed to create backup: {e}")

    # Initialize fixer
    fixer = StructuredDatasetFixer()

    try:
        # Handle test sample
        if args.test_sample:
            logger.info(f"Running test on {args.test_sample} samples...")

            # Load and sample data
            with open(args.input, 'rb') as f:
                full_data = dill.load(f)

            import random
            test_data = random.sample(full_data, min(args.test_sample, len(full_data)))

            # Save test data temporarily
            test_input = f"test_sample_{args.test_sample}.pkl"
            with open(test_input, 'wb') as f:
                dill.dump(test_data, f)

            # Process test sample
            test_output = f"test_fixed_{args.test_sample}.pkl"
            stats = fixer.fix_dataset(test_input, test_output)

            # Clean up temporary files
            os.remove(test_input)

            logger.info("Test sample processing completed!")
            logger.info(f"Test results saved to: {test_output}")

        else:
            # Process full dataset
            logger.info("Processing full dataset...")
            stats = fixer.fix_dataset(args.input, args.output)

        # Print summary
        print("\n" + "=" * 80)
        print("DATASET FIX COMPLETED")
        print("=" * 80)
        print(f"Input file: {args.input}")
        print(f"Output file: {args.output}")
        print("")
        print("Fix Statistics:")
        print(f"  Total entries: {stats['total_entries']:,}")
        print(f"  Entries fixed: {stats['entries_fixed']:,}")
        print(f"  Tags fixed: {stats['tags_fixed']:,}")
        print(f"  LoRA tags reclassified: {stats['lora_reclassified']:,}")
        print(f"  Complex brackets fixed: {stats['complex_brackets_fixed']:,}")
        print(f"  Unicode issues fixed: {stats['unicode_issues_fixed']:,}")
        print(f"  Normalization fixes: {stats['normalization_fixes']:,}")
        print(f"  Problematic tags remaining: {stats['problematic_tags_remaining']:,}")
        print(f"  Fix success rate: {stats['fix_success_rate']:.2f}%")
        print("")

        # Show top tags after fix
        if 'most_common_tags' in stats:
            print("Top 10 Tags After Fix:")
            for i, (tag, count) in enumerate(stats['most_common_tags'][:10], 1):
                print(f"  {i:2d}. {tag}: {count:,}")
        print("")

        # Show LoRA analysis
        if 'lora_frequency' in stats and stats['lora_frequency']:
            print("Top 10 LoRA Models After Fix:")
            for i, (lora, count) in enumerate(stats['lora_frequency'][:10], 1):
                print(f"  {i:2d}. {lora}: {count:,}")
        else:
            print("LoRA Models: None found (all reclassified as artist/style tags)")
        print("")

        # Check if quality improved
        if stats['fix_success_rate'] >= 98.0:
            print("🎉 SUCCESS: Dataset fixes applied with excellent quality!")
            print("✅ Critical parsing errors have been resolved")
        elif stats['fix_success_rate'] >= 95.0:
            print("✅ SUCCESS: Dataset fixes applied with good quality")
            print("⚠️  Some minor issues may remain")
        else:
            print("⚠️  WARNING: Some parsing issues may still remain")
            print("❌ Consider additional manual review")

        return 0

    except Exception as e:
        logger.error(f"Dataset fix failed: {e}")
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())

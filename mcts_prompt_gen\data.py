import json
import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer

class PromptDataset(Dataset):
    def __init__(self, data_path, tokenizer, max_length=50):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.prompts = []
        self.scores = []
        
        # Load data from JSON file
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        for item in data:
            self.prompts.append(item['prompt'])
            self.scores.append(item['score'])
            
    def __len__(self):
        return len(self.prompts)
    
    def __getitem__(self, idx):
        prompt = self.prompts[idx]
        score = self.scores[idx]
        
        # Tokenize prompt
        tokens = self.tokenizer.encode(
            prompt, 
            max_length=self.max_length, 
            padding='max_length',
            truncation=True
        )
        
        # Create features (one-hot encoding or embeddings)
        features = np.zeros((self.max_length, self.tokenizer.vocab_size))
        for i, token in enumerate(tokens):
            if i < self.max_length:
                features[i, token] = 1
                
        return {
            'features': torch.tensor(features.flatten(), dtype=torch.float32),
            'tokens': torch.tensor(tokens, dtype=torch.long),
            'score': torch.tensor(score, dtype=torch.float32)
        }

def load_data(data_path, tokenizer, batch_size=32, max_length=50, split=0.8):
    """Load and split data into train and validation sets."""
    dataset = PromptDataset(data_path, tokenizer, max_length)
    
    # Split dataset
    train_size = int(len(dataset) * split)
    val_size = len(dataset) - train_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    
    return train_loader, val_loader

class RewardModel:
    def __init__(self, model_path=None):
        """
        Initialize reward model - could be a trained model or heuristic function
        """
        self.model = None
        if model_path and os.path.exists(model_path):
            self.model = torch.load(model_path)
    
    def evaluate(self, prompt):
        """
        Evaluate prompt quality and return a reward score
        """
        if self.model:
            # Use trained model for evaluation
            # Implementation depends on model type
            return 0.5  # Placeholder
        else:
            # Simple heuristic: reward longer, more diverse prompts
            # This is just a placeholder - real implementation would be more sophisticated
            words = prompt.split()
            unique_words = set(words)
            
            length_score = min(len(words) / 20, 1.0)  # Normalize by expected length
            diversity_score = len(unique_words) / (len(words) + 1e-10)
            
            return 0.6 * length_score + 0.4 * diversity_score
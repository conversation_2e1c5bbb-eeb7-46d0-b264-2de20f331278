[{"image_path": "F:\\SD-webui\\ComfyUI\\output\\ComfyUI_00002_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 9, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\ComfyUI-kko_00002_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\ComfyUI_00014_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 7, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\ComfyUI_00012_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 7, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\ComfyUI_00072_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\ComfyUI_00115_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\ComfyUI_00053_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\ComfyUI_00081_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\ComfyUI_00010_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 7, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\ComfyUI_00045_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\ComfyUI_00005_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "shuicai.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\ComfyUI_00012_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\ComfyUI_00047_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\ComfyUI_00050_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\ComfyUI_00112_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\ComfyUI_00041_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "shuicai.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\ComfyUI_00006_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\ComfyUI_00005_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy\\ComfyUI_00013_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy\\ComfyUI_00121_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy\\ComfyUI_00112_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy\\ComfyUI_00041_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\ComfyUI_00004_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy\\ComfyUI_00003_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-27\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\ComfyUI_00002_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\ComfyUI_00041_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\ComfyUI_00041_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\ComfyUI_00020_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel\\ComfyUI_00060_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel\\ComfyUI_00052_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel\\ComfyUI_00064_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel\\ComfyUI_00023_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel\\ComfyUI_00051_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-09-29\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\ComfyUI_00195_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\ComfyUI_00048_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\ComfyUI_00050_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\ComfyUI_00184_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\ComfyUI_00191_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel\\ComfyUI_00189_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel\\ComfyUI_00031_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel\\ComfyUI_00068_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel\\ComfyUI_00029_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel\\ComfyUI_00181_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-05\\sel", "success": false, "loras_found": [], "error": "'list' object has no attribute 'lower'", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 600, in extract_comfyui_workflow_params\n    'embedding:' in node.get('inputs', {}).get('text', '').lower())):\nAttributeError: 'list' object has no attribute 'lower'\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\ComfyUI_00128_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\ComfyUI_00497_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\ComfyUI_00445_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\ComfyUI_00081_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\ComfyUI_00388_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel\\ComfyUI_00149_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6", "YeFlower.safetensors:0.99"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel\\ComfyUI_00072_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel\\ComfyUI_00122_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel\\ComfyUI_00123_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel\\ComfyUI_00148_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-19\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6", "YeFlower.safetensors:0.99"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\ComfyUI_00139_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\ComfyUI_00036_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\ComfyUI_00024_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\ComfyUI_00117_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\ComfyUI_00117_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\ComfyUI_00105_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\ComfyUI_00095_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\ComfyUI_00037_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\ComfyUI_00134_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\ComfyUI_00074_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\ComfyUI_00042_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\ComfyUI_00160_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\ComfyUI_00118_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\ComfyUI_00028_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\ComfyUI_00074_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.7", "outline_xl_kohaku_delta_spv5x.safetensors:0.4", "shuicai.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 6, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy\\ComfyUI_00157_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy\\ComfyUI_00052_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy\\ComfyUI_00054_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\ComfyUI_00019_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy\\ComfyUI_00118_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy\\ComfyUI_00118_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-20\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\ComfyUI_00044_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\ComfyUI_00042_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\ComfyUI_00063_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.5"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\ComfyUI_00054_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel\\ComfyUI_00011_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel\\ComfyUI_00049_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel", "success": true, "loras_found": ["yoneyamaiXL_il_lokr_V53P1.safetensors:0.6", "Yanami XL kohaku zeta.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel\\ComfyUI_00047_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel", "success": true, "loras_found": ["yoneyamaiXL_il_lokr_V53P1.safetensors:0.6", "Yanami XL kohaku zeta.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16\\ComfyUI_61042.jpg", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16\\ComfyUI_61043.jpg", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16\\ComfyUI_61041.jpg", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16\\ComfyUI_61043.jpg", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel\\ComfyUI_00044_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel\\ComfyUI_00045_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-10-23\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.7", "yoneyamaiXL_il_lokr_V53P1.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16\\ComfyUI_061039_449802917750001_00001.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-16", "success": false, "loras_found": [], "error": "Failed to traverse workflow graph: No output nodes found in workflow. Available node types: {'ImpactWildcardProcessor', 'TilePreprocessor', 'easy imageColorMatch', 'Reroute', 'Number to String', 'ImpactImageInfo', 'ImageScale', 'RescaleCFG', 'JoinStrings', 'ControlNetLoader', 'ModelSamplingSD3', 'SDParameterGenerator', 'KSampler Adv. (Efficient)', 'ShowText|pysssss', 'SAMLoader', 'TIPO', 'ControlNetApplySD3', 'String to Text', 'DPRandomGenerator', 'UltimateSDUpscale', 'easy loraStackApply', 'LoadImage', 'ImpactInt', 'LoRA Stack to String converter', 'Load Text File', 'FaceDetailer', 'UpscaleModelLoader', 'UltralyticsDetectorProvider', 'easy loraStack', 'easy float', 'CLIPTextEncode', 'Random Number', 'HighRes-Fix Script', 'SDPromptSaver', 'easy negative', 'easy promptConcat', 'EmptyLatentImage', 'Prompts', 'SDAnyConverter', 'easy positive'}", "workflow_format": "unknown", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0, "traceback": "Traceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 545, in extract_comfyui_workflow_params\n    connected_nodes = traverse_workflow_graph(workflow)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 475, in traverse_workflow_graph\n    raise ValueError(f\"No output nodes found in workflow. Available node types: {set(available_types)}\")\nValueError: No output nodes found in workflow. Available node types: {'ImpactWildcardProcessor', 'TilePreprocessor', 'easy imageColorMatch', 'Reroute', 'Number to String', 'ImpactImageInfo', 'ImageScale', 'RescaleCFG', 'JoinStrings', 'ControlNetLoader', 'ModelSamplingSD3', 'SDParameterGenerator', 'KSampler Adv. (Efficient)', 'ShowText|pysssss', 'SAMLoader', 'TIPO', 'ControlNetApplySD3', 'String to Text', 'DPRandomGenerator', 'UltimateSDUpscale', 'easy loraStackApply', 'LoadImage', 'ImpactInt', 'LoRA Stack to String converter', 'Load Text File', 'FaceDetailer', 'UpscaleModelLoader', 'UltralyticsDetectorProvider', 'easy loraStack', 'easy float', 'CLIPTextEncode', 'Random Number', 'HighRes-Fix Script', 'SDPromptSaver', 'easy negative', 'easy promptConcat', 'EmptyLatentImage', 'Prompts', 'SDAnyConverter', 'easy positive'}\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\comprehensive_validation_test.py\", line 135, in validate_single_image\n    info = image_info(img, image_path, enhanced_prompts=True)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 778, in image_info\n    workflow_params = extract_comfyui_workflow_params(workflow_json)\n  File \"F:\\Code\\PlayGround\\knowledgebase\\mcts_prompt_gen\\src\\utils.py\", line 547, in extract_comfyui_workflow_params\n    raise ValueError(f\"Failed to traverse workflow graph: {e}\")\nValueError: Failed to traverse workflow graph: No output nodes found in workflow. Available node types: {'ImpactWildcardProcessor', 'TilePreprocessor', 'easy imageColorMatch', 'Reroute', 'Number to String', 'ImpactImageInfo', 'ImageScale', 'RescaleCFG', 'JoinStrings', 'ControlNetLoader', 'ModelSamplingSD3', 'SDParameterGenerator', 'KSampler Adv. (Efficient)', 'ShowText|pysssss', 'SAMLoader', 'TIPO', 'ControlNetApplySD3', 'String to Text', 'DPRandomGenerator', 'UltimateSDUpscale', 'easy loraStackApply', 'LoadImage', 'ImpactInt', 'LoRA Stack to String converter', 'Load Text File', 'FaceDetailer', 'UpscaleModelLoader', 'UltralyticsDetectorProvider', 'easy loraStack', 'easy float', 'CLIPTextEncode', 'Random Number', 'HighRes-Fix Script', 'SDPromptSaver', 'easy negative', 'easy promptConcat', 'EmptyLatentImage', 'Prompts', 'SDAnyConverter', 'easy positive'}\n"}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\ComfyUI_00094_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.6", "luce2_Noob75XL.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\ComfyUI_00081_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17", "success": true, "loras_found": ["luce2_Noob75XL.safetensors:0.8", "Yanami XL kohaku zeta.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\ComfyUI_00103_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17", "success": true, "loras_found": ["luce2_Noob75XL.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 6, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\ComfyUI_00017_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\ComfyUI_00069_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17", "success": true, "loras_found": ["luce2_Noob75XL.safetensors:0.6", "Yanami XL kohaku zeta.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 6, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\ComfyUI_00034_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\ComfyUI_00051_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\ComfyUI_00086_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.6", "luce2_Noob75XL.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\ComfyUI_00091_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.6", "luce2_Noob75XL.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel\\ComfyUI_00090_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel", "success": true, "loras_found": ["Yanami XL kohaku zeta.safetensors:0.6", "luce2_Noob75XL.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\ComfyUI_00052_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel\\ComfyUI_00012_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel\\ComfyUI_00038_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\ComfyUI_00017_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\ComfyUI_00009_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel\\ComfyUI_00011_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel\\ComfyUI_00032_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 6, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\ComfyUI_00016_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\ComfyUI_00005_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\ComfyUI_00017_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel\\ComfyUI_00018_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel\\ComfyUI_00022_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel\\ComfyUI_00022_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel\\ComfyUI_00018_s.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01\\ComfyUI_00007_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel\\ComfyUI_00031_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2024-12-26\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:-0.2", "a31_style_koni-000010.safetensors:0.4", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9", "XL_mutsuki_face_(blue_archive)(a3.1)-000004.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 16, "total_lora_nodes": 4, "connected_lora_nodes": 4}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01\\ComfyUI_00006_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01\\ComfyUI_00004_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-01", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\ComfyUI_00022_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05", "success": true, "loras_found": ["<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0", "outline_xl_kohaku_delta_spv5x.safetensors:0.05"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\ComfyUI_00020_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05", "success": true, "loras_found": ["<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0", "outline_xl_kohaku_delta_spv5x.safetensors:0.05"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\ComfyUI_00012_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05", "success": true, "loras_found": ["<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0", "outline_xl_kohaku_delta_spv5x.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\ComfyUI_00015_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05", "success": true, "loras_found": ["<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0", "outline_xl_kohaku_delta_spv5x.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel\\ComfyUI_00019_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel", "success": true, "loras_found": ["<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0", "outline_xl_kohaku_delta_spv5x.safetensors:0.05"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\ComfyUI_00007_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05", "success": true, "loras_found": ["a31_style_koni-000010.safetensors:0.2", "outline_xl_kohaku_delta_spv5x.safetensors:-0.1", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 3, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel\\ComfyUI_00042_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel", "success": true, "loras_found": ["MyGo_IlluXL.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 4, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel\\ComfyUI_00020_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel", "success": true, "loras_found": ["<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0", "outline_xl_kohaku_delta_spv5x.safetensors:0.05"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\ComfyUI_temp_ryqes_00070_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10", "success": true, "loras_found": ["rimochan-ePred110.safetensors:0.8", "fufu-IL01.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel\\ComfyUI_00043_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel", "success": true, "loras_found": ["MyGo_IlluXL.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 4, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\ComfyUI_temp_ryqes_00036_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10", "success": true, "loras_found": ["niachan-ePred075.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 9, "total_lora_nodes": 4, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel\\ComfyUI_00010_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-01-05\\sel", "success": true, "loras_found": ["a31_style_koni-000010.safetensors:0.2", "outline_xl_kohaku_delta_spv5x.safetensors:-0.1", "<PERSON><PERSON><PERSON>_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:1.0"], "error": null, "workflow_format": "api_format", "connected_nodes": 13, "total_lora_nodes": 3, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\ComfyUI_temp_ryqes_00015_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10", "success": true, "loras_found": ["niachan-ePred075.safetensors:0.8", "fufu-IL01.safetensors:0.1"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\ComfyUI_temp_ryqes_00044_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10", "success": true, "loras_found": ["niachan-ePred075.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 9, "total_lora_nodes": 4, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\ComfyUI_temp_ryqes_00056_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10", "success": true, "loras_found": ["rimochan-ePred110.safetensors:0.8", "fufu-IL01.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel\\ComfyUI_temp_ryqes_00056_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel", "success": true, "loras_found": ["rimochan-ePred110.safetensors:0.8", "fufu-IL01.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel\\ComfyUI_temp_ryqes_00077_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel", "success": true, "loras_found": ["rimochan-ePred110.safetensors:0.8", "fufu-IL01.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel\\ComfyUI_temp_ryqes_00056_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel", "success": true, "loras_found": ["rimochan-ePred110.safetensors:0.8", "fufu-IL01.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel\\ComfyUI_temp_ryqes_00078_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel", "success": true, "loras_found": ["rimochan-ePred110.safetensors:0.8", "fufu-IL01.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel\\ComfyUI_temp_ryqes_00063_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-10\\sel", "success": true, "loras_found": ["rimochan-ePred110.safetensors:0.8", "fufu-IL01.safetensors:0.2"], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 4, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\ComfyUI_00004_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 3, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\ComfyUI_00008_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 3, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\ComfyUI_00009_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy\\ComfyUI_00010_.png.noisy.pixel8.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy\\ComfyUI_00010_.png.pixel4.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\ComfyUI_00001_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 3, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\ComfyUI_00010_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy\\ComfyUI_00010_.png.noisy.pixel5.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp\\a2.jpg", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp\\b.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy\\ComfyUI_00010_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp\\b2.jpg", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy\\ComfyUI_00010_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp\\ComfyUI_00007_.png.pixel4.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp\\b.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-16\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\ComfyUI_00032_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9", "rimocookiequq-ePred075.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\ComfyUI_00006_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\ComfyUI_00026_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9", "rimocookiequq-ePred075.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\ComfyUI_00020_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\ComfyUI_00015_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel\\ComfyUI_00019_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel\\ComfyUI_00019_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel\\ComfyUI_00028_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9", "rimocookiequq-ePred075.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel\\ComfyUI_00012_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\ComfyUI_00012_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18", "success": true, "loras_found": ["YeFlower.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel\\ComfyUI_00030_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-17\\sel", "success": true, "loras_found": ["neuro_NoobVpred05.safetensors:0.9", "rimocookiequq-ePred075.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18", "success": true, "loras_found": ["YeFlower.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\ComfyUI_00040_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18", "success": true, "loras_found": ["YeFlower.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\ComfyUI_00047_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18", "success": true, "loras_found": ["YeFlower.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\ComfyUI_00015_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18", "success": true, "loras_found": ["YeFlower.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\ComfyUI_00024_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\ComfyUI_00024_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\ComfyUI_00009_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\ComfyUI_00016_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel", "success": true, "loras_found": ["YeFlower.safetensors:0.9"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel\\ComfyUI_00016_.png.pixel4.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel\\ComfyUI_00018_s.png.pixel4.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel\\ComfyUI_00024_.png.pixel4.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel\\ComfyUI_00024_.png.pixel4.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\ComfyUI_00018_s.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel\\ComfyUI_00009_.png.pixel4.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-18\\sel\\pixel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19\\ComfyUI_00017_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19", "success": true, "loras_found": ["roxy_migurdia_offset.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19\\ComfyUI_00017_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19", "success": true, "loras_found": ["roxy_migurdia_offset.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19\\ComfyUI_00011_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19", "success": true, "loras_found": ["roxy_migurdia_offset.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19\\ComfyUI_00011_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19", "success": true, "loras_found": ["roxy_migurdia_offset.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19\\ComfyUI_00014_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-19", "success": true, "loras_found": ["roxy_migurdia_offset.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27\\ComfyUI_00007_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27\\ComfyUI_00004_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27\\ComfyUI_00005_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27\\ComfyUI_00008_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-02-27", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\ComfyUI_00015_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\ComfyUI_00024_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\ComfyUI_00007_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\ComfyUI_00008_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\ComfyUI_00002_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 1, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\ComfyUI_00024_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\ComfyUI_00027_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\ComfyUI_00018_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\ComfyUI_00027_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\ComfyUI_00013_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel", "success": true, "loras_found": ["outline_xl_kohaku_delta_spv5x.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy\\ComfyUI_00024_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy\\ComfyUI_00024_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy\\ComfyUI_00020_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy\\ComfyUI_00027_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\ComfyUI_00019_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy\\ComfyUI_00013_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-21\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\ComfyUI_00088_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\ComfyUI_00068_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\ComfyUI_00016_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\ComfyUI_00012_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\ComfyUI_00059_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\ComfyUI_00076_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\ComfyUI_00010_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\ComfyUI_00070_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\ComfyUI_00055_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy\\ComfyUI_00019_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy\\ComfyUI_00012_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy\\ComfyUI_00052_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy\\ComfyUI_00057_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy\\ComfyUI_00038_.png.noisy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\noisy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp\\ComfyUI_00010_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp\\ComfyUI_00038_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp\\ComfyUI_00019_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\ComfyUI_00231_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp\\ComfyUI_00024_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp\\ComfyUI_00057_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-03-22\\sel\\sel\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 10, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\ComfyUI_00203_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\ComfyUI_00045_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.6", "arknights_mon3tr_IL_v1.1.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\ComfyUI_00047_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.6", "arknights_mon3tr_IL_v1.1.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\ComfyUI_00243_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\ComfyUI_00226_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\ComfyUI_00232_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\ComfyUI_00234_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\ComfyUI_00070_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\ComfyUI_00090_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\ComfyUI_00020_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\ComfyUI_00086_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\ComfyUI_00204_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\ComfyUI_00070_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\ComfyUI_00086_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\ComfyUI_00236_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\ComfyUI_00036_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\ComfyUI_00232_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\ComfyUI_00086_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel\\ComfyUI_00036_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel\\ComfyUI_00076_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel\\ComfyUI_00004_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel\\ComfyUI_00090_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel\\ComfyUI_00230_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\paperfy\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\ComfyUI_00208_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp\\ComfyUI_00076_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp\\ComfyUI_00020_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.3"], "error": null, "workflow_format": "api_format", "connected_nodes": 14, "total_lora_nodes": 1, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2\\ComfyUI_00153_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2\\ComfyUI_00233_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp\\ComfyUI_00182_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2\\ComfyUI_00129_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2\\ComfyUI_00065_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2\\ComfyUI_00207_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp2", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\ComfyUI_00025_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\ComfyUI_00039_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\ComfyUI_00022_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\ComfyUI_00028_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp\\ComfyUI_00040_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.6", "arknights_mon3tr_IL_v1.1.safetensors:0.6"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\ComfyUI_00052_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp\\ComfyUI_00180_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-05\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.4", "arknights_mon3tr_IL_v1.1.safetensors:0.4"], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 2, "connected_lora_nodes": 2}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy\\ComfyUI_00046_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy\\ComfyUI_00018_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy\\ComfyUI_00022_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp\\ComfyUI_00022_e.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp\\ComfyUI_00029_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy\\ComfyUI_00041_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp\\ComfyUI_00027_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp\\ComfyUI_00022_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy\\ComfyUI_00029_e.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp\\ComfyUI_00030_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-09\\sel\\tmp", "success": true, "loras_found": ["arknights_mon3tr_IL_v1.1.safetensors:0.5", "Mon3tr-V2-P.safetensors:0.5", "Bbmasa_IL.safetensors:0.7"], "error": null, "workflow_format": "api_format", "connected_nodes": 12, "total_lora_nodes": 7, "connected_lora_nodes": 3}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\ComfyUI_00061_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22", "success": true, "loras_found": ["roxy_migurdia_offset.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\ComfyUI_00169_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22", "success": true, "loras_found": ["prts_sn59.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\ComfyUI_00081_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22", "success": true, "loras_found": ["prts_sn59.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\ComfyUI_00092_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22", "success": true, "loras_found": ["prts_sn59.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\ComfyUI_00176_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel", "success": true, "loras_found": ["prts_sn59.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\ComfyUI_00278_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\ComfyUI_00299_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\ComfyUI_00115_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel", "success": true, "loras_found": ["prts_sn59.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\ComfyUI_00283_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\ComfyUI_00078_.png.paperfy_s2_512.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\ComfyUI_00067_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\ComfyUI_00034_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\ComfyUI_00078_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out\\ComfyUI_00029_.png.paperfy.pngs512.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\ComfyUI_00078_.png.paperfy.s32.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out\\ComfyUI_00044_.png.paperfy.pngs512.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out\\ComfyUI_00040_.png.paperfy.pngs512.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out\\ComfyUI_00067_.png.paperfy.pngs512.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out\\ComfyUI_00072_.png.paperfy.pngs512.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\paperfy\\out", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\ComfyUI_00298_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\ComfyUI_00300_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\ComfyUI_00290_e.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\ComfyUI_00233_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel", "success": true, "loras_found": ["prts_sn59.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\ComfyUI_00275_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy\\ComfyUI_00298_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy\\ComfyUI_00233_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy\\ComfyUI_00235_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp\\ComfyUI_00059_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp\\ComfyUI_00034_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy\\ComfyUI_00282_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp\\ComfyUI_00063_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp\\ComfyUI_00062_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp\\ComfyUI_00078_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\tmp", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\ComfyUI_00016_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25", "success": true, "loras_found": ["prts_sn59.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\ComfyUI_00023_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\ComfyUI_00032_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\ComfyUI_00025_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\ComfyUI_00027_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\ComfyUI_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy\\ComfyUI_00234_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-22\\sel\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\ComfyUI_00032_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\ComfyUI_00007_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\ComfyUI_00002_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\ComfyUI_00038_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel", "success": true, "loras_found": ["Aoba - Blue Archive (内海アオバ).safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 11, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy\\ComfyUI_00014_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy\\ComfyUI_00027_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy\\ComfyUI_00045_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26\\ComfyUI_00001_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 24, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy\\ComfyUI_00038_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26\\ComfyUI_00004_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 27, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26\\ComfyUI_00005_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 27, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26\\ComfyUI_00004_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 27, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga\\screentone_coarse_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 17, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26\\ComfyUI_00002_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-26", "success": true, "loras_found": ["Mon3tr-V2-P.safetensors:0.8"], "error": null, "workflow_format": "api_format", "connected_nodes": 24, "total_lora_nodes": 2, "connected_lora_nodes": 1}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga\\screentone_final_00003_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 17, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy\\ComfyUI_00032_.png.paperfy.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\2025-04-25\\sel\\paperfy", "success": true, "loras_found": [], "error": null, "workflow_format": "no_workflow", "connected_nodes": 0, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga\\screentone_colorized_00001_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga\\screentone_coarse_00001_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 0, "connected_lora_nodes": 0}, {"image_path": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga\\screentone_colorized_00002_.png", "source_dir": "F:\\SD-webui\\ComfyUI\\output\\sketch2manga", "success": true, "loras_found": [], "error": null, "workflow_format": "api_format", "connected_nodes": 15, "total_lora_nodes": 0, "connected_lora_nodes": 0}]
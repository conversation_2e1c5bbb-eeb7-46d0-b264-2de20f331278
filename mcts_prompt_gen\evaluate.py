import torch
import numpy as np
import argparse
import os
import json
from transformers import <PERSON>Tokenizer

from models import ResNetModel, LightGBMModel, LLMWrapper
from mcts import MCTS
from environment import PromptEnvironment
from data import RewardModel

def evaluate_model(model, env, num_prompts=10, use_mcts=False, temperature=0.8):
    """Evaluate model by generating prompts and calculating rewards."""
    rewards = []
    prompts = []
    
    for _ in range(num_prompts):
        if use_mcts:
            mcts = MCTS(model, num_simulations=100)
            prompt = env.generate_prompt(model, mcts=mcts, temperature=temperature)
        else:
            prompt = env.generate_prompt(model, temperature=temperature)
        
        reward = env.reward_model.evaluate(prompt)
        rewards.append(reward)
        prompts.append(prompt)
        
        print(f"Prompt: {prompt}")
        print(f"Reward: {reward}")
        print("-" * 50)
    
    avg_reward = np.mean(rewards)
    print(f"Average Reward: {avg_reward:.4f}")
    
    return prompts, rewards, avg_reward

def compare_models(models, env, num_prompts=10, temperature=0.8):
    """Compare different models with and without MCTS."""
    results = {}
    
    for model_name, model in models.items():
        print(f"Evaluating {model_name} without MCTS")
        _, rewards, avg_reward = evaluate_model(
            model, env, num_prompts, use_mcts=False, temperature=temperature
        )
        results[f"{model_name}_no_mcts"] = avg_reward
        
        print(f"Evaluating {model_name} with MCTS")
        _, rewards, avg_reward = evaluate_model(
            model, env, num_prompts, use_mcts=True, temperature=temperature
        )
        results[f"{model_name}_mcts"] = avg_reward
    
    # Print comparison
    print("\nModel Comparison:")
    for model_name, avg_reward in results.items():
        print(f"{model_name}: {avg_reward:.4f}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Evaluate prompt generation models")
    parser.add_argument("--model_dir", type=str, required=True, help="Directory containing trained models")
    parser.add_argument("--num_prompts", type=int, default=10, help="Number of prompts to generate")
    parser.add_argument("--temperature", type=float, default=0.8, help="Sampling temperature")
    parser.add_argument("--max_length", type=int, default=50, help="Maximum prompt length")
    parser.add_argument("--output_file", type=str, default="evaluation_results.json", help="Output file for results")
    parser.add_argument("--llm_model", type=str, default="gpt2", help="Pretrained LLM model name")
    
    args = parser.parse_args()
    
    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained(args.llm_model)
    
    # Initialize reward model
    reward_model = RewardModel()
    
    # Initialize environment
    env = PromptEnvironment(tokenizer, reward_model, args.max_length)
    
    # Load models
    device = "cuda" if torch.cuda.is_available() else "cpu"
    models = {}
    
    # ResNet model
    resnet_path = os.path.join(args.model_dir, "resnet_model.pt")
    if os.path.exists(resnet_path):
        input_dim = args.max_length * tokenizer.vocab_size
        resnet_model = ResNetModel(input_dim).to(device)
        resnet_model.load_state_dict(torch.load(resnet_path, map_location=device))
        resnet_model.eval()
        models["ResNet"] = resnet_model
    
    # LightGBM model
    lightgbm_path = os.path.join(args.model_dir, "lightgbm_model")
    if os.path.exists(lightgbm_path):
        lightgbm_model = LightGBMModel()
        # Load LightGBM model (implementation depends on how it was saved)
        models["LightGBM"] = lightgbm_model
    
    # LLM model
    models["LLM"] = LLMWrapper(args.llm_model, device)
    
    # Compare models
    results = compare_models(models, env, args.num_prompts, args.temperature)
    
    # Save results
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=4)

if __name__ == "__main__":
    main()
import numpy as np
from typing import List, Dict, Any, Tuple

class PromptState:
    def __init__(self, max_length=50, vocab_size=10000, initial_prompt=None):
        self.max_length = max_length
        self.vocab_size = vocab_size
        self.current_tokens = [] if initial_prompt is None else initial_prompt
        
    def apply(self, action):
        """Apply action (add token) to state and return new state."""
        new_state = PromptState(self.max_length, self.vocab_size)
        new_state.current_tokens = self.current_tokens.copy()
        
        if len(new_state.current_tokens) < self.max_length:
            new_state.current_tokens.append(action)
            
        return new_state
    
    def legal_actions(self):
        """Return list of legal actions (tokens that can be added)."""
        if len(self.current_tokens) >= self.max_length:
            return []  # No more tokens can be added
        
        # In a real implementation, you might want to filter based on grammar or context
        return list(range(self.vocab_size))
    
    def is_terminal(self):
        """Check if state is terminal (prompt is complete)."""
        # Terminal if max length reached or special end token encountered
        return len(self.current_tokens) >= self.max_length or \
               (len(self.current_tokens) > 0 and self.current_tokens[-1] == 1)  # Assuming 1 is EOS token
    
    def features(self):
        """Convert state to feature representation for model input."""
        # One-hot encoding of tokens
        features = np.zeros((self.max_length, self.vocab_size))
        for i, token in enumerate(self.current_tokens):
            if i < self.max_length:
                features[i, token] = 1
        return features.flatten()
    
    def to_text(self, tokenizer):
        """Convert token sequence to text using tokenizer."""
        return tokenizer.decode(self.current_tokens)

class PromptEnvironment:
    def __init__(self, tokenizer, reward_model, max_length=50):
        self.tokenizer = tokenizer
        self.reward_model = reward_model
        self.max_length = max_length
        self.vocab_size = tokenizer.vocab_size
        
    def reset(self, initial_prompt=None):
        """Reset environment with optional initial prompt."""
        if initial_prompt is not None and isinstance(initial_prompt, str):
            initial_tokens = self.tokenizer.encode(initial_prompt)
        else:
            initial_tokens = initial_prompt
        return PromptState(self.max_length, self.vocab_size, initial_tokens)
    
    def step(self, state, action):
        """Take action in state and return new state and reward."""
        new_state = state.apply(action)
        reward = 0
        
        if new_state.is_terminal():
            # Only evaluate complete prompts
            prompt_text = new_state.to_text(self.tokenizer)
            reward = self.reward_model.evaluate(prompt_text)
            
        return new_state, reward
    
    def generate_prompt(self, model, mcts=None, initial_prompt=None, temperature=1.0):
        """Generate a complete prompt using the given model and optional MCTS."""
        state = self.reset(initial_prompt)
        
        while not state.is_terminal():
            if mcts is not None:
                # Use MCTS to select action
                actions, probs = mcts.search(state)
                
                # Sample action based on probabilities
                if temperature == 0:
                    action = actions[np.argmax(probs)]
                else:
                    probs = np.power(probs, 1.0 / temperature)
                    probs /= probs.sum()
                    action = np.random.choice(actions, p=probs)
            else:
                # Use model directly to predict next token
                policy, _ = model.predict(state.features())
                legal_actions = state.legal_actions()
                
                # Filter and normalize policy for legal actions
                legal_probs = np.array([policy[a] for a in legal_actions])
                legal_probs = np.power(legal_probs, 1.0 / temperature)
                legal_probs /= legal_probs.sum()
                
                action = np.random.choice(legal_actions, p=legal_probs)
            
            state, _ = self.step(state, action)
            
        return state.to_text(self.tokenizer)
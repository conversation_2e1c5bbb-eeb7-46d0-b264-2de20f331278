#!/usr/bin/env python3
"""
Data Validation and Preparation Pipeline

This script validates the promptlabels.pkl file and prepares the data for machine learning.
Implements comprehensive data quality checks, visualization, and train/test splitting.

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import List, Tuple, Dict, Any
from collections import Counter
import dill
from sklearn.model_selection import train_test_split
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")


class DataValidator:
    """Comprehensive data validation and preparation pipeline."""
    
    def __init__(self, data_path: str = "promptlabels.pkl", results_dir: str = "results"):
        self.data_path = data_path
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(exist_ok=True)
        
        # Create timestamped subdirectory for this run
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = self.results_dir / f"validation_{timestamp}"
        self.run_dir.mkdir(exist_ok=True)
        
        self.raw_data = None
        self.df = None
        self.stats = {}
        
        print(f"Results will be saved to: {self.run_dir}")
    
    def load_and_validate_data(self) -> bool:
        """Load and perform basic validation of the dataset."""
        print("="*60)
        print("PHASE 1: DATA LOADING AND VALIDATION")
        print("="*60)
        
        try:
            with open(self.data_path, 'rb') as f:
                self.raw_data = dill.load(f)
            print(f"✓ Successfully loaded {len(self.raw_data)} entries from {self.data_path}")
        except Exception as e:
            print(f"✗ Error loading data: {e}")
            return False
        
        # Validate data structure
        if not self.raw_data:
            print("✗ Dataset is empty!")
            return False
        
        # Check tuple structure
        first_entry = self.raw_data[0]
        if not isinstance(first_entry, tuple) or len(first_entry) != 3:
            print(f"✗ Invalid data structure. Expected tuples of length 3, got: {type(first_entry)}")
            return False
        
        # Validate data types
        invalid_entries = []
        for i, (filename, prompt, goodness) in enumerate(self.raw_data):
            if not all(isinstance(x, str) for x in [filename, prompt, goodness]):
                invalid_entries.append(i)
            if goodness not in ['good', 'normal']:
                print(f"Warning: Unexpected goodness value '{goodness}' at entry {i}")
        
        if invalid_entries:
            print(f"✗ Found {len(invalid_entries)} entries with invalid data types")
            return False
        
        print("✓ Data structure validation passed")
        
        # Convert to DataFrame for easier analysis
        self.df = pd.DataFrame(self.raw_data, columns=['filename', 'prompt', 'goodness'])
        
        # Basic statistics
        self.stats['total_entries'] = len(self.df)
        self.stats['good_count'] = (self.df['goodness'] == 'good').sum()
        self.stats['normal_count'] = (self.df['goodness'] == 'normal').sum()
        self.stats['empty_prompts'] = (self.df['prompt'].str.strip() == '').sum()
        self.stats['non_empty_prompts'] = self.stats['total_entries'] - self.stats['empty_prompts']
        
        print(f"✓ Dataset converted to DataFrame: {self.df.shape}")
        return True
    
    def analyze_data_quality(self):
        """Perform comprehensive data quality analysis."""
        print("\n" + "="*60)
        print("DATA QUALITY ANALYSIS")
        print("="*60)
        
        # Basic statistics
        print(f"Total entries: {self.stats['total_entries']:,}")
        print(f"Good quality: {self.stats['good_count']:,} ({self.stats['good_count']/self.stats['total_entries']*100:.1f}%)")
        print(f"Normal quality: {self.stats['normal_count']:,} ({self.stats['normal_count']/self.stats['total_entries']*100:.1f}%)")
        print(f"Empty prompts: {self.stats['empty_prompts']:,} ({self.stats['empty_prompts']/self.stats['total_entries']*100:.1f}%)")
        print(f"Valid prompts: {self.stats['non_empty_prompts']:,} ({self.stats['non_empty_prompts']/self.stats['total_entries']*100:.1f}%)")
        
        # Prompt length analysis
        self.df['prompt_length'] = self.df['prompt'].str.len()
        non_empty_df = self.df[self.df['prompt'].str.strip() != '']
        
        if len(non_empty_df) > 0:
            self.stats['avg_prompt_length'] = non_empty_df['prompt_length'].mean()
            self.stats['median_prompt_length'] = non_empty_df['prompt_length'].median()
            self.stats['max_prompt_length'] = non_empty_df['prompt_length'].max()
            self.stats['min_prompt_length'] = non_empty_df['prompt_length'].min()
            
            print(f"\nPrompt Length Statistics (non-empty prompts):")
            print(f"  Average: {self.stats['avg_prompt_length']:.1f} characters")
            print(f"  Median: {self.stats['median_prompt_length']:.1f} characters")
            print(f"  Range: {self.stats['min_prompt_length']} - {self.stats['max_prompt_length']} characters")
            
            # Length by quality
            good_lengths = non_empty_df[non_empty_df['goodness'] == 'good']['prompt_length']
            normal_lengths = non_empty_df[non_empty_df['goodness'] == 'normal']['prompt_length']
            
            if len(good_lengths) > 0 and len(normal_lengths) > 0:
                print(f"  Good prompts avg length: {good_lengths.mean():.1f}")
                print(f"  Normal prompts avg length: {normal_lengths.mean():.1f}")
        
        # File extension analysis
        self.df['file_extension'] = self.df['filename'].apply(lambda x: Path(x).suffix.lower())
        ext_counts = self.df['file_extension'].value_counts()
        print(f"\nFile Extension Distribution:")
        for ext, count in ext_counts.head(5).items():
            print(f"  {ext}: {count:,} files ({count/len(self.df)*100:.1f}%)")
        
        # Directory analysis
        self.df['parent_dir'] = self.df['filename'].apply(lambda x: Path(x).parent.name)
        dir_counts = self.df['parent_dir'].value_counts()
        print(f"\nTop 5 Source Directories:")
        for dir_name, count in dir_counts.head(5).items():
            print(f"  {dir_name}: {count:,} files")
        
        # Save statistics
        stats_file = self.run_dir / "data_statistics.txt"
        with open(stats_file, 'w') as f:
            f.write("Dataset Statistics\n")
            f.write("="*50 + "\n\n")
            for key, value in self.stats.items():
                f.write(f"{key}: {value}\n")
        
        print(f"\n✓ Statistics saved to: {stats_file}")
    
    def create_visualizations(self):
        """Create comprehensive data visualizations."""
        print("\n" + "="*60)
        print("CREATING VISUALIZATIONS")
        print("="*60)
        
        # Set up the plotting
        fig_size = (15, 12)
        fig, axes = plt.subplots(2, 3, figsize=fig_size)
        fig.suptitle('Dataset Analysis Dashboard', fontsize=16, fontweight='bold')
        
        # 1. Class distribution pie chart
        ax1 = axes[0, 0]
        class_counts = self.df['goodness'].value_counts()
        colors = ['#ff9999', '#66b3ff']
        ax1.pie(class_counts.values, labels=class_counts.index, autopct='%1.1f%%', 
                colors=colors, startangle=90)
        ax1.set_title('Quality Label Distribution')
        
        # 2. Prompt length distribution
        ax2 = axes[0, 1]
        non_empty_df = self.df[self.df['prompt'].str.strip() != '']
        if len(non_empty_df) > 0:
            ax2.hist(non_empty_df['prompt_length'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            ax2.set_xlabel('Prompt Length (characters)')
            ax2.set_ylabel('Frequency')
            ax2.set_title('Prompt Length Distribution')
            ax2.axvline(non_empty_df['prompt_length'].mean(), color='red', linestyle='--', 
                       label=f'Mean: {non_empty_df["prompt_length"].mean():.0f}')
            ax2.legend()
        
        # 3. Prompt length by quality
        ax3 = axes[0, 2]
        if len(non_empty_df) > 0:
            good_lengths = non_empty_df[non_empty_df['goodness'] == 'good']['prompt_length']
            normal_lengths = non_empty_df[non_empty_df['goodness'] == 'normal']['prompt_length']
            
            ax3.hist([good_lengths, normal_lengths], bins=30, alpha=0.7, 
                    label=['Good', 'Normal'], color=['green', 'orange'])
            ax3.set_xlabel('Prompt Length (characters)')
            ax3.set_ylabel('Frequency')
            ax3.set_title('Prompt Length by Quality')
            ax3.legend()
        
        # 4. File extension distribution
        ax4 = axes[1, 0]
        ext_counts = self.df['file_extension'].value_counts().head(10)
        ax4.bar(range(len(ext_counts)), ext_counts.values, color='lightcoral')
        ax4.set_xticks(range(len(ext_counts)))
        ax4.set_xticklabels(ext_counts.index, rotation=45)
        ax4.set_ylabel('Count')
        ax4.set_title('File Extension Distribution')
        
        # 5. Quality distribution by directory
        ax5 = axes[1, 1]
        top_dirs = self.df['parent_dir'].value_counts().head(8).index
        dir_quality = self.df[self.df['parent_dir'].isin(top_dirs)].groupby(['parent_dir', 'goodness']).size().unstack(fill_value=0)
        dir_quality.plot(kind='bar', stacked=True, ax=ax5, color=['orange', 'green'])
        ax5.set_xlabel('Directory')
        ax5.set_ylabel('Count')
        ax5.set_title('Quality Distribution by Directory')
        ax5.legend(title='Quality')
        ax5.tick_params(axis='x', rotation=45)
        
        # 6. Empty vs Non-empty prompts
        ax6 = axes[1, 2]
        empty_counts = [self.stats['empty_prompts'], self.stats['non_empty_prompts']]
        labels = ['Empty Prompts', 'Valid Prompts']
        ax6.pie(empty_counts, labels=labels, autopct='%1.1f%%', colors=['red', 'lightgreen'])
        ax6.set_title('Prompt Validity Distribution')
        
        plt.tight_layout()
        
        # Save the dashboard
        dashboard_file = self.run_dir / "data_analysis_dashboard.png"
        plt.savefig(dashboard_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Analysis dashboard saved to: {dashboard_file}")
        
        # Create additional detailed plots
        self._create_detailed_plots()
    
    def _create_detailed_plots(self):
        """Create additional detailed visualizations."""
        non_empty_df = self.df[self.df['prompt'].str.strip() != '']
        
        if len(non_empty_df) == 0:
            print("⚠ No valid prompts found for detailed analysis")
            return
        
        # Prompt length comparison boxplot
        plt.figure(figsize=(10, 6))
        sns.boxplot(data=non_empty_df, x='goodness', y='prompt_length')
        plt.title('Prompt Length Distribution by Quality')
        plt.ylabel('Prompt Length (characters)')
        plt.xlabel('Quality Label')
        
        boxplot_file = self.run_dir / "prompt_length_boxplot.png"
        plt.savefig(boxplot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Boxplot saved to: {boxplot_file}")
    
    def prepare_train_test_split(self, test_size: float = 0.2, random_state: int = 42):
        """Create stratified train/test split and save the datasets."""
        print("\n" + "="*60)
        print("TRAIN/TEST SPLIT PREPARATION")
        print("="*60)
        
        # Filter out empty prompts for ML training
        valid_df = self.df[self.df['prompt'].str.strip() != ''].copy()
        
        if len(valid_df) == 0:
            print("✗ No valid prompts available for training!")
            return False
        
        print(f"Using {len(valid_df):,} samples with valid prompts for train/test split")
        
        # Prepare features and labels
        X = valid_df[['filename', 'prompt']].copy()
        y = valid_df['goodness'].copy()
        
        # Create stratified split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, 
            stratify=y, shuffle=True
        )
        
        # Create train and test DataFrames
        train_df = pd.concat([X_train, y_train], axis=1)
        test_df = pd.concat([X_test, y_test], axis=1)
        
        print(f"Train set: {len(train_df):,} samples")
        print(f"  - Good: {(train_df['goodness'] == 'good').sum():,}")
        print(f"  - Normal: {(train_df['goodness'] == 'normal').sum():,}")
        
        print(f"Test set: {len(test_df):,} samples")
        print(f"  - Good: {(test_df['goodness'] == 'good').sum():,}")
        print(f"  - Normal: {(test_df['goodness'] == 'normal').sum():,}")
        
        # Save the splits
        train_file = self.run_dir / "train_data.pkl"
        test_file = self.run_dir / "test_data.pkl"
        
        # Convert back to list of tuples format for consistency
        train_tuples = [(row['filename'], row['prompt'], row['goodness']) 
                       for _, row in train_df.iterrows()]
        test_tuples = [(row['filename'], row['prompt'], row['goodness']) 
                      for _, row in test_df.iterrows()]
        
        with open(train_file, 'wb') as f:
            dill.dump(train_tuples, f)
        
        with open(test_file, 'wb') as f:
            dill.dump(test_tuples, f)
        
        # Save split indices for reproducibility
        split_info = {
            'train_indices': X_train.index.tolist(),
            'test_indices': X_test.index.tolist(),
            'test_size': test_size,
            'random_state': random_state,
            'total_samples': len(valid_df),
            'train_samples': len(train_df),
            'test_samples': len(test_df)
        }
        
        split_info_file = self.run_dir / "split_info.pkl"
        with open(split_info_file, 'wb') as f:
            dill.dump(split_info, f)
        
        print(f"✓ Train data saved to: {train_file}")
        print(f"✓ Test data saved to: {test_file}")
        print(f"✓ Split info saved to: {split_info_file}")
        
        return True
    
    def run_full_validation(self):
        """Run the complete validation pipeline."""
        print("Starting comprehensive data validation pipeline...")
        
        if not self.load_and_validate_data():
            return False
        
        self.analyze_data_quality()
        self.create_visualizations()
        
        if not self.prepare_train_test_split():
            return False
        
        print("\n" + "="*60)
        print("VALIDATION PIPELINE COMPLETED SUCCESSFULLY")
        print("="*60)
        print(f"All results saved to: {self.run_dir}")
        
        return True


def main():
    """Main function with command-line interface."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Data validation and preparation pipeline")
    parser.add_argument('--data', '-d', default='promptlabels.pkl', 
                       help='Path to the promptlabels.pkl file')
    parser.add_argument('--results-dir', '-r', default='results',
                       help='Results directory')
    parser.add_argument('--test-size', '-t', type=float, default=0.2,
                       help='Test set size (default: 0.2)')
    parser.add_argument('--random-seed', '-s', type=int, default=42,
                       help='Random seed for reproducibility')
    
    args = parser.parse_args()
    
    # Create validator and run pipeline
    validator = DataValidator(args.data, args.results_dir)
    success = validator.run_full_validation()
    
    if success:
        print("\n🎉 Data validation completed successfully!")
        print("Ready to proceed with model training.")
    else:
        print("\n❌ Data validation failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()

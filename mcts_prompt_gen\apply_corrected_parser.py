#!/usr/bin/env python3
"""
Apply Corrected Parser to Structured Dataset

This script applies the corrected parser to fix the critical parsing issues
in the existing structured dataset, specifically addressing:
1. Massive concatenated LoRA parameters
2. Weight values embedded in tag names
3. Artist tags misclassified as LoRA models
4. Incorrect tag normalization

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import logging
from typing import List, Tuple, Dict, Any
from datetime import datetime
import traceback
from collections import Counter, defaultdict

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from corrected_prompt_parser import CorrectedPromptParser

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'corrected_parsing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StructuredDatasetCorrector:
    """Apply corrected parsing to structured dataset."""
    
    def __init__(self):
        """Initialize the corrector."""
        self.parser = CorrectedPromptParser()
        self.correction_stats = {
            'total_entries': 0,
            'entries_corrected': 0,
            'tags_before': 0,
            'tags_after': 0,
            'lora_tags_before': 0,
            'lora_tags_after': 0,
            'artist_tags_reclassified': 0,
            'concatenated_tags_split': 0,
            'weight_extractions': 0
        }
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def needs_correction(self, tags: List[Tuple[str, float]]) -> bool:
        """
        Check if an entry needs correction.
        
        Args:
            tags: List of (tag, weight) tuples
            
        Returns:
            True if correction is needed
        """
        for tag, weight in tags:
            # Check for problematic patterns
            if (len(tag) > 100 or  # Very long tags
                '--lora' in tag or  # Concatenated LoRA parameters
                ':' in tag and not tag.startswith('artist:') or  # Embedded weights
                tag.startswith('best_composition_--') or  # Concatenated parameters
                (tag.startswith('lora_') and any(artist in tag for artist in ['ciloranko', 'floral_print']))):  # Misclassified artist tags
                return True
        return False
    
    def reconstruct_prompt_from_tags(self, tags: List[Tuple[str, float]]) -> str:
        """
        Reconstruct a prompt string from structured tags for re-parsing.
        
        Args:
            tags: List of (tag, weight) tuples
            
        Returns:
            Reconstructed prompt string
        """
        prompt_parts = []
        
        for tag, weight in tags:
            # Handle different tag types
            if tag.startswith('lora_'):
                # Convert back to LoRA parameter format
                lora_name = tag[5:]  # Remove 'lora_' prefix
                if not lora_name.endswith(('.safetensors', '.ckpt')):
                    lora_name += '.safetensors'
                prompt_parts.append(f'--lora {lora_name}:{weight}')
            elif tag in ['cfg_value', 'cfg']:
                prompt_parts.append(f'--cfg {weight}')
            elif tag in ['sampling_steps', 'steps']:
                prompt_parts.append(f'--steps {int(weight)}')
            elif tag == 'width':
                # We'll handle width/height together
                continue
            elif tag == 'height':
                # Look for width in the same entry
                width_weight = None
                for t, w in tags:
                    if t == 'width':
                        width_weight = w
                        break
                if width_weight:
                    prompt_parts.append(f'--size {int(width_weight)}x{int(weight)}')
            elif weight == 1.0:
                # Regular tag
                prompt_parts.append(tag)
            elif weight == 1.1:
                prompt_parts.append(f'{{{tag}}}')
            elif weight == 1.2:
                prompt_parts.append(f'{{{{{tag}}}}}')
            elif weight == 1.3:
                prompt_parts.append(f'{{{{{{{tag}}}}}}}')
            elif weight == 0.9:
                prompt_parts.append(f'[{tag}]')
            elif weight == 0.8:
                prompt_parts.append(f'[[{tag}]]')
            else:
                # Explicit weight
                prompt_parts.append(f'({tag}:{weight})')
        
        return ', '.join(prompt_parts)
    
    def correct_entry_tags(self, tags: List[Tuple[str, float]]) -> List[Tuple[str, float]]:
        """
        Correct tags in a single entry.
        
        Args:
            tags: Original list of (tag, weight) tuples
            
        Returns:
            Corrected list of (tag, weight) tuples
        """
        if not self.needs_correction(tags):
            return tags
        
        self.correction_stats['entries_corrected'] += 1
        self.correction_stats['tags_before'] += len(tags)
        
        # Count original LoRA tags
        original_lora_count = sum(1 for tag, _ in tags if tag.startswith('lora_'))
        self.correction_stats['lora_tags_before'] += original_lora_count
        
        # Reconstruct prompt and re-parse
        reconstructed_prompt = self.reconstruct_prompt_from_tags(tags)
        corrected_tags = self.parser.parse_prompt_corrected(reconstructed_prompt)
        
        # Count corrections
        self.correction_stats['tags_after'] += len(corrected_tags)
        corrected_lora_count = sum(1 for tag, _ in corrected_tags if tag.startswith('lora_'))
        self.correction_stats['lora_tags_after'] += corrected_lora_count
        
        # Count specific correction types
        for tag, weight in tags:
            if len(tag) > 100:
                self.correction_stats['concatenated_tags_split'] += 1
            if ':' in tag and not tag.startswith('artist:'):
                self.correction_stats['weight_extractions'] += 1
            if tag.startswith('lora_') and any(artist in tag for artist in ['ciloranko', 'floral_print']):
                self.correction_stats['artist_tags_reclassified'] += 1
        
        return corrected_tags
    
    def correct_dataset(self, input_file: str, output_file: str) -> Dict[str, Any]:
        """
        Apply corrections to the entire dataset.
        
        Args:
            input_file: Path to problematic structured dataset
            output_file: Path to output corrected dataset
            
        Returns:
            Correction statistics
        """
        logger.info(f"Starting dataset correction: {input_file} -> {output_file}")
        
        # Load problematic dataset
        try:
            with open(input_file, 'rb') as f:
                problematic_data = dill.load(f)
            logger.info(f"Loaded {len(problematic_data)} entries from {input_file}")
            self.correction_stats['total_entries'] = len(problematic_data)
        except Exception as e:
            logger.error(f"Failed to load input file {input_file}: {e}")
            raise
        
        # Correct each entry
        corrected_data = []
        
        for i, entry in enumerate(problematic_data):
            try:
                if not isinstance(entry, (tuple, list)) or len(entry) != 3:
                    logger.warning(f"Invalid entry format at index {i}")
                    corrected_data.append(entry)
                    continue
                
                filename, tags, goodness = entry
                
                # Correct the tags
                corrected_tags = self.correct_entry_tags(tags)
                
                # Create corrected entry
                corrected_entry = (filename, corrected_tags, goodness)
                corrected_data.append(corrected_entry)
                
                # Log progress
                if (i + 1) % 1000 == 0:
                    logger.info(f"Corrected {i + 1}/{len(problematic_data)} entries")
                    
            except Exception as e:
                logger.error(f"Error correcting entry {i}: {e}")
                # Keep original entry on error
                corrected_data.append(entry)
        
        # Save corrected dataset
        try:
            with open(output_file, 'wb') as f:
                dill.dump(corrected_data, f)
            logger.info(f"Saved {len(corrected_data)} corrected entries to {output_file}")
        except Exception as e:
            logger.error(f"Failed to save output file {output_file}: {e}")
            raise
        
        # Generate final statistics
        final_stats = self._generate_correction_statistics(corrected_data)
        
        return final_stats
    
    def _generate_correction_statistics(self, corrected_data: List[Tuple]) -> Dict[str, Any]:
        """Generate comprehensive correction statistics."""
        stats = self.correction_stats.copy()
        
        # Analyze corrected data
        total_tags = 0
        unique_tags = set()
        lora_tags = []
        artist_tags = []
        
        for filename, tags, goodness in corrected_data:
            total_tags += len(tags)
            
            for tag, weight in tags:
                unique_tags.add(tag)
                
                if tag.startswith('lora_'):
                    lora_tags.append((tag, weight))
                elif any(artist in tag.lower() for artist in ['ciloranko', 'artist:', 'hiten', 'sy4']):
                    artist_tags.append((tag, weight))
        
        stats.update({
            'corrected_entries': len(corrected_data),
            'total_tags_final': total_tags,
            'unique_tags_final': len(unique_tags),
            'lora_tags_final': len(lora_tags),
            'artist_tags_final': len(artist_tags),
            'unique_lora_models': len(set(tag for tag, _ in lora_tags)),
            'avg_tags_per_entry': total_tags / len(corrected_data) if corrected_data else 0,
            'correction_rate': (stats['entries_corrected'] / stats['total_entries']) * 100 if stats['total_entries'] > 0 else 0,
            'lora_reduction': stats['lora_tags_before'] - stats['lora_tags_after'],
            'top_lora_models': Counter(tag for tag, _ in lora_tags).most_common(20),
            'top_artist_tags': Counter(tag for tag, _ in artist_tags).most_common(20)
        })
        
        return stats


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Apply corrected parsing to fix critical issues in structured dataset"
    )
    
    parser.add_argument("--input", required=True,
                       help="Input structured pickle file with parsing errors")
    parser.add_argument("--output", required=True,
                       help="Output pickle file for corrected dataset")
    parser.add_argument("--test-sample", type=int,
                       help="Test on a small sample first")
    parser.add_argument("--backup", action="store_true",
                       help="Create backup of original file")
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input):
        logger.error(f"Input file not found: {args.input}")
        return 1
    
    # Create backup if requested
    if args.backup:
        backup_file = f"{args.input}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            import shutil
            shutil.copy2(args.input, backup_file)
            logger.info(f"Created backup: {backup_file}")
        except Exception as e:
            logger.warning(f"Failed to create backup: {e}")
    
    # Initialize corrector
    corrector = StructuredDatasetCorrector()
    
    try:
        # Handle test sample
        if args.test_sample:
            logger.info(f"Running test on {args.test_sample} samples...")
            
            # Load and sample data
            with open(args.input, 'rb') as f:
                full_data = dill.load(f)
            
            import random
            test_data = random.sample(full_data, min(args.test_sample, len(full_data)))
            
            # Save test data temporarily
            test_input = f"test_sample_{args.test_sample}.pkl"
            with open(test_input, 'wb') as f:
                dill.dump(test_data, f)
            
            # Process test sample
            test_output = f"test_corrected_{args.test_sample}.pkl"
            stats = corrector.correct_dataset(test_input, test_output)
            
            # Clean up temporary files
            os.remove(test_input)
            
        else:
            # Process full dataset
            stats = corrector.correct_dataset(args.input, args.output)
        
        # Print summary
        print("\n" + "=" * 80)
        print("DATASET CORRECTION COMPLETED")
        print("=" * 80)
        print(f"Input file: {args.input}")
        print(f"Output file: {args.output}")
        print("")
        print("Correction Statistics:")
        print(f"  Total entries: {stats['total_entries']:,}")
        print(f"  Entries corrected: {stats['entries_corrected']:,}")
        print(f"  Correction rate: {stats['correction_rate']:.1f}%")
        print(f"  Tags before: {stats['tags_before']:,}")
        print(f"  Tags after: {stats['tags_after']:,}")
        print(f"  LoRA tags before: {stats['lora_tags_before']:,}")
        print(f"  LoRA tags after: {stats['lora_tags_after']:,}")
        print(f"  LoRA reduction: {stats['lora_reduction']:,}")
        print(f"  Artist tags reclassified: {stats['artist_tags_reclassified']:,}")
        print(f"  Concatenated tags split: {stats['concatenated_tags_split']:,}")
        print(f"  Weight extractions: {stats['weight_extractions']:,}")
        print("")
        
        # Show top LoRA models after correction
        if 'top_lora_models' in stats and stats['top_lora_models']:
            print("Top 10 LoRA Models After Correction:")
            for i, (lora, count) in enumerate(stats['top_lora_models'][:10], 1):
                print(f"  {i:2d}. {lora}: {count:,}")
        else:
            print("LoRA Models: None found after correction")
        
        print("")
        print("🎉 SUCCESS: Critical parsing issues have been corrected!")
        
        return 0
        
    except Exception as e:
        logger.error(f"Dataset correction failed: {e}")
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())

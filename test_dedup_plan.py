#!/usr/bin/env python3
"""
Test script to debug deduplication plan generation
"""

import dill
import logging
from dataset_duplicate_analyzer import DatasetDuplicateAnalyzer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dedup_plan():
    """Test the deduplication plan generation."""
    analyzer = DatasetDuplicateAnalyzer("production_dataset.pkl")
    
    # Load dataset
    if not analyzer.load_dataset():
        logger.error("Failed to load dataset")
        return
    
    # Run analyses
    logger.info("Running path analysis...")
    path_analysis = analyzer.analyze_file_paths()
    logger.info(f"Found {path_analysis['duplicate_count']} duplicate groups")
    
    logger.info("Running tag analysis...")
    tag_analysis = analyzer.analyze_tag_relevance()
    logger.info(f"Found {tag_analysis['irrelevant_count']} irrelevant entries")
    
    logger.info("Generating deduplication plan...")
    dedup_plan = analyzer.generate_deduplication_plan()
    
    logger.info(f"Deduplication plan generated:")
    logger.info(f"  Total duplicates: {dedup_plan['total_duplicates']}")
    logger.info(f"  Entries to keep: {dedup_plan['total_to_keep']}")
    logger.info(f"  Entries to remove: {dedup_plan['total_to_remove']}")
    
    # Print some sample deduplication rules
    logger.info("Sample deduplication rules:")
    for i, rule in enumerate(dedup_plan['deduplication_rules'][:5]):
        logger.info(f"  {i+1}. {rule['filename']}: {rule['action']} (kept: {rule['kept']}, removed: {rule['removed']})")

if __name__ == "__main__":
    test_dedup_plan()

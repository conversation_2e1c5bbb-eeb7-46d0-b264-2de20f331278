#!/usr/bin/env python3
"""
ResNet-like Neural Network for Goodness Prediction

This module implements a ResNet-like neural network architecture for predicting
prompt goodness scores from structured prompt data.

Features:
- Residual blocks for deep learning
- Tag embedding layers
- Weight-aware processing
- Variable-length sequence handling
- Comprehensive training with early stopping

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from typing import List, Tuple, Dict, Any, Optional, Union
from collections import defaultdict
import pickle

# Import base class
from goodness_predictor_models import BaseGoodnessPredictorModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PromptDataset(Dataset):
    """
    PyTorch Dataset for structured prompt data.
    """
    
    def __init__(self, prompt_data: List[Tuple[str, List[Tuple[str, float]], float]], 
                 tag_vocab: Dict[str, int], max_length: int = 100):
        """
        Initialize dataset.
        
        Args:
            prompt_data: List of (filename, [(tag, weight)], goodness) tuples
            tag_vocab: Mapping from tag strings to indices
            max_length: Maximum sequence length for padding/truncation
        """
        self.prompt_data = prompt_data
        self.tag_vocab = tag_vocab
        self.max_length = max_length
        
    def __len__(self):
        return len(self.prompt_data)
    
    def __getitem__(self, idx):
        filename, tags, goodness = self.prompt_data[idx]
        
        # Convert tags to indices and weights
        tag_indices = []
        weights = []
        
        for tag, weight in tags[:self.max_length]:  # Truncate if too long
            if tag in self.tag_vocab:
                tag_indices.append(self.tag_vocab[tag])
                weights.append(weight)
        
        # Pad sequences
        while len(tag_indices) < self.max_length:
            tag_indices.append(0)  # Padding token
            weights.append(0.0)
        
        return {
            'tag_indices': torch.tensor(tag_indices, dtype=torch.long),
            'weights': torch.tensor(weights, dtype=torch.float32),
            'length': torch.tensor(min(len(tags), self.max_length), dtype=torch.long),
            'goodness': torch.tensor(goodness, dtype=torch.float32)
        }


class ResidualBlock(nn.Module):
    """
    Residual block for the neural network.
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, dropout: float = 0.1):
        super(ResidualBlock, self).__init__()
        
        self.linear1 = nn.Linear(input_dim, hidden_dim)
        self.linear2 = nn.Linear(hidden_dim, input_dim)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(input_dim)
        
    def forward(self, x):
        residual = x
        
        x = F.relu(self.linear1(x))
        x = self.dropout(x)
        x = self.linear2(x)
        x = self.dropout(x)
        
        # Residual connection
        x = x + residual
        x = self.layer_norm(x)
        
        return x


class ResNetGoodnessPredictorModel(BaseGoodnessPredictorModel, nn.Module):
    """
    ResNet-like neural network for goodness prediction.
    
    Processes tag embeddings through residual blocks and handles
    variable-length sequences with attention mechanisms.
    """
    
    def __init__(self, vocab_size: int = 10000, embedding_dim: int = 128, 
                 hidden_dim: int = 256, num_blocks: int = 3, max_length: int = 100,
                 dropout: float = 0.1, device: str = None):
        """
        Initialize ResNet model.
        
        Args:
            vocab_size: Size of tag vocabulary
            embedding_dim: Dimension of tag embeddings
            hidden_dim: Hidden dimension for residual blocks
            num_blocks: Number of residual blocks
            max_length: Maximum sequence length
            dropout: Dropout rate
            device: Device to run on ('cuda' or 'cpu')
        """
        BaseGoodnessPredictorModel.__init__(self, "ResNet_Goodness_Predictor")
        nn.Module.__init__(self)
        
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        self.num_blocks = num_blocks
        self.max_length = max_length
        self.dropout = dropout
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # Initialize vocabulary
        self.tag_vocab = {}
        self.reverse_vocab = {}
        
        # Build model
        self._build_model()
        
    def _build_model(self):
        """Build the neural network architecture."""
        
        # Tag embedding layer
        self.tag_embedding = nn.Embedding(self.vocab_size, self.embedding_dim, padding_idx=0)
        
        # Weight processing layer
        self.weight_projection = nn.Linear(1, self.embedding_dim)
        
        # Combined embedding processing
        self.input_projection = nn.Linear(self.embedding_dim * 2, self.hidden_dim)
        
        # Residual blocks
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(self.hidden_dim, self.hidden_dim * 2, self.dropout)
            for _ in range(self.num_blocks)
        ])
        
        # Attention mechanism for sequence aggregation
        self.attention = nn.MultiheadAttention(
            embed_dim=self.hidden_dim,
            num_heads=8,
            dropout=self.dropout,
            batch_first=True
        )
        
        # Final prediction layers
        self.classifier = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim // 2, self.hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim // 4, 1),
            nn.Sigmoid()  # Output between 0 and 1
        )
        
        # Initialize weights
        self._initialize_weights()

        # Move to device
        self.to(self.device)

    def _initialize_weights(self):
        """Initialize model weights properly."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0, 0.1)
            elif isinstance(module, nn.LayerNorm):
                nn.init.constant_(module.bias, 0)
                nn.init.constant_(module.weight, 1.0)

    def _build_vocabulary(self, prompt_data: List[Tuple[str, List[Tuple[str, float]], float]]):
        """Build tag vocabulary from training data."""
        tag_counts = defaultdict(int)
        
        for filename, tags, goodness in prompt_data:
            for tag, weight in tags:
                tag_counts[tag] += 1
        
        # Sort by frequency and take top vocab_size - 1 (reserve 0 for padding)
        sorted_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)
        
        self.tag_vocab = {'<PAD>': 0}  # Padding token
        self.reverse_vocab = {0: '<PAD>'}
        
        for i, (tag, count) in enumerate(sorted_tags[:self.vocab_size - 1], 1):
            self.tag_vocab[tag] = i
            self.reverse_vocab[i] = tag
        
        logger.info(f"Built vocabulary with {len(self.tag_vocab)} tags")
    
    def forward(self, tag_indices, weights, lengths):
        """
        Forward pass through the network.
        
        Args:
            tag_indices: Tensor of tag indices [batch_size, max_length]
            weights: Tensor of tag weights [batch_size, max_length]
            lengths: Tensor of sequence lengths [batch_size]
            
        Returns:
            Predicted goodness scores [batch_size, 1]
        """
        batch_size, seq_len = tag_indices.shape
        
        # Tag embeddings
        tag_embeds = self.tag_embedding(tag_indices)  # [batch_size, seq_len, embedding_dim]
        
        # Weight embeddings
        weight_embeds = self.weight_projection(weights.unsqueeze(-1))  # [batch_size, seq_len, embedding_dim]
        
        # Combine tag and weight embeddings
        combined_embeds = torch.cat([tag_embeds, weight_embeds], dim=-1)  # [batch_size, seq_len, 2*embedding_dim]
        
        # Project to hidden dimension
        x = self.input_projection(combined_embeds)  # [batch_size, seq_len, hidden_dim]
        
        # Apply residual blocks
        for block in self.residual_blocks:
            x = block(x)
        
        # Create attention mask for padding
        attention_mask = torch.arange(seq_len, device=self.device).expand(batch_size, seq_len) < lengths.unsqueeze(1)
        
        # Apply attention mechanism
        attended_x, _ = self.attention(x, x, x, key_padding_mask=~attention_mask)
        
        # Global average pooling (considering sequence lengths)
        mask = attention_mask.unsqueeze(-1).float()
        pooled = (attended_x * mask).sum(dim=1) / lengths.unsqueeze(-1).float()
        
        # Final prediction
        output = self.classifier(pooled)
        
        return output.squeeze(-1)
    
    def train_model(self, X_train: List[Tuple[str, List[Tuple[str, float]], float]],
                    y_train: List[float],
                    X_val: Optional[List[Tuple[str, List[Tuple[str, float]], float]]] = None,
                    y_val: Optional[List[float]] = None,
                    batch_size: int = 32,
                    epochs: int = 50,
                    learning_rate: float = 0.001,
                    patience: int = 10,
                    **kwargs) -> Dict[str, Any]:
        """
        Train the ResNet model.
        
        Args:
            X_train: Training data
            y_train: Training labels
            X_val: Validation data
            y_val: Validation labels
            batch_size: Batch size for training
            epochs: Maximum number of epochs
            learning_rate: Learning rate
            patience: Early stopping patience
            **kwargs: Additional parameters
            
        Returns:
            Training history
        """
        logger.info(f"Training {self.model_name} on {len(X_train)} samples...")
        
        # Build vocabulary
        self._build_vocabulary(X_train)
        
        # Create datasets
        train_dataset = PromptDataset(X_train, self.tag_vocab, self.max_length)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        val_loader = None
        if X_val is not None and y_val is not None:
            val_dataset = PromptDataset(X_val, self.tag_vocab, self.max_length)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # Setup training
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
        
        # Training history
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': [],
            'val_metrics': []
        }
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # Training phase
            self.train()
            train_loss = 0.0
            train_predictions = []
            train_targets = []
            
            for batch in train_loader:
                tag_indices = batch['tag_indices'].to(self.device)
                weights = batch['weights'].to(self.device)
                lengths = batch['length'].to(self.device)
                targets = batch['goodness'].to(self.device)
                
                optimizer.zero_grad()
                
                outputs = self.forward(tag_indices, weights, lengths)

                # Check for NaN in outputs
                if torch.isnan(outputs).any():
                    logger.warning("NaN detected in outputs, skipping batch")
                    continue

                loss = criterion(outputs, targets)

                # Check for NaN in loss
                if torch.isnan(loss):
                    logger.warning("NaN detected in loss, skipping batch")
                    continue

                loss.backward()

                # Gradient clipping to prevent NaN
                torch.nn.utils.clip_grad_norm_(self.parameters(), max_norm=1.0)

                optimizer.step()
                
                train_loss += loss.item()
                train_predictions.extend(outputs.detach().cpu().numpy())
                train_targets.extend(targets.detach().cpu().numpy())
            
            train_loss /= len(train_loader)
            history['train_loss'].append(train_loss)
            
            # Validation phase
            val_loss = 0.0
            val_predictions = []
            val_targets = []
            
            if val_loader is not None:
                self.eval()
                with torch.no_grad():
                    for batch in val_loader:
                        tag_indices = batch['tag_indices'].to(self.device)
                        weights = batch['weights'].to(self.device)
                        lengths = batch['length'].to(self.device)
                        targets = batch['goodness'].to(self.device)
                        
                        outputs = self.forward(tag_indices, weights, lengths)
                        loss = criterion(outputs, targets)
                        
                        val_loss += loss.item()
                        val_predictions.extend(outputs.detach().cpu().numpy())
                        val_targets.extend(targets.detach().cpu().numpy())
                
                val_loss /= len(val_loader)
                history['val_loss'].append(val_loss)
                
                # Learning rate scheduling
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # Save best model state
                    self.best_state_dict = self.state_dict().copy()
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    logger.info(f"Early stopping at epoch {epoch + 1}")
                    break
            
            # Log progress
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch + 1}/{epochs}, Train Loss: {train_loss:.4f}, "
                           f"Val Loss: {val_loss:.4f}")
        
        # Load best model state
        if hasattr(self, 'best_state_dict'):
            self.load_state_dict(self.best_state_dict)
        
        self.is_trained = True
        self.training_history = history
        
        logger.info("Training complete!")
        return history

    def predict(self, prompt_data: Union[List[Tuple[str, List[Tuple[str, float]], float]],
                                       Tuple[str, List[Tuple[str, float]], float]]) -> Union[List[float], float]:
        """
        Predict goodness scores for prompt data.

        Args:
            prompt_data: Single prompt or list of prompts

        Returns:
            Predicted goodness score(s)
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")

        # Handle single prompt
        if isinstance(prompt_data, tuple):
            prompt_data = [prompt_data]
            single_prediction = True
        else:
            single_prediction = False

        # Create dataset and dataloader
        dataset = PromptDataset(prompt_data, self.tag_vocab, self.max_length)
        dataloader = DataLoader(dataset, batch_size=32, shuffle=False)

        # Predict
        self.eval()
        predictions = []

        with torch.no_grad():
            for batch in dataloader:
                tag_indices = batch['tag_indices'].to(self.device)
                weights = batch['weights'].to(self.device)
                lengths = batch['length'].to(self.device)

                outputs = self.forward(tag_indices, weights, lengths)
                predictions.extend(outputs.detach().cpu().numpy())

        if single_prediction:
            return float(predictions[0])
        else:
            return predictions

    def evaluate(self, X_test: List[Tuple[str, List[Tuple[str, float]], float]],
                 y_test: List[float]) -> Dict[str, float]:
        """
        Evaluate model using comprehensive ranking metrics.

        Args:
            X_test: Test data
            y_test: Test labels

        Returns:
            Dictionary of evaluation metrics
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")

        logger.info(f"Evaluating {self.model_name} on {len(X_test)} test samples...")

        # Get predictions
        y_pred = self.predict(X_test)

        # Calculate metrics
        metrics = self._calculate_metrics(y_test, y_pred)

        logger.info(f"Evaluation complete. RMSE: {metrics.get('rmse', 0):.4f}, "
                   f"NDCG: {metrics.get('ndcg', 0):.4f}")

        return metrics

    def _calculate_metrics(self, y_true: List[float], y_pred: List[float]) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics."""
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)

        metrics = {
            'rmse': np.sqrt(np.mean((y_true - y_pred) ** 2)),
            'mae': np.mean(np.abs(y_true - y_pred)),
            'r2': 1 - np.sum((y_true - y_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2),
            'mean_pred': np.mean(y_pred),
            'std_pred': np.std(y_pred)
        }

        # Add ranking metrics
        try:
            from sklearn.metrics import ndcg_score, roc_auc_score

            # NDCG calculation
            y_true_2d = y_true.reshape(1, -1)
            y_pred_2d = y_pred.reshape(1, -1)
            metrics['ndcg'] = ndcg_score(y_true_2d, y_pred_2d)

            # Precision@K calculations
            for k in [5, 10, 20]:
                if len(y_true) >= k:
                    top_k_indices = np.argsort(y_pred)[-k:]
                    top_k_true = y_true[top_k_indices]
                    metrics[f'precision_at_{k}'] = np.mean(top_k_true)

            # AUC if binary classification
            unique_labels = np.unique(y_true)
            if len(unique_labels) == 2 and set(unique_labels) == {0.0, 1.0}:
                metrics['auc'] = roc_auc_score(y_true, y_pred)

            # Score difference between good and normal prompts
            good_mask = y_true == 1.0
            normal_mask = y_true == 0.0

            if np.any(good_mask) and np.any(normal_mask):
                good_scores = y_pred[good_mask]
                normal_scores = y_pred[normal_mask]
                metrics['score_difference'] = np.mean(good_scores) - np.mean(normal_scores)

        except Exception as e:
            logger.warning(f"Error calculating ranking metrics: {e}")

        return metrics

    def to(self, device):
        """Move model to device."""
        nn.Module.to(self, device)
        self.device = device
        return self

#!/usr/bin/env python3
"""
Master Machine Learning Pipeline

This script orchestrates the complete machine learning pipeline from data validation
through model training and evaluation.

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime
import subprocess
import json

from data_validation import DataValidator
from lightgbm_trainer import LightGBMTrainer
from model_evaluator import ModelEvaluator


class MLPipelineOrchestrator:
    """Master orchestrator for the complete ML pipeline."""
    
    def __init__(self, data_file: str = "promptlabels.pkl", results_dir: str = "results"):
        self.data_file = data_file
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(exist_ok=True)
        
        # Create master run directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.master_run_dir = self.results_dir / f"ml_pipeline_{timestamp}"
        self.master_run_dir.mkdir(exist_ok=True)
        
        # Pipeline state
        self.validation_dir = None
        self.training_dir = None
        self.evaluation_dir = None
        self.pipeline_results = {}
        
        print(f"Master pipeline results will be saved to: {self.master_run_dir}")
    
    def run_data_validation(self, test_size: float = 0.2, random_seed: int = 42):
        """Run Phase 1: Data validation and preparation."""
        print("\n" + "="*80)
        print("PHASE 1: DATA VALIDATION AND PREPARATION")
        print("="*80)
        
        validator = DataValidator(self.data_file, str(self.results_dir))
        success = validator.run_full_validation()
        
        if not success:
            raise RuntimeError("Data validation failed!")
        
        self.validation_dir = validator.run_dir
        
        # Find the train/test files
        train_file = self.validation_dir / "train_data.pkl"
        test_file = self.validation_dir / "test_data.pkl"
        
        if not train_file.exists() or not test_file.exists():
            raise RuntimeError("Train/test split files not found!")
        
        self.pipeline_results['validation'] = {
            'directory': str(self.validation_dir),
            'train_file': str(train_file),
            'test_file': str(test_file),
            'statistics': validator.stats
        }
        
        print(f"✓ Phase 1 completed. Results in: {self.validation_dir}")
        return train_file, test_file
    
    def run_model_training(self, train_file: str, test_file: str, 
                          optimize_hyperparams: bool = True, n_trials: int = 50):
        """Run Phase 2: Model training."""
        print("\n" + "="*80)
        print("PHASE 2: LIGHTGBM MODEL TRAINING")
        print("="*80)
        
        trainer = LightGBMTrainer(str(self.results_dir))
        model, feature_extractor = trainer.run_training_pipeline(
            str(train_file), str(test_file),
            optimize_hyperparams=optimize_hyperparams,
            n_trials=n_trials
        )
        
        self.training_dir = trainer.run_dir
        
        self.pipeline_results['training'] = {
            'directory': str(self.training_dir),
            'model_file': str(self.training_dir / "lightgbm_model.pkl"),
            'feature_extractor_file': str(self.training_dir / "feature_extractor.pkl"),
            'parameters_file': str(self.training_dir / "model_parameters.json"),
            'best_params': trainer.best_params
        }
        
        print(f"✓ Phase 2 completed. Results in: {self.training_dir}")
        return model, feature_extractor
    
    def run_model_evaluation(self, test_file: str):
        """Run Phase 3: Model evaluation."""
        print("\n" + "="*80)
        print("PHASE 3: COMPREHENSIVE MODEL EVALUATION")
        print("="*80)
        
        if self.training_dir is None:
            raise RuntimeError("Model training must be completed first!")
        
        evaluator = ModelEvaluator(str(self.results_dir))
        metrics = evaluator.run_evaluation_pipeline(str(self.training_dir), str(test_file))
        
        self.evaluation_dir = evaluator.run_dir
        
        self.pipeline_results['evaluation'] = {
            'directory': str(self.evaluation_dir),
            'metrics': metrics
        }
        
        print(f"✓ Phase 3 completed. Results in: {self.evaluation_dir}")
        return metrics
    
    def generate_pipeline_summary(self):
        """Generate comprehensive pipeline summary."""
        print("\n" + "="*80)
        print("GENERATING PIPELINE SUMMARY")
        print("="*80)
        
        summary = {
            'pipeline_timestamp': datetime.now().isoformat(),
            'master_directory': str(self.master_run_dir),
            'data_file': self.data_file,
            'phases': self.pipeline_results
        }
        
        # Save summary
        summary_file = self.master_run_dir / "pipeline_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        # Create readable summary
        readable_summary = self.master_run_dir / "PIPELINE_SUMMARY.md"
        with open(readable_summary, 'w') as f:
            f.write("# Machine Learning Pipeline Summary\n\n")
            f.write(f"**Pipeline Run:** {summary['pipeline_timestamp']}\n")
            f.write(f"**Data Source:** {self.data_file}\n")
            f.write(f"**Master Directory:** {self.master_run_dir}\n\n")
            
            # Phase 1 Summary
            if 'validation' in self.pipeline_results:
                val_stats = self.pipeline_results['validation']['statistics']
                f.write("## Phase 1: Data Validation\n")
                f.write(f"- **Directory:** {self.pipeline_results['validation']['directory']}\n")
                f.write(f"- **Total Samples:** {val_stats.get('total_entries', 'N/A'):,}\n")
                f.write(f"- **Good Quality:** {val_stats.get('good_count', 'N/A'):,}\n")
                f.write(f"- **Normal Quality:** {val_stats.get('normal_count', 'N/A'):,}\n")
                f.write(f"- **Valid Prompts:** {val_stats.get('non_empty_prompts', 'N/A'):,}\n\n")
            
            # Phase 2 Summary
            if 'training' in self.pipeline_results:
                f.write("## Phase 2: Model Training\n")
                f.write(f"- **Directory:** {self.pipeline_results['training']['directory']}\n")
                f.write(f"- **Model Type:** LightGBM\n")
                f.write(f"- **Hyperparameter Optimization:** {'Yes' if self.pipeline_results['training'].get('best_params') else 'No'}\n")
                if self.pipeline_results['training'].get('best_params'):
                    f.write("- **Best Parameters:**\n")
                    for param, value in self.pipeline_results['training']['best_params'].items():
                        f.write(f"  - {param}: {value}\n")
                f.write("\n")
            
            # Phase 3 Summary
            if 'evaluation' in self.pipeline_results:
                metrics = self.pipeline_results['evaluation']['metrics']
                f.write("## Phase 3: Model Evaluation\n")
                f.write(f"- **Directory:** {self.pipeline_results['evaluation']['directory']}\n")
                f.write("- **Key Metrics:**\n")
                f.write(f"  - ROC-AUC: {metrics.get('roc_auc', 'N/A'):.4f}\n")
                f.write(f"  - Average Precision: {metrics.get('avg_precision', 'N/A'):.4f}\n")
                f.write(f"  - F1-Score: {metrics.get('f1_score', 'N/A'):.4f}\n")
                f.write(f"  - NDCG: {metrics.get('ndcg', 'N/A'):.4f}\n")
                if 'mean_score_diff' in metrics:
                    f.write(f"  - Mean Score Difference: {metrics['mean_score_diff']:.4f}\n")
                f.write("\n")
            
            f.write("## Files and Directories\n")
            f.write("### Key Output Files:\n")
            if 'validation' in self.pipeline_results:
                f.write(f"- Train Data: `{self.pipeline_results['validation']['train_file']}`\n")
                f.write(f"- Test Data: `{self.pipeline_results['validation']['test_file']}`\n")
            if 'training' in self.pipeline_results:
                f.write(f"- Trained Model: `{self.pipeline_results['training']['model_file']}`\n")
                f.write(f"- Feature Extractor: `{self.pipeline_results['training']['feature_extractor_file']}`\n")
            if 'evaluation' in self.pipeline_results:
                f.write(f"- Evaluation Metrics: `{self.pipeline_results['evaluation']['directory']}/evaluation_metrics.json`\n")
                f.write(f"- Detailed Predictions: `{self.pipeline_results['evaluation']['directory']}/detailed_predictions.csv`\n")
        
        print(f"✓ Pipeline summary saved to: {summary_file}")
        print(f"✓ Readable summary saved to: {readable_summary}")
        
        return summary
    
    def run_complete_pipeline(self, test_size: float = 0.2, random_seed: int = 42,
                            optimize_hyperparams: bool = True, n_trials: int = 50):
        """Run the complete ML pipeline."""
        print("="*80)
        print("STARTING COMPLETE MACHINE LEARNING PIPELINE")
        print("="*80)
        
        start_time = datetime.now()
        
        try:
            # Phase 1: Data Validation
            train_file, test_file = self.run_data_validation(test_size, random_seed)
            
            # Phase 2: Model Training
            model, feature_extractor = self.run_model_training(
                train_file, test_file, optimize_hyperparams, n_trials
            )
            
            # Phase 3: Model Evaluation
            metrics = self.run_model_evaluation(test_file)
            
            # Generate Summary
            summary = self.generate_pipeline_summary()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            print("\n" + "="*80)
            print("PIPELINE COMPLETED SUCCESSFULLY! 🎉")
            print("="*80)
            print(f"Total Duration: {duration}")
            print(f"Master Results Directory: {self.master_run_dir}")
            print("\nKey Performance Metrics:")
            print(f"  ROC-AUC: {metrics.get('roc_auc', 'N/A'):.4f}")
            print(f"  Average Precision: {metrics.get('avg_precision', 'N/A'):.4f}")
            print(f"  F1-Score: {metrics.get('f1_score', 'N/A'):.4f}")
            print(f"  NDCG: {metrics.get('ndcg', 'N/A'):.4f}")
            if 'mean_score_diff' in metrics:
                print(f"  Mean Score Difference: {metrics['mean_score_diff']:.4f}")
            
            return True, summary
            
        except Exception as e:
            print(f"\n❌ Pipeline failed: {e}")
            return False, None


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Complete Machine Learning Pipeline for Prompt Quality Prediction",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --data promptlabels.pkl                    # Run complete pipeline
  %(prog)s --data promptlabels.pkl --no-optimization  # Skip hyperparameter tuning
  %(prog)s --data promptlabels.pkl --n-trials 100     # More optimization trials
  %(prog)s --data promptlabels.pkl --test-size 0.3    # Larger test set
        """
    )
    
    parser.add_argument('--data', '-d', default='promptlabels.pkl',
                       help='Path to the promptlabels.pkl file')
    parser.add_argument('--results-dir', '-r', default='results',
                       help='Results directory')
    parser.add_argument('--test-size', '-t', type=float, default=0.2,
                       help='Test set size (default: 0.2)')
    parser.add_argument('--random-seed', '-s', type=int, default=42,
                       help='Random seed for reproducibility')
    parser.add_argument('--no-optimization', action='store_true',
                       help='Skip hyperparameter optimization')
    parser.add_argument('--n-trials', type=int, default=50,
                       help='Number of hyperparameter optimization trials')
    
    args = parser.parse_args()
    
    # Validate input file
    if not Path(args.data).exists():
        print(f"❌ Data file not found: {args.data}")
        print("Please run the data cleaning script first:")
        print("  python run_data_cleaning.py --run")
        sys.exit(1)
    
    # Create orchestrator and run pipeline
    orchestrator = MLPipelineOrchestrator(args.data, args.results_dir)
    
    success, summary = orchestrator.run_complete_pipeline(
        test_size=args.test_size,
        random_seed=args.random_seed,
        optimize_hyperparams=not args.no_optimization,
        n_trials=args.n_trials
    )
    
    if success:
        print("\n🚀 Ready for MCTS integration!")
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()

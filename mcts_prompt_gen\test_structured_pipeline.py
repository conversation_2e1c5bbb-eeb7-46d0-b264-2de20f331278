#!/usr/bin/env python3
"""
Test script for the structured prompt parsing pipeline.

This script tests the complete pipeline with structured prompt extraction,
including tag parsing, weight extraction, and ML feature engineering.
"""

import os
import sys
import dill
import tempfile
from pathlib import Path

# Add src directory to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

from prompt_parser import PromptParser, parse_enhanced_prompt
from lightgbm_trainer import PromptFeatureExtractor


def test_structured_prompt_parsing():
    """Test structured prompt parsing on sample enhanced prompts."""
    print("Testing Structured Prompt Parsing")
    print("=" * 50)
    
    # Sample enhanced prompts with various complexity levels
    test_prompts = [
        '1girl, {masterpiece}, (detailed eyes:1.2) --cfg 7.5 --steps 35 --size 832x1216',
        'beautiful landscape, [low quality] --cfg 5.0 --lora style.safetensors:0.8 --lora character.safetensors:1.2',
        'simple prompt without any special syntax',
        '{{{ultra detailed}}}, ((best quality:1.3)), normal tag --sampler euler_ancestral --model animagine.safetensors',
        '1girl, masterpiece, best quality, detailed face, beautiful eyes, long hair',
        'portrait, high resolution, {detailed}, [blurry:0.8] --cfg 6.0 --steps 40'
    ]
    
    parser = PromptParser()
    
    print(f"Parsing {len(test_prompts)} test prompts...")
    
    structured_prompts = []
    for i, prompt in enumerate(test_prompts):
        print(f"\n--- Prompt {i+1} ---")
        print(f"Original: {prompt}")
        
        structured = parser.parse_enhanced_prompt(prompt)
        structured_prompts.append(structured)
        
        print(f"Tags found: {structured['tag_count']}")
        print(f"Average weight: {structured['avg_weight']:.2f}")
        print(f"Weight variance: {structured['weight_variance']:.3f}")
        print(f"Technical params: {len(structured['technical_params'])}")
        
        # Show sample tags
        print("Sample tags:")
        for tag, weight in structured['tags'][:5]:
            print(f"  {tag}: {weight}")
        
        if structured['technical_params']:
            print("Technical parameters:")
            for param, value in structured['technical_params'].items():
                print(f"  {param}: {value}")
    
    # Test reconstruction
    print(f"\n--- Testing Reconstruction ---")
    for i, (original, structured) in enumerate(zip(test_prompts, structured_prompts)):
        reconstructed = parser.reconstruct_prompt(structured)
        print(f"\nPrompt {i+1}:")
        print(f"Original:     {original}")
        print(f"Reconstructed: {reconstructed}")
    
    # Get parsing statistics
    stats = parser.get_parsing_statistics()
    print(f"\n--- Parsing Statistics ---")
    print(f"Total tags parsed: {stats['total_tags_parsed']}")
    print(f"Unique tags: {stats['unique_tags']}")
    print(f"Parsing errors: {stats['parsing_errors']}")
    print(f"Error rate: {stats['error_rate']:.3f}")
    
    print(f"Most common tags:")
    for tag, count in stats['most_common_tags'][:10]:
        print(f"  {tag}: {count}")
    
    return len(structured_prompts) == len(test_prompts)


def test_structured_feature_extraction():
    """Test feature extraction with structured prompts."""
    print("\n" + "=" * 50)
    print("Testing Structured Feature Extraction")
    print("=" * 50)
    
    # Create sample structured prompts
    sample_prompts = [
        '1girl, {masterpiece}, (detailed:1.2) --cfg 7.5 --steps 35',
        'landscape, [low quality] --cfg 5.0 --lora style.safetensors:0.8',
        'simple prompt',
        '{{{ultra detailed}}}, best quality --sampler euler'
    ]
    
    labels = ['good', 'normal', 'normal', 'good']
    
    # Parse prompts into structured format
    parser = PromptParser()
    structured_prompts = []
    
    for prompt in sample_prompts:
        structured = parser.parse_enhanced_prompt(prompt)
        structured_prompts.append(structured)
    
    print(f"Created {len(structured_prompts)} structured prompts")
    
    # Test feature extraction in structured mode
    print("\n--- Testing Structured Feature Extractor ---")
    
    extractor_structured = PromptFeatureExtractor(
        max_features=1000, 
        ngram_range=(1, 2), 
        structured_mode=True
    )
    
    # Fit and transform with structured prompts
    extractor_structured.fit(structured_prompts, labels)
    features_structured, encoded_labels = extractor_structured.transform(structured_prompts, labels)
    
    print(f"Structured features shape: {features_structured.shape}")
    
    # Compare with regular feature extraction
    print("\n--- Comparing with Regular Feature Extractor ---")
    
    extractor_regular = PromptFeatureExtractor(
        max_features=1000, 
        ngram_range=(1, 2), 
        structured_mode=False
    )
    
    # Extract text prompts for comparison
    text_prompts = [structured['raw_text'] for structured in structured_prompts]
    
    extractor_regular.fit(text_prompts, labels)
    features_regular, _ = extractor_regular.transform(text_prompts, labels)
    
    print(f"Regular features shape: {features_regular.shape}")
    
    # Show feature names
    structured_feature_names = extractor_structured.get_feature_names()
    regular_feature_names = extractor_regular.get_feature_names()
    
    print(f"\nStructured feature count: {len(structured_feature_names)}")
    print(f"Regular feature count: {len(regular_feature_names)}")
    
    print(f"\nLast 10 structured features:")
    for name in structured_feature_names[-10:]:
        print(f"  {name}")
    
    print(f"\nLast 10 regular features:")
    for name in regular_feature_names[-10:]:
        print(f"  {name}")
    
    return features_structured.shape[0] == len(structured_prompts)


def test_iterative_cleaning_process():
    """Test the iterative cleaning process on problematic prompts."""
    print("\n" + "=" * 50)
    print("Testing Iterative Cleaning Process")
    print("=" * 50)
    
    # Problematic prompts that need cleaning
    problematic_prompts = [
        '1girl,,,{masterpiece}}}detailed eyes --cfg 7.5',  # Extra commas and brackets
        '((artist: john doe)), beautiful face:1.2}',        # Malformed syntax
        'tag1   tag2\n\ntag3，tag4；tag5 --steps 30',       # Mixed separators
        '{{{ultra detailed, best quality, normal tag --sampler euler',  # Unbalanced brackets
        'very long tag that should probably be split into multiple tags because it contains many concepts',
        'normal, {weighted}, [reduced] --cfg 5.0 --lora test.safetensors:1.0'
    ]
    
    print(f"Testing iterative cleaning on {len(problematic_prompts)} problematic prompts...")
    
    parser = PromptParser()
    
    # Run iterative cleaning
    cleaned_prompts = parser.iterative_cleaning_process(problematic_prompts, max_iterations=3)
    
    print(f"\n--- Cleaning Results ---")
    for i, (original, cleaned) in enumerate(zip(problematic_prompts, cleaned_prompts)):
        print(f"\nPrompt {i+1}:")
        print(f"Original: {original}")
        print(f"Tags: {cleaned['tag_count']}")
        print(f"Tech params: {len(cleaned['technical_params'])}")
        
        # Show cleaned tags
        if cleaned['tags']:
            print(f"Sample tags: {cleaned['tags'][:3]}")
    
    # Get final statistics
    stats = parser.get_parsing_statistics()
    print(f"\n--- Final Statistics ---")
    print(f"Total tags parsed: {stats['total_tags_parsed']}")
    print(f"Parsing errors: {stats['parsing_errors']}")
    print(f"Error rate: {stats['error_rate']:.3f}")
    print(f"Cleaning iterations: {stats['cleaning_iterations']}")
    
    # Success if error rate is reasonable
    success = stats['error_rate'] < 0.15  # Allow up to 15% error rate for problematic prompts
    print(f"\n{'✓' if success else '❌'} Cleaning process {'passed' if success else 'failed'}")
    
    return success


def test_backward_compatibility():
    """Test that structured parsing maintains backward compatibility."""
    print("\n" + "=" * 50)
    print("Testing Backward Compatibility")
    print("=" * 50)
    
    # Test prompts without enhanced syntax
    simple_prompts = [
        '1girl, masterpiece, best quality',
        'beautiful landscape, high resolution',
        'portrait, detailed face, blue eyes'
    ]
    
    labels = ['good', 'normal', 'good']
    
    print("Testing simple prompts without enhanced syntax...")
    
    # Parse with structured parser
    parser = PromptParser()
    structured_results = []
    
    for prompt in simple_prompts:
        structured = parser.parse_enhanced_prompt(prompt)
        structured_results.append(structured)
        
        print(f"\nPrompt: {prompt}")
        print(f"Tags: {structured['tag_count']}")
        print(f"Tech params: {len(structured['technical_params'])}")
        
        # Verify that basic tags are preserved
        tag_names = [tag for tag, _ in structured['tags']]
        expected_tags = ['1girl', 'masterpiece', 'best_quality', 'beautiful', 'landscape', 'high_resolution', 'portrait', 'detailed_face', 'blue_eyes']
        
        found_expected = any(expected in tag_names for expected in expected_tags if expected in prompt.lower().replace(' ', '_'))
        if found_expected:
            print("✓ Basic tags preserved")
        else:
            print("⚠️  Some basic tags might be missing")
    
    # Test feature extraction compatibility
    print(f"\n--- Testing Feature Extraction Compatibility ---")
    
    # Structured mode
    extractor_structured = PromptFeatureExtractor(max_features=500, structured_mode=True)
    extractor_structured.fit(structured_results, labels)
    features_structured, _ = extractor_structured.transform(structured_results, labels)
    
    # Regular mode
    extractor_regular = PromptFeatureExtractor(max_features=500, structured_mode=False)
    extractor_regular.fit(simple_prompts, labels)
    features_regular, _ = extractor_regular.transform(simple_prompts, labels)
    
    print(f"Structured features shape: {features_structured.shape}")
    print(f"Regular features shape: {features_regular.shape}")
    
    # Both should work without errors
    success = (features_structured.shape[0] == len(simple_prompts) and 
              features_regular.shape[0] == len(simple_prompts))
    
    print(f"\n{'✓' if success else '❌'} Backward compatibility {'maintained' if success else 'broken'}")
    
    return success


def main():
    """Run all structured prompt parsing tests."""
    print("Structured Prompt Parsing Test Suite")
    print("=" * 60)
    
    tests = [
        ("Structured Prompt Parsing", test_structured_prompt_parsing),
        ("Structured Feature Extraction", test_structured_feature_extraction),
        ("Iterative Cleaning Process", test_iterative_cleaning_process),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✓ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Structured prompt parsing is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

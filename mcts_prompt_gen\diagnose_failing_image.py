#!/usr/bin/env python3
"""
Diagnostic script for the failing LoRA extraction case.

This script specifically tests the image F:\SD-webui\ComfyUI\output\2024-09-27\ComfyUI_00001_.png
to understand why LoRA extraction is failing.
"""

import json
import sys
import os
import traceback
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import image_info, extract_comfyui_workflow_params, traverse_workflow_graph, extract_connected_lora_nodes
from PIL import Image


def detailed_image_analysis(image_path):
    """
    Perform detailed analysis of the failing image to understand the LoRA extraction issue.
    
    Args:
        image_path: Path to the failing image
    """
    print("=" * 80)
    print(f"DETAILED ANALYSIS OF FAILING IMAGE")
    print("=" * 80)
    print(f"Image path: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ ERROR: Image file does not exist: {image_path}")
        return False
    
    try:
        with Image.open(image_path) as img:
            print(f"✅ Image loaded successfully")
            print(f"   Size: {img.size}")
            print(f"   Mode: {img.mode}")
            print(f"   Format: {img.format}")
            
            # Step 1: Extract raw metadata
            print("\n" + "-" * 60)
            print("STEP 1: RAW METADATA EXTRACTION")
            print("-" * 60)
            
            # Get all metadata
            strimg = []
            w, h = img.size
            info_dict = {
                "Filename": image_path,
                "Image Height": h,
                "Image Width": w,
                "Image is Animated": getattr(img, "is_animated", False),
                "Frames in Image": getattr(img, "n_frames", 1)
            }
            for label, value in info_dict.items():
                strimg.append(f"{label}: {value}")

            # EXIF data
            exifdata = img.getexif()
            print(f"EXIF tags found: {len(exifdata)}")
            for tag_id in exifdata:
                from PIL.ExifTags import TAGS
                tag = TAGS.get(tag_id, tag_id)
                data = exifdata.get(tag_id)
                if isinstance(data, bytes):
                    data = data.decode()
                strimg.append(f"{tag}: {data}")

            # PNG info
            print(f"PNG info keys: {list(img.info.keys()) if hasattr(img, 'info') else 'None'}")
            for x in img.info:
                if x == "exif": 
                    continue
                y = img.info[x]
                if type(y) is str:
                    y = ''.join(c for c in y if c.isprintable() or c.isspace())
                strimg.append(f"{x}: {y}")
                
                # Look for workflow data specifically
                if 'workflow' in x.lower():
                    print(f"Found workflow key: {x}")
                    print(f"Workflow data length: {len(str(y))}")
                    
            if "exif" in img.info:
                x = img.info["exif"]
                x = x.replace(b'\x00', b'')
                y = x.decode('utf-8', errors='ignore')
                y = ''.join(c for c in y if c.isprintable() or c.isspace())
                strimg.append(y)
            
            strimg_full = '\n'.join(strimg)
            
            # Look for LoRA mentions in raw metadata
            print(f"\nSearching for LoRA mentions in raw metadata...")
            lora_mentions = []
            for line in strimg_full.split('\n'):
                if 'lora' in line.lower() or '<lora:' in line:
                    lora_mentions.append(line.strip())
            
            print(f"LoRA mentions found: {len(lora_mentions)}")
            for mention in lora_mentions:
                print(f"  - {mention[:200]}{'...' if len(mention) > 200 else ''}")
            
            # Step 2: Test workflow JSON extraction
            print("\n" + "-" * 60)
            print("STEP 2: WORKFLOW JSON EXTRACTION")
            print("-" * 60)

            workflow_json = ""

            # Method 1: Check if workflow is directly available in image.info (most common for ComfyUI)
            if hasattr(img, 'info') and 'workflow' in img.info:
                print("✅ Found 'workflow' in image.info")
                workflow_data = img.info['workflow']
                if isinstance(workflow_data, str):
                    print("✅ Workflow is stored as JSON string")
                    workflow_json = workflow_data
                    print(f"✅ Extracted workflow JSON ({len(workflow_json)} chars)")
                elif isinstance(workflow_data, dict):
                    print("✅ Workflow is stored as dictionary")
                    workflow_json = json.dumps(workflow_data)
                    print(f"✅ Converted workflow to JSON ({len(workflow_json)} chars)")
                else:
                    print(f"❌ Workflow data has unexpected type: {type(workflow_data)}")
            else:
                print("❌ No 'workflow' found in image.info")

            # Method 2: Check for workflow in various metadata fields (legacy approach)
            if not workflow_json and 'workflow' in strimg_full:
                print("Trying legacy workflow extraction...")
                workflow_start = strimg_full.find('workflow:')
                if workflow_start >= 0:
                    print("✅ Found 'workflow:' pattern")
                    # Find the JSON object for workflow
                    brace_count = 0
                    start_pos = strimg_full.find('{', workflow_start)
                    if start_pos >= 0:
                        print(f"✅ Found opening brace at position {start_pos}")
                        for i, char in enumerate(strimg_full[start_pos:], start_pos):
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                if brace_count == 0:
                                    workflow_json = strimg_full[start_pos:i+1]
                                    print(f"✅ Extracted workflow JSON ({len(workflow_json)} chars)")
                                    break
                    else:
                        print("❌ No opening brace found after 'workflow:'")
                else:
                    print("❌ No 'workflow:' pattern found")

            # Method 3: Look for direct JSON workflow pattern in metadata string
            if not workflow_json:
                print("Trying alternative JSON pattern extraction...")
                import re
                json_pattern = re.search(r'\{"?\d+"?\s*:\s*\{[^}]*"class_type"[^}]*\}.*?\}', strimg_full, re.DOTALL)
                if json_pattern:
                    print("✅ Found ComfyUI workflow pattern")
                    # Try to extract the full JSON by finding balanced braces
                    start_pos = json_pattern.start()
                    brace_count = 0
                    for i, char in enumerate(strimg_full[start_pos:], start_pos):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                workflow_json = strimg_full[start_pos:i+1]
                                print(f"✅ Extracted workflow JSON via pattern ({len(workflow_json)} chars)")
                                break
                else:
                    print("❌ No ComfyUI workflow pattern found")

            if not workflow_json:
                print("❌ CRITICAL: No workflow JSON found in image metadata")
                return False
            
            # Step 3: Parse workflow JSON
            print("\n" + "-" * 60)
            print("STEP 3: WORKFLOW JSON PARSING")
            print("-" * 60)
            
            try:
                workflow = json.loads(workflow_json)
                print(f"✅ Workflow JSON parsed successfully")
                print(f"   Raw workflow keys: {list(workflow.keys())}")

                # Import the conversion function
                from utils import convert_api_workflow_to_standard

                # Convert API format to standard format for processing
                workflow = convert_api_workflow_to_standard(workflow)
                print(f"   Converted to standard format with {len(workflow)} nodes")

                for node_id, node in workflow.items():
                    if not isinstance(node, dict):
                        print(f"   Node {node_id}: Invalid node structure (type: {type(node)})")
                        continue

                    class_type = node.get('class_type', 'Unknown')
                    title = node.get('_meta', {}).get('title', 'No title')
                    print(f"   Node {node_id}: {class_type} ({title})")

                    # Check for LoRA nodes specifically
                    if class_type in ['LoraTagLoader', 'LoraLoader']:
                        inputs = node.get('inputs', {})
                        print(f"     *** LoRA NODE FOUND ***")
                        print(f"     Inputs type: {type(inputs)}")
                        print(f"     Inputs content: {inputs}")

                        if isinstance(inputs, dict):
                            lora_text = inputs.get('text', '')
                            print(f"     LoRA text: {lora_text}")

                            # Parse LoRA strings
                            import re
                            lora_matches = re.findall(r'<lora:([^>]+)>', lora_text)
                            print(f"     LoRA matches: {lora_matches}")
                        else:
                            print(f"     ERROR: Inputs is not a dictionary!")
                        
            except json.JSONDecodeError as e:
                print(f"❌ CRITICAL: Failed to parse workflow JSON: {e}")
                print(f"   JSON preview: {workflow_json[:500]}...")
                return False
            
            # Step 4: Test graph traversal
            print("\n" + "-" * 60)
            print("STEP 4: GRAPH TRAVERSAL TESTING")
            print("-" * 60)
            
            try:
                connected_nodes = traverse_workflow_graph(workflow)
                print(f"✅ Graph traversal completed")
                print(f"   Connected nodes: {sorted(connected_nodes)}")
                
                # Check if LoRA nodes are connected
                lora_nodes = []
                for node_id, node in workflow.items():
                    if node.get('class_type') in ['LoraTagLoader', 'LoraLoader']:
                        lora_nodes.append(node_id)
                        is_connected = node_id in connected_nodes
                        print(f"   LoRA node {node_id}: {'CONNECTED' if is_connected else 'DISCONNECTED'}")
                
                if not lora_nodes:
                    print("❌ CRITICAL: No LoRA nodes found in workflow")
                    return False
                    
            except Exception as e:
                print(f"❌ CRITICAL: Graph traversal failed: {e}")
                print(f"   Exception type: {type(e).__name__}")
                print(f"   Traceback:")
                traceback.print_exc()
                return False
            
            # Step 5: Test LoRA extraction
            print("\n" + "-" * 60)
            print("STEP 5: LORA EXTRACTION TESTING")
            print("-" * 60)
            
            try:
                loras = extract_connected_lora_nodes(workflow, connected_nodes)
                print(f"✅ LoRA extraction completed")
                print(f"   LoRAs extracted: {len(loras)}")
                for lora in loras:
                    print(f"   - {lora}")
                
                if not loras:
                    print("❌ CRITICAL: No LoRAs extracted despite LoRA nodes being present")
                    return False
                    
            except Exception as e:
                print(f"❌ CRITICAL: LoRA extraction failed: {e}")
                print(f"   Exception type: {type(e).__name__}")
                print(f"   Traceback:")
                traceback.print_exc()
                return False
            
            # Step 6: Test full workflow parameter extraction
            print("\n" + "-" * 60)
            print("STEP 6: FULL WORKFLOW PARAMETER EXTRACTION")
            print("-" * 60)
            
            try:
                params = extract_comfyui_workflow_params(workflow_json)
                print(f"✅ Full parameter extraction completed")
                print(f"   Parameters extracted: {len(params)}")
                for key, value in params.items():
                    print(f"   {key}: {value}")
                
                expected_loras = ['shuicai.safetensors:0.8', 'Yanami XL kohaku zeta.safetensors:0.7']
                actual_loras = params.get('loras', [])
                
                print(f"\n   Expected LoRAs: {expected_loras}")
                print(f"   Actual LoRAs: {actual_loras}")
                
                if set(expected_loras) == set(actual_loras):
                    print("✅ LoRA extraction matches expected results!")
                    return True
                else:
                    print("❌ LoRA extraction does not match expected results")
                    return False
                    
            except Exception as e:
                print(f"❌ CRITICAL: Full parameter extraction failed: {e}")
                print(f"   Exception type: {type(e).__name__}")
                print(f"   Traceback:")
                traceback.print_exc()
                return False
            
    except Exception as e:
        print(f"❌ CRITICAL: Failed to process image: {e}")
        print(f"   Exception type: {type(e).__name__}")
        print(f"   Traceback:")
        traceback.print_exc()
        return False


def main():
    """Main diagnostic function."""
    failing_image = r"F:\SD-webui\ComfyUI\output\2024-09-27\ComfyUI_00001_.png"
    
    print("DIAGNOSTIC SCRIPT FOR FAILING LORA EXTRACTION")
    print("=" * 80)
    print(f"Target image: {failing_image}")
    print(f"Expected LoRAs: shuicai.safetensors:0.8, Yanami XL kohaku zeta.safetensors:0.7")
    print()
    
    success = detailed_image_analysis(failing_image)
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 DIAGNOSIS COMPLETE: LoRA extraction working correctly!")
    else:
        print("❌ DIAGNOSIS FAILED: Critical issues found in LoRA extraction")
    print("=" * 80)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())

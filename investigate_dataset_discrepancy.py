#!/usr/bin/env python3
"""
Dataset Discrepancy Investigation

This script investigates the massive discrepancy in dataset sizes:
- Original: 81,187 entries
- Expected after cleaning: ~50,000+ entries  
- Actual after cleaning: 8,501 entries

We need to find where 40,000+ entries disappeared!

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import logging
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import List, Tuple, Dict, Set, Any
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_dataset(path: str):
    """Load dataset from pickle file."""
    logger.info(f"Loading dataset from {path}...")
    with open(path, 'rb') as f:
        dataset = dill.load(f)
    logger.info(f"Loaded {len(dataset)} entries")
    return dataset


def analyze_original_dataset():
    """Analyze the original production dataset."""
    logger.info("="*80)
    logger.info("ANALYZING ORIGINAL DATASET")
    logger.info("="*80)
    
    dataset = load_dataset("production_dataset.pkl")
    
    # Basic statistics
    logger.info(f"Total entries: {len(dataset):,}")
    
    # Analyze goodness scores
    goodness_scores = [goodness for _, _, goodness in dataset]
    good_count = sum(1 for score in goodness_scores if score > 0.5)
    normal_count = len(goodness_scores) - good_count
    
    logger.info(f"Good entries (>0.5): {good_count:,} ({good_count/len(dataset)*100:.1f}%)")
    logger.info(f"Normal entries (<=0.5): {normal_count:,} ({normal_count/len(dataset)*100:.1f}%)")
    
    # Analyze tag relevance (1girl/2girls)
    relevant_count = 0
    irrelevant_count = 0
    
    for filename, tags, goodness in dataset:
        tag_names = [tag.lower() for tag, weight in tags]
        is_relevant = any(tag in tag_names for tag in ['1girl', '2girls'])
        
        if is_relevant:
            relevant_count += 1
        else:
            irrelevant_count += 1
    
    logger.info(f"Relevant entries (1girl/2girls): {relevant_count:,} ({relevant_count/len(dataset)*100:.1f}%)")
    logger.info(f"Irrelevant entries: {irrelevant_count:,} ({irrelevant_count/len(dataset)*100:.1f}%)")
    
    # Analyze file path patterns
    base_level_count = 0
    subfolder_count = 0
    unique_filenames = set()

    for filename, tags, goodness in dataset:
        path_parts = Path(filename).parts
        base_filename = Path(filename).name

        # Find date folder to create proper unique key
        date_folder = None
        for part in path_parts:
            if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                date_folder = part
                break

        # Create proper unique key: date_folder + base_filename
        if date_folder:
            unique_key = f"{date_folder}/{base_filename}"
        else:
            unique_key = base_filename  # Fallback for files without date folders

        unique_filenames.add(unique_key)
        
        path_parts = Path(filename).parts
        
        # Look for date folder patterns
        date_pattern = None
        for part in path_parts:
            if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                date_pattern = part
                break
        
        if date_pattern and date_pattern in path_parts:
            date_index = path_parts.index(date_pattern)
            remaining_parts = path_parts[date_index+1:]
            
            if len(remaining_parts) == 1 and remaining_parts[0].startswith('ComfyUI_'):
                base_level_count += 1
            else:
                subfolder_count += 1
    
    logger.info(f"Base-level images: {base_level_count:,}")
    logger.info(f"Subfolder images: {subfolder_count:,}")
    logger.info(f"Unique filenames: {len(unique_filenames):,}")
    logger.info(f"Duplication ratio: {len(dataset) / len(unique_filenames):.2f}x")
    
    return {
        'total': len(dataset),
        'good_count': good_count,
        'normal_count': normal_count,
        'relevant_count': relevant_count,
        'irrelevant_count': irrelevant_count,
        'base_level_count': base_level_count,
        'subfolder_count': subfolder_count,
        'unique_filenames': len(unique_filenames)
    }


def analyze_cleaned_dataset():
    """Analyze the cleaned dataset."""
    logger.info("="*80)
    logger.info("ANALYZING CLEANED DATASET")
    logger.info("="*80)
    
    dataset = load_dataset("cleaned_dataset.pkl")
    
    # Basic statistics
    logger.info(f"Total entries: {len(dataset):,}")
    
    # Analyze goodness scores
    goodness_scores = [goodness for _, _, goodness in dataset]
    good_count = sum(1 for score in goodness_scores if score > 0.5)
    normal_count = len(goodness_scores) - good_count
    
    logger.info(f"Good entries (>0.5): {good_count:,} ({good_count/len(dataset)*100:.1f}%)")
    logger.info(f"Normal entries (<=0.5): {normal_count:,} ({normal_count/len(dataset)*100:.1f}%)")
    
    # Analyze tag relevance
    relevant_count = 0
    irrelevant_count = 0
    
    for filename, tags, goodness in dataset:
        tag_names = [tag.lower() for tag, weight in tags]
        is_relevant = any(tag in tag_names for tag in ['1girl', '2girls'])
        
        if is_relevant:
            relevant_count += 1
        else:
            irrelevant_count += 1
    
    logger.info(f"Relevant entries (1girl/2girls): {relevant_count:,} ({relevant_count/len(dataset)*100:.1f}%)")
    logger.info(f"Irrelevant entries: {irrelevant_count:,} ({irrelevant_count/len(dataset)*100:.1f}%)")
    
    # Check for unique filenames
    unique_filenames = set()
    for filename, tags, goodness in dataset:
        base_filename = Path(filename).name
        unique_filenames.add(base_filename)
    
    logger.info(f"Unique filenames: {len(unique_filenames):,}")
    logger.info(f"Duplication ratio: {len(dataset) / len(unique_filenames):.2f}x")
    
    return {
        'total': len(dataset),
        'good_count': good_count,
        'normal_count': normal_count,
        'relevant_count': relevant_count,
        'irrelevant_count': irrelevant_count,
        'unique_filenames': len(unique_filenames)
    }


def simulate_expected_cleaning():
    """Simulate what the cleaning should have produced."""
    logger.info("="*80)
    logger.info("SIMULATING EXPECTED CLEANING RESULTS")
    logger.info("="*80)
    
    dataset = load_dataset("production_dataset.pkl")
    
    # Step 1: Remove duplicates (keep base-level only)
    # CORRECTED: Use date_folder + base_filename as unique key
    unique_filenames = {}  # unique_key -> list of entries

    for i, (filename, tags, goodness) in enumerate(dataset):
        path_parts = Path(filename).parts
        base_filename = Path(filename).name

        # Find date folder to create proper unique key
        date_folder = None
        for part in path_parts:
            if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                date_folder = part
                break

        # Create proper unique key: date_folder + base_filename
        if date_folder:
            unique_key = f"{date_folder}/{base_filename}"
        else:
            unique_key = base_filename  # Fallback for files without date folders

        if unique_key not in unique_filenames:
            unique_filenames[unique_key] = []
        
        path_parts = Path(filename).parts
        
        # Check if it's base-level
        is_base_level = False
        date_pattern = None
        for part in path_parts:
            if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                date_pattern = part
                break
        
        if date_pattern and date_pattern in path_parts:
            date_index = path_parts.index(date_pattern)
            remaining_parts = path_parts[date_index+1:]
            
            if len(remaining_parts) == 1 and remaining_parts[0].startswith('ComfyUI_'):
                is_base_level = True
        
        unique_filenames[unique_key].append({
            'index': i,
            'entry': (filename, tags, goodness),
            'is_base_level': is_base_level
        })

    # Keep only one entry per unique key (prefer base-level)
    deduplicated_entries = []

    for unique_key, entries in unique_filenames.items():
        # Prefer base-level entries
        base_level_entries = [e for e in entries if e['is_base_level']]
        
        if base_level_entries:
            # Keep the first base-level entry
            deduplicated_entries.append(base_level_entries[0]['entry'])
        else:
            # No base-level entries, keep the first one
            deduplicated_entries.append(entries[0]['entry'])
    
    logger.info(f"After deduplication: {len(deduplicated_entries):,} entries")
    
    # Step 2: Filter out irrelevant entries
    relevant_entries = []
    
    for filename, tags, goodness in deduplicated_entries:
        tag_names = [tag.lower() for tag, weight in tags]
        is_relevant = any(tag in tag_names for tag in ['1girl', '2girls'])
        
        if is_relevant:
            relevant_entries.append((filename, tags, goodness))
    
    logger.info(f"After relevance filtering: {len(relevant_entries):,} entries")
    
    return {
        'after_deduplication': len(deduplicated_entries),
        'after_relevance_filter': len(relevant_entries)
    }


def compare_cleaning_methods():
    """Compare the actual cleaning with expected cleaning."""
    logger.info("="*80)
    logger.info("COMPARING CLEANING METHODS")
    logger.info("="*80)
    
    # Load both datasets
    original = load_dataset("production_dataset.pkl")
    cleaned = load_dataset("cleaned_dataset.pkl")
    
    # Find which entries were kept
    cleaned_filenames = set()
    for filename, tags, goodness in cleaned:
        cleaned_filenames.add(filename)  # Use full path
    
    # Analyze what was removed
    removed_count = 0
    kept_count = 0
    
    for filename, tags, goodness in original:
        if filename in cleaned_filenames:
            kept_count += 1
        else:
            removed_count += 1
    
    logger.info(f"Original entries: {len(original):,}")
    logger.info(f"Entries kept: {kept_count:,}")
    logger.info(f"Entries removed: {removed_count:,}")
    logger.info(f"Removal rate: {removed_count/len(original)*100:.1f}%")
    
    # Check if the issue is with exact filename matching
    cleaned_base_filenames = set()
    for filename, tags, goodness in cleaned:
        base_filename = Path(filename).name
        cleaned_base_filenames.add(base_filename)
    
    logger.info(f"Unique base filenames in cleaned dataset: {len(cleaned_base_filenames):,}")
    
    # Count how many original entries have base filenames that exist in cleaned
    matching_base_count = 0
    for filename, tags, goodness in original:
        base_filename = Path(filename).name
        if base_filename in cleaned_base_filenames:
            matching_base_count += 1
    
    logger.info(f"Original entries with matching base filenames: {matching_base_count:,}")


def main():
    """Main investigation function."""
    logger.info("STARTING DATASET DISCREPANCY INVESTIGATION")
    logger.info("="*80)
    
    # Analyze original dataset
    original_stats = analyze_original_dataset()
    
    # Analyze cleaned dataset  
    cleaned_stats = analyze_cleaned_dataset()
    
    # Simulate expected cleaning
    expected_stats = simulate_expected_cleaning()
    
    # Compare methods
    compare_cleaning_methods()
    
    # Summary
    logger.info("="*80)
    logger.info("INVESTIGATION SUMMARY")
    logger.info("="*80)
    
    logger.info(f"Original dataset: {original_stats['total']:,} entries")
    logger.info(f"Expected after deduplication: {expected_stats['after_deduplication']:,} entries")
    logger.info(f"Expected after relevance filter: {expected_stats['after_relevance_filter']:,} entries")
    logger.info(f"Actual cleaned dataset: {cleaned_stats['total']:,} entries")
    
    expected_final = expected_stats['after_relevance_filter']
    actual_final = cleaned_stats['total']
    discrepancy = expected_final - actual_final
    
    logger.info(f"DISCREPANCY: {discrepancy:,} entries missing!")
    logger.info(f"Missing percentage: {discrepancy/expected_final*100:.1f}%")
    
    if discrepancy > 0:
        logger.error("CRITICAL ISSUE: Cleaning process removed too many entries!")
        logger.error("The cleaning logic has a serious bug that needs investigation.")


if __name__ == "__main__":
    main()

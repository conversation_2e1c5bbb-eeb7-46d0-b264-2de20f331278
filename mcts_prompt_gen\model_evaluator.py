#!/usr/bin/env python3
"""
Comprehensive Model Evaluation Framework

This script provides comprehensive evaluation of trained LightGBM models with
ranking-focused metrics, visualizations, and analysis tools.

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional
import dill
import json
from datetime import datetime
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# ML imports
from sklearn.metrics import (
    roc_auc_score, precision_recall_curve, roc_curve, average_precision_score,
    confusion_matrix, classification_report, precision_score, recall_score,
    f1_score, accuracy_score
)
from sklearn.calibration import calibration_curve
import lightgbm as lgb
from scipy import stats
from wordcloud import WordCloud


class ModelEvaluator:
    """Comprehensive model evaluation with ranking-focused metrics."""
    
    def __init__(self, results_dir: str = "results"):
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(exist_ok=True)
        
        # Create timestamped subdirectory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = self.results_dir / f"evaluation_{timestamp}"
        self.run_dir.mkdir(exist_ok=True)
        
        self.model = None
        self.feature_extractor = None
        self.test_data = None
        self.predictions = None
        self.probabilities = None
        self.metrics = {}
        
        print(f"Evaluation results will be saved to: {self.run_dir}")
    
    def load_model_and_data(self, model_dir: str, test_data_file: str):
        """Load trained model and test data."""
        print("="*60)
        print("LOADING MODEL AND TEST DATA")
        print("="*60)
        
        model_dir = Path(model_dir)
        
        # Load model
        model_file = model_dir / "lightgbm_model.pkl"
        if not model_file.exists():
            raise FileNotFoundError(f"Model file not found: {model_file}")
        
        with open(model_file, 'rb') as f:
            self.model = dill.load(f)
        
        # Load feature extractor
        feature_extractor_file = model_dir / "feature_extractor.pkl"
        if not feature_extractor_file.exists():
            raise FileNotFoundError(f"Feature extractor not found: {feature_extractor_file}")
        
        with open(feature_extractor_file, 'rb') as f:
            self.feature_extractor = dill.load(f)
        
        # Load test data
        with open(test_data_file, 'rb') as f:
            test_tuples = dill.load(f)
        
        self.test_data = {
            'filenames': [item[0] for item in test_tuples],
            'prompts': [item[1] for item in test_tuples],
            'labels': [item[2] for item in test_tuples]
        }
        
        print(f"✓ Model loaded successfully")
        print(f"✓ Feature extractor loaded successfully")
        print(f"✓ Test data loaded: {len(test_tuples)} samples")
        
        # Class distribution
        good_count = sum(1 for label in self.test_data['labels'] if label == 'good')
        normal_count = len(self.test_data['labels']) - good_count
        print(f"Test set distribution - Good: {good_count}, Normal: {normal_count}")
    
    def generate_predictions(self):
        """Generate predictions on test data."""
        print("\n" + "="*60)
        print("GENERATING PREDICTIONS")
        print("="*60)
        
        # Extract features
        X_test, y_test = self.feature_extractor.transform(
            self.test_data['prompts'], 
            self.test_data['labels']
        )
        
        # Generate predictions
        self.probabilities = self.model.predict(X_test, num_iteration=self.model.best_iteration)
        self.predictions = (self.probabilities > 0.5).astype(int)
        self.true_labels = y_test
        
        print(f"✓ Generated predictions for {len(self.predictions)} samples")
        print(f"✓ Prediction range: [{self.probabilities.min():.4f}, {self.probabilities.max():.4f}]")
    
    def calculate_ranking_metrics(self):
        """Calculate ranking-focused evaluation metrics."""
        print("\n" + "="*60)
        print("CALCULATING RANKING METRICS")
        print("="*60)
        
        # Basic classification metrics
        self.metrics['accuracy'] = accuracy_score(self.true_labels, self.predictions)
        self.metrics['precision'] = precision_score(self.true_labels, self.predictions)
        self.metrics['recall'] = recall_score(self.true_labels, self.predictions)
        self.metrics['f1_score'] = f1_score(self.true_labels, self.predictions)
        self.metrics['roc_auc'] = roc_auc_score(self.true_labels, self.probabilities)
        self.metrics['avg_precision'] = average_precision_score(self.true_labels, self.probabilities)
        
        # Ranking quality metrics
        good_scores = self.probabilities[self.true_labels == 1]
        normal_scores = self.probabilities[self.true_labels == 0]
        
        if len(good_scores) > 0 and len(normal_scores) > 0:
            # Mann-Whitney U test (measures ranking quality)
            u_stat, p_value = stats.mannwhitneyu(good_scores, normal_scores, alternative='greater')
            self.metrics['mann_whitney_u'] = u_stat
            self.metrics['mann_whitney_p'] = p_value
            
            # Mean score difference
            self.metrics['mean_score_diff'] = np.mean(good_scores) - np.mean(normal_scores)
            self.metrics['good_mean_score'] = np.mean(good_scores)
            self.metrics['normal_mean_score'] = np.mean(normal_scores)
            
            # Precision at K metrics
            sorted_indices = np.argsort(self.probabilities)[::-1]  # Descending order
            sorted_labels = self.true_labels[sorted_indices]
            
            for k in [10, 20, 50, 100]:
                if k <= len(sorted_labels):
                    precision_at_k = np.mean(sorted_labels[:k])
                    self.metrics[f'precision_at_{k}'] = precision_at_k
        
        # NDCG calculation
        self.metrics['ndcg'] = self._calculate_ndcg(self.true_labels, self.probabilities)
        
        # Print metrics
        print("Classification Metrics:")
        print(f"  Accuracy: {self.metrics['accuracy']:.4f}")
        print(f"  Precision: {self.metrics['precision']:.4f}")
        print(f"  Recall: {self.metrics['recall']:.4f}")
        print(f"  F1-Score: {self.metrics['f1_score']:.4f}")
        print(f"  ROC-AUC: {self.metrics['roc_auc']:.4f}")
        print(f"  Average Precision: {self.metrics['avg_precision']:.4f}")
        
        print("\nRanking Metrics:")
        if 'mean_score_diff' in self.metrics:
            print(f"  Mean Score Difference: {self.metrics['mean_score_diff']:.4f}")
            print(f"  Good Prompts Mean Score: {self.metrics['good_mean_score']:.4f}")
            print(f"  Normal Prompts Mean Score: {self.metrics['normal_mean_score']:.4f}")
            print(f"  Mann-Whitney U p-value: {self.metrics['mann_whitney_p']:.6f}")
        
        print(f"  NDCG: {self.metrics['ndcg']:.4f}")
        
        for k in [10, 20, 50, 100]:
            if f'precision_at_{k}' in self.metrics:
                print(f"  Precision@{k}: {self.metrics[f'precision_at_{k}']:.4f}")
    
    def _calculate_ndcg(self, y_true, y_scores, k=None):
        """Calculate Normalized Discounted Cumulative Gain."""
        if k is None:
            k = len(y_true)
        
        # Sort by predicted scores (descending)
        sorted_indices = np.argsort(y_scores)[::-1][:k]
        y_true_sorted = y_true[sorted_indices]
        
        # Calculate DCG
        dcg = 0
        for i, relevance in enumerate(y_true_sorted):
            dcg += relevance / np.log2(i + 2)  # i+2 because log2(1) = 0
        
        # Calculate IDCG (ideal DCG)
        y_true_ideal = np.sort(y_true)[::-1][:k]
        idcg = 0
        for i, relevance in enumerate(y_true_ideal):
            idcg += relevance / np.log2(i + 2)
        
        # NDCG
        if idcg == 0:
            return 0
        return dcg / idcg
    
    def create_evaluation_plots(self):
        """Create comprehensive evaluation visualizations."""
        print("\n" + "="*60)
        print("CREATING EVALUATION PLOTS")
        print("="*60)
        
        # Create a large figure with multiple subplots
        fig = plt.figure(figsize=(20, 15))
        
        # 1. ROC Curve
        ax1 = plt.subplot(3, 4, 1)
        fpr, tpr, _ = roc_curve(self.true_labels, self.probabilities)
        ax1.plot(fpr, tpr, color='darkorange', lw=2, 
                label=f'ROC curve (AUC = {self.metrics["roc_auc"]:.3f})')
        ax1.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        ax1.set_xlim([0.0, 1.0])
        ax1.set_ylim([0.0, 1.05])
        ax1.set_xlabel('False Positive Rate')
        ax1.set_ylabel('True Positive Rate')
        ax1.set_title('ROC Curve')
        ax1.legend(loc="lower right")
        ax1.grid(True, alpha=0.3)
        
        # 2. Precision-Recall Curve
        ax2 = plt.subplot(3, 4, 2)
        precision, recall, _ = precision_recall_curve(self.true_labels, self.probabilities)
        ax2.plot(recall, precision, color='blue', lw=2,
                label=f'PR curve (AP = {self.metrics["avg_precision"]:.3f})')
        ax2.set_xlabel('Recall')
        ax2.set_ylabel('Precision')
        ax2.set_title('Precision-Recall Curve')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. Score Distribution
        ax3 = plt.subplot(3, 4, 3)
        good_scores = self.probabilities[self.true_labels == 1]
        normal_scores = self.probabilities[self.true_labels == 0]
        
        ax3.hist(normal_scores, bins=30, alpha=0.7, label='Normal', color='orange', density=True)
        ax3.hist(good_scores, bins=30, alpha=0.7, label='Good', color='green', density=True)
        ax3.set_xlabel('Predicted Score')
        ax3.set_ylabel('Density')
        ax3.set_title('Score Distribution by Quality')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. Confusion Matrix
        ax4 = plt.subplot(3, 4, 4)
        cm = confusion_matrix(self.true_labels, self.predictions)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax4,
                   xticklabels=['Normal', 'Good'], yticklabels=['Normal', 'Good'])
        ax4.set_title('Confusion Matrix')
        ax4.set_ylabel('True Label')
        ax4.set_xlabel('Predicted Label')
        
        # 5. Calibration Plot
        ax5 = plt.subplot(3, 4, 5)
        fraction_of_positives, mean_predicted_value = calibration_curve(
            self.true_labels, self.probabilities, n_bins=10
        )
        ax5.plot(mean_predicted_value, fraction_of_positives, "s-", label="Model")
        ax5.plot([0, 1], [0, 1], "k:", label="Perfectly calibrated")
        ax5.set_xlabel('Mean Predicted Probability')
        ax5.set_ylabel('Fraction of Positives')
        ax5.set_title('Calibration Plot')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. Feature Importance
        ax6 = plt.subplot(3, 4, 6)
        if hasattr(self.model, 'feature_importance'):
            importance = self.model.feature_importance(importance_type='gain')
            feature_names = self.feature_extractor.get_feature_names()
            
            # Get top 15 features
            top_indices = np.argsort(importance)[-15:]
            top_importance = importance[top_indices]
            top_names = [feature_names[i] if i < len(feature_names) else f'feature_{i}' 
                        for i in top_indices]
            
            ax6.barh(range(len(top_importance)), top_importance)
            ax6.set_yticks(range(len(top_importance)))
            ax6.set_yticklabels(top_names, fontsize=8)
            ax6.set_xlabel('Feature Importance')
            ax6.set_title('Top 15 Feature Importance')
        
        # 7. Precision at K
        ax7 = plt.subplot(3, 4, 7)
        k_values = [k for k in [10, 20, 50, 100, 200, 500] if f'precision_at_{k}' in self.metrics]
        precision_at_k_values = [self.metrics[f'precision_at_{k}'] for k in k_values]
        
        if k_values:
            ax7.plot(k_values, precision_at_k_values, 'o-', color='purple')
            ax7.set_xlabel('K')
            ax7.set_ylabel('Precision@K')
            ax7.set_title('Precision at K')
            ax7.grid(True, alpha=0.3)
        
        # 8. Score vs True Label Scatter
        ax8 = plt.subplot(3, 4, 8)
        jitter = np.random.normal(0, 0.05, len(self.true_labels))
        ax8.scatter(self.true_labels + jitter, self.probabilities, alpha=0.6, s=20)
        ax8.set_xlabel('True Label (with jitter)')
        ax8.set_ylabel('Predicted Score')
        ax8.set_title('Predictions vs True Labels')
        ax8.set_xticks([0, 1])
        ax8.set_xticklabels(['Normal', 'Good'])
        ax8.grid(True, alpha=0.3)
        
        # 9-12: Additional analysis plots will be added in the next section
        
        plt.tight_layout()
        
        # Save the comprehensive plot
        eval_plot_file = self.run_dir / "comprehensive_evaluation.png"
        plt.savefig(eval_plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Comprehensive evaluation plot saved to: {eval_plot_file}")
    
    def analyze_feature_importance(self):
        """Detailed feature importance analysis."""
        print("\n" + "="*60)
        print("FEATURE IMPORTANCE ANALYSIS")
        print("="*60)
        
        if not hasattr(self.model, 'feature_importance'):
            print("Model does not support feature importance analysis")
            return
        
        importance = self.model.feature_importance(importance_type='gain')
        feature_names = self.feature_extractor.get_feature_names()
        
        # Create feature importance DataFrame
        feature_df = pd.DataFrame({
            'feature': feature_names[:len(importance)],
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        # Save feature importance
        feature_file = self.run_dir / "feature_importance.csv"
        feature_df.to_csv(feature_file, index=False)
        
        print(f"✓ Feature importance saved to: {feature_file}")
        print(f"Top 10 most important features:")
        for i, (_, row) in enumerate(feature_df.head(10).iterrows()):
            print(f"  {i+1}. {row['feature']}: {row['importance']:.2f}")
    
    def save_evaluation_results(self):
        """Save all evaluation results and metrics."""
        print("\n" + "="*60)
        print("SAVING EVALUATION RESULTS")
        print("="*60)
        
        # Save metrics
        metrics_file = self.run_dir / "evaluation_metrics.json"
        with open(metrics_file, 'w') as f:
            json.dump(self.metrics, f, indent=2)
        
        # Save predictions
        predictions_df = pd.DataFrame({
            'filename': self.test_data['filenames'],
            'prompt': self.test_data['prompts'],
            'true_label': [self.feature_extractor.label_encoder.inverse_transform([label])[0] 
                          for label in self.true_labels],
            'predicted_score': self.probabilities,
            'predicted_label': [self.feature_extractor.label_encoder.inverse_transform([pred])[0] 
                               for pred in self.predictions]
        })
        
        predictions_file = self.run_dir / "detailed_predictions.csv"
        predictions_df.to_csv(predictions_file, index=False)
        
        print(f"✓ Metrics saved to: {metrics_file}")
        print(f"✓ Detailed predictions saved to: {predictions_file}")
    
    def run_evaluation_pipeline(self, model_dir: str, test_data_file: str):
        """Run the complete evaluation pipeline."""
        print("Starting comprehensive model evaluation...")
        
        self.load_model_and_data(model_dir, test_data_file)
        self.generate_predictions()
        self.calculate_ranking_metrics()
        self.create_evaluation_plots()
        self.analyze_feature_importance()
        self.save_evaluation_results()
        
        print("\n" + "="*60)
        print("EVALUATION PIPELINE COMPLETED")
        print("="*60)
        print(f"All results saved to: {self.run_dir}")
        
        return self.metrics


def main():
    """Main function with command-line interface."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Comprehensive model evaluation")
    parser.add_argument('--model-dir', required=True, 
                       help='Directory containing trained model and feature extractor')
    parser.add_argument('--test-data', required=True, 
                       help='Path to test data pickle file')
    parser.add_argument('--results-dir', default='results', 
                       help='Results directory')
    
    args = parser.parse_args()
    
    # Create evaluator and run pipeline
    evaluator = ModelEvaluator(args.results_dir)
    
    try:
        metrics = evaluator.run_evaluation_pipeline(args.model_dir, args.test_data)
        
        print("\n🎉 Evaluation completed successfully!")
        print(f"Key metrics:")
        print(f"  ROC-AUC: {metrics['roc_auc']:.4f}")
        print(f"  Average Precision: {metrics['avg_precision']:.4f}")
        print(f"  NDCG: {metrics['ndcg']:.4f}")
        if 'mean_score_diff' in metrics:
            print(f"  Mean Score Difference: {metrics['mean_score_diff']:.4f}")
        
    except Exception as e:
        print(f"\n❌ Evaluation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

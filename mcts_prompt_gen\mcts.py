import math
import numpy as np
import random
from collections import defaultdict

class MCTSNode:
    def __init__(self, state, parent=None, action=None):
        self.state = state
        self.parent = parent
        self.action = action
        self.children = {}
        self.visit_count = 0
        self.value_sum = 0
        self.prior = 0
        
    def value(self):
        if self.visit_count == 0:
            return 0
        return self.value_sum / self.visit_count
    
    def expanded(self):
        return len(self.children) > 0
    
    def select_child(self, c_puct=1.0):
        """Select child with highest UCB score."""
        log_sum = math.log(max(1, self.visit_count))
        
        best_score = -float('inf')
        best_action = None
        best_child = None
        
        for action, child in self.children.items():
            # UCB formula: Q(s,a) + c_puct * P(s,a) * sqrt(N(s)) / (1 + N(s,a))
            exploit = child.value()
            explore = c_puct * child.prior * math.sqrt(log_sum) / (1 + child.visit_count)
            score = exploit + explore
            
            if score > best_score:
                best_score = score
                best_action = action
                best_child = child
                
        return best_action, best_child
    
    def expand(self, actions, priors):
        """Expand node with given actions and priors."""
        for action, prior in zip(actions, priors):
            if action not in self.children:
                new_state = self.state.apply(action)
                self.children[action] = MCTSNode(new_state, parent=self, action=action)
                self.children[action].prior = prior
    
    def update(self, value):
        """Update node statistics."""
        self.visit_count += 1
        self.value_sum += value

class MCTS:
    def __init__(self, model, num_simulations=100, c_puct=1.0, dirichlet_alpha=0.3, dirichlet_weight=0.25):
        self.model = model
        self.num_simulations = num_simulations
        self.c_puct = c_puct
        self.dirichlet_alpha = dirichlet_alpha
        self.dirichlet_weight = dirichlet_weight
        
    def search(self, state):
        """Perform MCTS search from given state."""
        root = MCTSNode(state)
        
        # Get initial policy and value
        policy, value = self.model.predict(state.features())
        
        # Add Dirichlet noise to root policy for exploration
        actions = state.legal_actions()
        noise = np.random.dirichlet([self.dirichlet_alpha] * len(actions))
        priors = []
        
        for i, action in enumerate(actions):
            prior = policy[action]
            prior = (1 - self.dirichlet_weight) * prior + self.dirichlet_weight * noise[i]
            priors.append(prior)
            
        # Expand root with all legal actions
        root.expand(actions, priors)
        
        # Perform simulations
        for _ in range(self.num_simulations):
            node = root
            search_path = [node]
            
            # Selection phase - traverse tree until we reach a leaf
            while node.expanded():
                action, node = node.select_child(self.c_puct)
                search_path.append(node)
            
            # Get state value
            if node.visit_count > 0:  # If not a new node
                # Expansion phase
                policy, value = self.model.predict(node.state.features())
                actions = node.state.legal_actions()
                priors = [policy[action] for action in actions]
                node.expand(actions, priors)
            else:
                # Evaluation phase for new node
                policy, value = self.model.predict(node.state.features())
            
            # Backup phase - update statistics for all nodes in search path
            for node in reversed(search_path):
                node.update(value)
                
        # Return action probabilities based on visit counts
        visit_counts = np.array([child.visit_count for child in root.children.values()])
        actions = list(root.children.keys())
        
        # Convert visit counts to probabilities
        if visit_counts.sum() == 0:
            probs = np.ones_like(visit_counts) / len(visit_counts)
        else:
            probs = visit_counts / visit_counts.sum()
            
        return actions, probs
import dill
filename = 'F:/Code/PlayGround/yeflib/tg/tgsaved_images/infos.pkl'
know_names, infos = dill.load(open(filename, 'rb'))
import os, json
from collections import defaultdict

import numpy as np
import networkx as nx

# data = [tags, ps, edges]
gen_infos_filename = './gen_infos.pkl'
if os.path.exists(gen_infos_filename):
    import dill
    with open(gen_infos_filename, 'rb') as f:
        data = dill.load(f)
else:
    print("No gen_infos.pkl found, please run build.py first.")
    exit(1)

tagcount, ps, edges = data

for message_id, tags in ps.items():
    break

print(message_id, tags)
print(','.join(tags))

{"metadata": {"user": {"name": "Yef flower", "email": "<EMAIL>"}, "dates": {"created": "6/21/2025 15:50", "updated": "6/21/2025 16:02", "exported": "6/23/2025 2:45"}, "powered_by": "ChatGPT Exporter (https://www.chatgptexporter.com)"}, "messages": [{"role": "Prompt", "say": "comfygen.py\n\n\nPython\n\n\nnovelai_gen_rc.py\n\n\nPython\n\n\ncomfygen.json\n\n\nFile\n\n\n我在写一个简易的随机生成danbooru tag格式prompt的代码，然后用comfyui生成对应图片。现在的代码(comfygen.py)非常简陋，只实现了最基础的生成，我想参考之前的另一份代码(novelai_gen_rc.py)，里面有一段更复杂的生成角色和衣服的函数。\n首先，应该设计一个更合理的结构，来存储这些随机的分支。目前的代码有3处不合理的地方：\n\n\nlora应该有权重，现在只能随机权重，但是可能有些权重有不同的分布，比如lora1适合0.8到1，而lora2可能要求必须权重在0.2到0.7之间才效果好，甚至有些特殊lora比如outline, 它的效果如果是-1则会消除轮廓线，配合flat color的tag反而效果更好。\n\n现有part之间有重合的关系。比如现在的角色yixuan，她有一套默认衣服，是黄色夹克，但是也可以换其他衣服（黑色西装等等）。如果有新的角色，新角色的默认衣服不是黄色夹克，那么新角色的衣服tag在随机的时候，不应该再给yixuan的这套衣服分配概率。也就是应该有不同part之间的联动，但又保留一些随机性。\n\n同一个part内可能多个tag都应该选。现有代码一定是每个part内部恰好选一个出来，但是这样很僵硬，实际上比如衣服，有些衣服是一整套，有些衣服则应该是上衣和下装两个子类别的组合，再比如style，也可能有多种风格一起出现，且不同权重。还有background，既要有简单背景之类的概括tag，也要有indoors outdoors之类的场景tag。\n上面这些都表明现有结构过于简单，难以表达这些复杂的关系。应该用一个更合理的数据结构来存储。 其次，这些关系不应该全靠手工编写，而是应该从我之前收集的10000张图片与prompt数据集里计算一些。 因此这个新的数据结构还需要能方便计算和拟合。 这是指两种意义上的计算，类似于AlphaZero的模型设计，一个是policy即生成多种多样prompt的概率，一个是value即这个模型应该能预判一个prompt的最终质量（比如用户是否喜欢，可以来自已有图片的标注初始化等等）。比如如果只考虑style, 可以认为不同style是相互独立的，而且可以相互叠加，只是有些组合会不好看或者相互矛盾。这就可以当成一个高维向量，每一维代表一种style，而模型应该根据用户反馈，拟合并动态调整一个高维分布。 但是角色特征，表情，衣服等等会更复杂，因为那些内容不能看成是相互独立的，或者说比style更容易相互矛盾。 \n最后，数据结构也应该考虑额外的采样参数，比如步数，cfg，长和宽，采样方式（虽然可以默认全用euler_a）。我设想的是把这个结构封装为一个class Data， 然后再把一个具体的prompt封装为class Prompt，然后可以prompt.queue()来调用api加入comfyui的队列，而它内部变量存储着各种生成信息，它的构造函数能根据Data随机采样一个合理的prompt，它的各种工具函数可以方便调整; 而Data应该能根据输入一堆prompt学一个更好的分布用来随机生成。 \n请分析上述需求，然后给出完整的设计，最后写出完整的python代码"}, {"role": "Response", "say": "为了更好地满足你的需求，请确认以下几点：\n\n\n你希望新设计的数据结构主要以哪些形式保存？例如 JSON 配置文件、Python dict、或自定义类结构？\n\nprompt质量的评价机制（value function）是否已有初步设计？还是你希望从已有图像的元数据或用户标注中自动提取并拟合？\n\n是否计划用已有的图像+prompt数据集来训练一个预测prompt效果的模型？如是，格式是否标准（如图像和prompt一一对应）？\n\n输出目标仅为ComfyUI生成图片，还是你希望之后也支持NovelAI之类其他平台的prompt构造？\n\n\n确认后我会给出新的结构设计和完整代码实现。"}, {"role": "Prompt", "say": "json比较好，这样python也可以读\n\n什么都还没有。也许可以用lightgbm拟合一个简单模型，从现有数据里建模\n\n对，不过因为是diffusion模型，图像生成有随机性，图片和prompt不完全一一对应，只能说大部分prompt都是一一对应，但是有些prompt会对应多张图片\n\n对，目前是以comfyui为主，但是这个应该很容易改成novelAI的api，因为生成的参数基本都一样，甚至因为novelai没有lora还更简单一些"}, {"role": "Response", "say": "好的，我将为你设计一个更合理的数据结构 Data 和 Prompt 类，并支持：\n\n\nLoRA权重范围和分布定义\n\n部件间依赖/联动，例如角色与其默认衣装\n\n同一part内支持多个tag的加权组合\n\nJSON格式保存，Python友好\n\n支持采样参数（steps, cfg等）\n\n后续可扩展至通过数据拟合Policy和Value模型，如LightGBM\n\n\n我将以这些目标编写结构定义和完整Python代码，稍后返回给你。请稍等。\n\n\nResearch completed in 6m · 14 sources · 85 searches\n\n\n重构ComfyUI Prompt生成结构并实现采样系统\n\n\nPython Module for Danbooru-Style Prompt Generation in ComfyUI\n\n\n模块设计概述 (Overview)\n\n\n本模块旨在生成 Danbooru 风格的 Prompt，并与 ComfyUI 集成，以便批量生成高质量的动漫图像。模块主要包含两个核心类：Data 和 Prompt，分别负责数据存储/采样逻辑和单条 Prompt 实例的封装。通过合理的数据结构和采样算法，该模块能够根据预定义的标签(tag)、LoRA模型、权重分布以及依赖关系，随机组合出符合设定的 Prompt，并以 ComfyUI 所需的工作流格式输出以实现自动队列生成。整体设计确保代码结构清晰，方便扩展和维护。下面将详细介绍模块各部分的设计与实现。\n\n\nData 类：标签与采样数据管理\n\n\nData 类用于存储生成 Prompt 所需的所有信息，包括：\n\n\n标签库(tags)：按照部件分类存储可能用到的标签（如角色、服装、风格、背景等）。每个部件可包含一个或多个标签，甚至是标签组合，并可为每个标签指定默认权重或出现概率。\n\nLoRA 模型：支持多个 LoRA 项，每个 LoRA 定义有一个推荐权重分布（例如高斯分布或固定范围）。这允许在采样时对 LoRA 的强度进行随机化，模拟不同风格或特征注入reddit.com。例如，一个角色LoRA建议权重 0.81.0，而风格LoRA建议 0.20.7。\n\n参数默认值：可以在 Data 中定义生成时的默认采样参数范围（如 steps 步数、CFG 值、分辨率、随机种子等），以便 Prompt 生成时随机选取合理的参数值。如果需要，也可以包含默认的负面提示词列表用于负面Prompt。\n\n依赖关系：定义不同部件之间的逻辑依赖或条件约束。例如，角色与服装的对应关系：若选择特定角色，则服装应限定在该角色专属的选项内。这确保生成的 Prompt 更加符合角色设定和情景。依赖关系可以通过条件规则或在数据结构中嵌套来表示（例如角色对象中包含其专属服装列表）。\n\n\nJSON 序列化：Data 提供从 JSON 文件加载数据 (from_json) 以及保存当前数据到 JSON (to_json) 的功能。JSON 文件结构应当简洁明了，便于手动编辑和维护。例如，可以按以下层次组织：\n\n\n{\n  \"characters\": { \n    \"Yixuan\": {\n      \"tags\": [\"Yixuan\"], \n      \"clothing\": {\n        \"options\": [\"yellow jacket\", \"black boots\"],\n        \"weights\": [0.8, 0.2]\n      }\n    },\n    \"Akiko\": {\n      \"tags\": [\"Akiko\"],\n      \"clothing\": {\n        \"options\": [\"kimono\", \"sandals\"],\n        \"weights\": [0.5, 0.5]\n      }\n    },\n    \"Original\": {\n      \"tags\": [\"1girl\"], \n      \"clothing\": {\n        \"options\": [\"hoodie\", \"jeans\", \"skirt\"],\n        \"weights\": [0.3, 0.3, 0.4]\n      }\n    }\n  },\n  \"styles\": {\n    \"allow_multiple\": true,\n    \"min_count\": 1,\n    \"max_count\": 3,\n    \"options\": [\n      {\"tag\": \"masterpiece\", \"weight\": 1.0},\n      {\"tag\": \"best quality\", \"weight\": 1.0},\n      {\"tag\": \"cinematic lighting\", \"weight\": 0.8},\n      {\"tag\": \"retro artstyle\", \"weight\": 0.5}\n    ]\n  },\n  \"backgrounds\": {\n    \"allow_multiple\": false,\n    \"options\": [\n      {\"tag\": \"outdoor\", \"weight\": 1.0},\n      {\"tag\": \"sunset sky\", \"weight\": 0.7},\n      {\"tag\": \"city street\", \"weight\": 0.7}\n    ]\n  },\n  \"loras\": [\n    {\"name\": \"YixuanCharacterLoRA\", \"weight_dist\": {\"type\": \"uniform\", \"min\": 0.8, \"max\": 1.0}},\n    {\"name\": \"AnimeStyleLoRA\", \"weight_dist\": {\"type\": \"normal\", \"mean\": 0.5, \"std\": 0.1, \"min\": 0.2, \"max\": 0.7}}\n  ],\n  \"parameters\": {\n    \"steps\": {\"min\": 20, \"max\": 40},\n    \"cfg_scale\": {\"choices\": [7, 8, 9, 10]},\n    \"resolution\": {\"choices\": [\"512x512\", \"640x640\"]},\n    \"seed\": {\"min\": 1, \"max\": 999999999}\n  }\n}\n\n\n上述 JSON 结构体现了模块的数据组织方式：\n\n\ncharacters 中定义了每个角色自己的一组标签和可能的服装列表（例如 Yixuan 角色有特定的 “yellow jacket”）。“新角色(Original)”有自己的通用服装，不与已知角色共享衣装，这满足了**“新角色不共享这组衣装”**的要求。\n\nstyles 定义风格标签，允许 1~3 个风格标签叠加（allow_multiple=true），每个风格标签有自己独立的权重。比如既可以同时使用masterpiece和cinematic lighting两个风格标签，各自带有不同的强度wiki.monai.art。\n\nbackgrounds 定义背景标签（单选，不可多选）。\n\nloras 列出可用的 LoRA 模型及其建议权重分布（uniform 表示均匀分布、normal 表示高斯分布等），例如 Yixuan 的角色LoRA在0.8-1.0范围效果最佳，Anime风格LoRA在0.2-0.7较为适合。\n\nparameters 指定了一些采样参数范围，例如 steps 从20到40随机，CFG Scale在7~10中选择，分辨率从两个选项中选取，种子在指定范围内随机。\n\n\n接下来，我们看看 Data 类的实现概要：\n\n\nclass Data:\n    def __init__(self):\n        # 存储各部分数据的字典\n        self.characters = {}   # 存储角色及其专属信息\n        self.styles = {}       # 风格标签及配置\n        self.backgrounds = {}  # 背景标签及配置\n        self.loras = []        # LoRA列表\n        self.parameters = {}   # 参数默认值/分布\n        # 可根据需要添加更多部分，例如动作pose、表情等\n    \n    @classmethod\n    def from_json(cls, filepath):\n        \"\"\"从 JSON 文件加载配置，返回 Data 实例\"\"\"\n        with open(filepath, 'r', encoding='utf-8') as f:\n            data_dict = json.load(f)\n        data = cls()\n        # 将JSON字典中的内容赋值给Data对象属性\n        data.characters = data_dict.get(\"characters\", {})\n        data.styles = data_dict.get(\"styles\", {})\n        data.backgrounds = data_dict.get(\"backgrounds\", {})\n        data.loras = data_dict.get(\"loras\", [])\n        data.parameters = data_dict.get(\"parameters\", {})\n        return data\n    \n    def to_json(self, filepath):\n        \"\"\"将当前 Data 对象保存为 JSON 文件\"\"\"\n        data_dict = {\n            \"characters\": self.characters,\n            \"styles\": self.styles,\n            \"backgrounds\": self.backgrounds,\n            \"loras\": self.loras,\n            \"parameters\": self.parameters\n        }\n        with open(filepath, 'w', encoding='utf-8') as f:\n            json.dump(data_dict, f, indent=4)\n    \n    def sample_prompt(self):\n        \"\"\"根据当前数据随机采样生成一个 Prompt 实例\"\"\"\n        # 1. 随机选取角色\n        character_name = random.choice(list(self.characters.keys()))\n        char_info = self.characters[character_name]\n        # 2. 根据角色选取服装标签（若定义了专属服装，则从对应列表中选，否则全局衣装）\n        clothing_tags = []\n        if \"clothing\" in char_info:\n            clothing_opts = char_info[\"clothing\"][\"options\"]\n            clothing_weights = char_info[\"clothing\"].get(\"weights\")\n            # 从角色专属服装列表随机选择1-2件 (示例：可根据需要调整数量范围)\n            count = random.randint(1, 2)\n            clothing_tags = random.choices(clothing_opts, weights=clothing_weights, k=count)\n        else:\n            # 如果角色无专属服装，尝试全局衣装列表（假设定义了self.clothing全局）\n            global_clothing = self.parameters.get(\"global_clothing\", [])\n            if global_clothing:\n                clothing_tags = random.sample(global_clothing, k=1)\n        # 3. 选取风格标签（允许多个）\n        style_tags = []\n        if self.styles:\n            style_opts = self.styles.get(\"options\", [])\n            allow_multi = self.styles.get(\"allow_multiple\", False)\n            if allow_multi:\n                min_c = self.styles.get(\"min_count\", 1)\n                max_c = self.styles.get(\"max_count\", 1)\n                count = random.randint(min_c, max_c)\n            else:\n                count = 1\n            # 按权重随机选择count个不同风格标签\n            weights = [opt.get(\"weight\", 1.0) for opt in style_opts]\n            style_tags = random.choices([opt[\"tag\"] for opt in style_opts], weights=weights, k=count)\n            # 确保不重复\n            style_tags = list(set(style_tags))\n        # 4. 选取背景标签（单选）\n        background_tag = None\n        if self.backgrounds:\n            bg_opts = self.backgrounds.get(\"options\", [])\n            weights = [opt.get(\"weight\", 1.0) for opt in bg_opts]\n            background_tag = random.choices([opt[\"tag\"] for opt in bg_opts], weights=weights, k=1)[0]\n        # 5. 选取 LoRA 模型及其权重\n        lora_name = None\n        lora_weight = None\n        if self.loras:\n            lora_def = random.choice(self.loras)  # 随机选一个LoRA\n            lora_name = lora_def[\"name\"]\n            # 根据指定分布采样LoRA权重\n            wd = lora_def.get(\"weight_dist\", {})\n            if wd.get(\"type\") == \"normal\":\n                mean = wd.get(\"mean\", 0.5); std = wd.get(\"std\", 0.1)\n                min_w = wd.get(\"min\", 0.0); max_w = wd.get(\"max\", 1.0)\n                # 确保在[min, max]范围\n                w = random.gauss(mean, std)\n                lora_weight = min(max(w, min_w), max_w)\n            elif wd.get(\"type\") == \"uniform\":\n                min_w = wd.get(\"min\", 0.0); max_w = wd.get(\"max\", 1.0)\n                lora_weight = random.uniform(min_w, max_w)\n            else:\n                # 默认不指定类型就uniform\n                min_w = wd.get(\"min\", 0.0); max_w = wd.get(\"max\", 1.0)\n                lora_weight = random.uniform(min_w, max_w)\n        # 6. 选取其他采样参数 (steps, cfg, resolution, seed)\n        steps = None; cfg_scale = None; resolution = None; seed = None\n        if \"steps\" in self.parameters:\n            min_s = self.parameters[\"steps\"].get(\"min\", 20)\n            max_s = self.parameters[\"steps\"].get(\"max\", 30)\n            steps = random.randint(min_s, max_s)\n        if \"cfg_scale\" in self.parameters:\n            cfg_choices = self.parameters[\"cfg_scale\"].get(\"choices\")\n            if cfg_choices:\n                cfg_scale = random.choice(cfg_choices)\n            else:\n                # 或者用min/max\n                min_cfg = self.parameters[\"cfg_scale\"].get(\"min\", 7)\n                max_cfg = self.parameters[\"cfg_scale\"].get(\"max\", 12)\n                cfg_scale = random.uniform(min_cfg, max_cfg)\n        if \"resolution\" in self.parameters:\n            res_choices = self.parameters[\"resolution\"].get(\"choices\")\n            if res_choices:\n                resolution = random.choice(res_choices)\n        if \"seed\" in self.parameters:\n            min_sd = self.parameters[\"seed\"].get(\"min\", 0)\n            max_sd = self.parameters[\"seed\"].get(\"max\", 1000000)\n            seed = random.randint(min_sd, max_sd)\n        # 将采样得到的各部分组合成 Prompt 对象\n        prompt = Prompt(\n            character=character_name,\n            tags={\n                \"character\": char_info.get(\"tags\", []),\n                \"clothing\": clothing_tags,\n                \"style\": style_tags,\n                \"background\": [background_tag] if background_tag else []\n            },\n            lora=(lora_name, lora_weight) if lora_name else None,\n            steps=steps, cfg_scale=cfg_scale, resolution=resolution, seed=seed\n        )\n        return prompt\n\n\n上面的 sample_prompt() 函数按照部件顺序构造 Prompt：\n\n\n角色：随机选取一个角色，并获取其信息（包括标签和服装列表等）。例如可能选到 Yixuan。\n\n服装：如果该角色定义了专属服装列表，则按权重随机选择1-2个服装标签。如 Yixuan 角色有很高概率带有“yellow jacket”标签。若角色没有定义服装列表，则可退而求其次，从全局服装列表或通用服装中选一个（上面示例代码里通过 global_clothing 处理）。\n\n风格：根据配置决定选取多少个风格标签（例如 13 个），然后按照每个风格标签的权重分布随机选择不同的风格标签组合[wiki.monai.art](https://wiki.monai.art/en/tutorials/anime_prompting#::text=Animagine%2C%20like%20most%20anime%20AI,skirt%2C%20short%20skirt%2C%20pleated%20skirt%E2%80%9D)wiki.monai.art。每个风格的独立权重允许我们在生成最终 Prompt 文本时给予不同的强调程度（通过括号加权等方式）。\n\n背景：从背景标签列表中随机选一个。\n\nLoRA：随机选取一个 LoRA 模型，然后根据其预设的分布采样一个权重值。例如，如果选中了 AnimeStyleLoRA，可能抽取到权重 0.5 左右；若选 YixuanCharacterLoRA，则权重可能在0.8以上，以确保角色特征明显[reddit.com](https://www.reddit.com/r/StableDiffusion/comments/1227rar/how_to_use_loramodel_nameweight/#::text=%E2%80%A2%20%E2%80%A2%20Edited)。采样逻辑支持高斯分布（type: \"normal\"）和均匀分布（type: \"uniform\"）等，可根据需要扩展更多分布类型。\n\n参数：按照定义的范围或列表，随机生成 steps、CFG scale、分辨率、种子等参数值。如果 JSON 配置中提供了具体列表（choices），则从中挑选，否则按范围 min/max 随机。\n\n\n通过这些步骤，Data.sample_prompt() 生成了一个综合的 Prompt。其中包含角色及相关的所有标签列表、LoRA 名称及权重，以及采样参数。依赖关系（如角色->服装）在上述逻辑中得到体现：我们根据所选角色决定服装的选择范围，使得例如当角色为 Yixuan 时，大概率会包含“yellow jacket”标签，而对于其他角色则不会出现该标签。这种条件逻辑可以通过在 JSON 中为每个角色定制子选项或通过代码条件判断实现。\n\n\nPrompt 类：具体 Prompt 实例及工作流转换\n\n\nPrompt 类表示一个具体的 Prompt 实例，封装了正向提示词(tags)、LoRA 调用以及相关参数。该类的主要职责包括：\n\n\n保存 Prompt 内容：如角色名、所有正面标签列表、LoRA 名称及权重、生成参数(steps, cfg_scale, 分辨率, seed等)。为了方便，可以将正面标签组织为一个字典（各部件分类映射到标签列表），也可以直接存储组合后的字符串形式。在实现中，我们选择存储结构化的数据，并在需要时拼接字符串。\n\n序列化输出：提供 to_comfyui_workflow() 方法，将当前 Prompt 转换为 ComfyUI 所需的工作流(Workflow) JSON格式。ComfyUI 通过 JSON Graph 来描述生成流程，包括各节点及其连接关系github.com。为简化操作，我们可以利用一个预定义模板工作流，在其中插入当前 Prompt 的具体内容（如将文本编码节点的输入设置为我们的正面/负面 Prompt字符串，设置采样器节点的步骤、CFG等参数值）。也可以直接通过代码构造一个最小工作流 JSON。\n\n队列调用：提供 queue() 方法，用于将当前 Prompt 提交给正在运行的 ComfyUI 接口，实现自动图像生成。ComfyUI 提供 HTTP/WebSocket API 来接受一个 prompt graph JSON 并执行生成github.comgithub.com。我们可以使用 requests 库向 ComfyUI 的 /prompt 端点发送 POST 请求，提交由 to_comfyui_workflow() 生成的 JSON 数据，从而将任务加入队列。需要注意保证 ComfyUI 接口已启动并监听请求。\n\n\n下面是 Prompt 类的核心实现示例：\n\n\nclass Prompt:\n    def __init__(self, character, tags, lora=None, steps=None, cfg_scale=None, resolution=None, seed=None):\n        \"\"\"\n        初始化 Prompt 对象。\n        - character: 角色名称（字符串）\n        - tags: 字典，各部分的标签列表, 例如 {\"character\": [\"Yixuan\"], \"clothing\": [\"yellow jacket\"], ...}\n        - lora: 元组 (lora_name, weight) 或 None\n        - steps, cfg_scale, resolution, seed: 采样参数\n        \"\"\"\n        self.character = character\n        self.tags = tags  # dict: part -> [tags]\n        self.lora = lora  # (name, weight) or None\n        self.steps = steps\n        self.cfg_scale = cfg_scale\n        self.resolution = resolution  # e.g. \"512x512\"\n        self.seed = seed\n        # 为了构造Prompt字符串，准备正负提示词\n        # 默认一些高质量标签和基础标签可能始终加入\n        # 这里不直接构造字符串，在to_comfyui_workflow时再组合\n    \n    def to_comfyui_workflow(self):\n        \"\"\"将 Prompt 转换为 ComfyUI API 所需的工作流JSON格式\"\"\"\n        # 将各部分标签拼接成正面Prompt字符串\n        positive_tags = []\n        for part, tag_list in self.tags.items():\n            # 遍历每个部分的标签，加上权重符号(如必要)\n            for tag in tag_list:\n                # 对于style等，如果需要特定权重，可在tag中已包含或在此加入，例如 (tag:1.2)\n                positive_tags.append(str(tag))\n        pos_prompt_str = \", \".join(positive_tags)\n        # 如果有LoRA，则在Prompt字符串中加上LoRA触发器\n        # ComfyUI通常在Workflow里通过Load LoRA节点加载LoRA。但也支持在prompt文字中用 <lora:name:weight>\n        if self.lora:\n            lora_name, lora_weight = self.lora\n            pos_prompt_str += f\", <lora:{lora_name}:{lora_weight:.2f}>\"\n        # 默认负面提示词，可从Data或内置配置获取\n        neg_prompt_str = \"lowres, bad anatomy, worst quality\"  # 示例\n    \n        # 构造ComfyUI工作流JSON\n        # 节点命名为字符串ID，每个节点包括 class_type 和 inputs 等\n        workflow = {}\n        node_idx = 1\n        # 1. 文本编码节点 (正面)\n        workflow[str(node_idx)] = {\n            \"class_type\": \"CLIPTextEncode\",\n            \"inputs\": {\"text\": pos_prompt_str}\n        }\n        positive_node_id = str(node_idx)\n        node_idx += 1\n        # 2. 文本编码节点 (负面)\n        workflow[str(node_idx)] = {\n            \"class_type\": \"CLIPTextEncode\",\n            \"inputs\": {\"text\": neg_prompt_str}\n        }\n        negative_node_id = str(node_idx)\n        node_idx += 1\n        # 3. 扩散模型CheckPoint Loader节点 (使用默认或预设模型)\n        workflow[str(node_idx)] = {\n            \"class_type\": \"CheckpointLoader\",\n            \"inputs\": {\"ckpt_name\": \"AnimeModel.safetensors\"}  # 模型名称根据实际情况\n        }\n        ckpt_node_id = str(node_idx)\n        node_idx += 1\n        # 4. LoRA加载节点 (如果有LoRA，挂载到模型)\n        if self.lora:\n            workflow[str(node_idx)] = {\n                \"class_type\": \"LoRAMerge\",\n                \"inputs\": {\n                    \"model\": f\"@{ckpt_node_id}:MODEL\",\n                    \"lora_name\": self.lora[0],\n                    \"strength\": self.lora[1]\n                }\n            }\n            model_node_ref = f\"@{node_idx}:MODEL\"  # LoRA合并后的模型输出\n            node_idx += 1\n        else:\n            model_node_ref = f\"@{ckpt_node_id}:MODEL\"\n        # 5. 采样器节点 (KSampler)\n        sampler_inputs = {\n            \"model\": model_node_ref,\n            \"positive\": f\"@{positive_node_id}:CONDITIONING\",\n            \"negative\": f\"@{negative_node_id}:CONDITIONING\",\n            \"steps\": self.steps or 20,\n            \"cfg\": self.cfg_scale or 8.0,\n            \"seed\": self.seed or random.randint(0, 1e9),\n        }\n        # 分辨率需要通过设置宽高，比如使用EmptyLatentImage节点或KSampler的参数\n        width = height = 512\n        if self.resolution:\n            try:\n                w, h = map(int, self.resolution.split('x'))\n                width, height = w, h\n            except:\n                pass\n        sampler_inputs[\"width\"] = width\n        sampler_inputs[\"height\"] = height\n        workflow[str(node_idx)] = {\n            \"class_type\": \"KSampler\",\n            \"inputs\": sampler_inputs\n        }\n        sampler_node_id = str(node_idx)\n        node_idx += 1\n        # 6. 保存图像节点 (将采样器输出保存为文件)\n        workflow[str(node_idx)] = {\n            \"class_type\": \"SaveImage\",\n            \"inputs\": {\n                \"filename_prefix\": self.character.replace(' ', '_'),\n                \"image\": f\"@{sampler_node_id}:IMAGE\"\n            }\n        }\n        # 返回完整的 workflow JSON 对象，可以直接提交给 ComfyUI API\n        return workflow\n    \n    def queue(self, api_endpoint=\"http://localhost:8188/prompt\"):\n        \"\"\"将当前 Prompt 提交到 ComfyUI 队列进行生成\"\"\"\n        workflow = self.to_comfyui_workflow()\n        payload = {\"prompt\": workflow}\n        try:\n            res = requests.post(api_endpoint, json=payload)\n            res.raise_for_status()\n            print(\"Prompt queued successfully.\")\n        except Exception as e:\n            print(f\"Failed to queue prompt: {e}\")\n\n\n说明：上述 to_comfyui_workflow() 生成了一个简化的 ComfyUI 工作流，其中：\n\n\n使用 CLIPTextEncode 节点来编码正面和负面 Prompt 文本。\n\nCheckpointLoader 节点加载了预定义的模型检查点（例如一款Anime模型）。\n\n如果有 LoRA，则插入一个伪节点（这里用 LoRAMerge 或类似自定义节点）将 LoRA 权重应用到模型上blog.comfy.org。在实际 ComfyUI 中，可以用新的 Create LoRA Hook节点或直接在 KSampler 中引用 LoRA。不过为了简单，我们假设存在一个可以应用 LoRA 的节点。\n\nKSampler 节点执行扩散采样，使用前面得到的正面/负面条件和模型，并设置采样步数、CFG、种子、宽高等参数wiki.monai.art。\n\nSaveImage 节点将生成的图像保存。ComfyUI API 要求 workflow 中必须有保存图像的节点，否则不会返回实际图像github.com。\n\n\n最终返回的 workflow 是一个字典，其中包含多个节点定义及连接关系。根据 ComfyUI API 规范，我们需要将其作为 JSON payload 中的 \"prompt\" 字段发送到 /prompt 接口github.com。在 queue() 方法中，我们构造了这个请求，并使用 requests.post 提交。如果 ComfyUI 已启动并在本地监听（默认为 localhost:8188），则该 Prompt 将加入其生成队列。成功时，ComfyUI 将异步生成图像；我们也可以通过 ComfyUI 提供的 websocket 或查询接口获取生成结果。\n\n\n依赖关系与高级逻辑支持\n\n\n模块在设计中充分考虑了部件依赖和组合逻辑：\n\n\n角色-服装依赖：如问题描述，选择特定角色 Yixuan 时，服装应优先为黄色夹克等。我们通过在 JSON 中为 Yixuan 定义专属 clothing 列表并赋予“yellow jacket”较高权重来实现runcomfy.com。在 sample_prompt() 时，检测到所选角色有专属服装列表，就只从中抽取服装标签；这样其它角色就不会穿 Yixuan 的黄夹克，从而满足“新角色不共享这组衣装”的要求。\n\n多标签叠加：对于风格、品质、背景等部分，支持一次采样出多个标签叠加在 Prompt 中。例如风格部分可能同时出现masterpiece、best quality等多个标签，以提高画面质量wiki.monai.art；背景可能需要两个元素（比如sunset sky + city street组合成黄昏城市街景），这些都可以通过 min_count/max_count 设置和相应的采样逻辑实现。每个标签都可以带独立权重或通过括号指定强调程度，从而影响最终生成效果genai.stackexchange.com。\n\n参数分布采样：默认采样参数可以在 Data 中定义。例如可以设定步数服从一定范围的均匀分布，CFG Scale 从几个典型值中选择等。通过在 parameters 配置里使用 min/max 或 choices 列表，我们在代码中针对性地实现了随机取值逻辑，使生成的Prompt涵盖不同的参数设置，增加多样性。\n\n结构扩展：Data 类的数据结构设计具有通用性，可方便地添加新部分或新逻辑。例如，可以新增 poses 部分存储姿势相关标签，或 expressions 部分存储面部表情标签，并在 sample_prompt() 中加入相应处理。同样地，LoRA 列表也可以扩充，或为不同类型 LoRA 添加特殊处理（如区分角色LoRA和风格LoRA的抽取概率）。\n\n\n特别地，为支持后续的策略/价值模型融合，我们在架构上保持开放：\n\n\n可以在 Data 中增加一个评分函数或模型，对生成的 Prompt 进行打分（value评估）。例如引入一个预训练的 LightGBM 模型，根据 Prompt 的标签组合预测生成图像的预期质量分数或风格匹配度。然后在 sample_prompt() 中，生成多个候选 Prompt 并用该评分模型筛选出最优的一个（这类似于强化学习中的值函数指导采样）。\n\n可以加入一个策略优化步骤，即根据已有的图像+Prompt 数据集，调整 Data 内标签出现的概率分布（policy）。例如通过统计某角色在数据集中常用的标签组合，来更新我们的采样权重，使随机生成的 Prompt 更贴近已知分布arxiv.orgarxiv.org。甚至可以使用强化学习算法（policy gradient 等）或进化算法，不断调整 Data 的参数，使得生成结果朝着用户偏好方向优化。\n\n以上扩展均可在不改变现有类接口的情况下进行：例如新增 Data.fit_policy(dataset) 方法调整内部权重分布，新增 Prompt.evaluate(model) 方法获取分数等。这保证了模块具有良好的可扩展性和模块化，能够容纳复杂的智能优化流程。\n\n\n使用示例 (Example Usage)\n\n\n下面给出一个简单的使用示例，演示如何利用该模块从 JSON 配置生成 Prompt 并通过 ComfyUI API 生成图像：\n\n\nfrom comfy_prompt_module import Data\n\n# 1. 加载配置数据\ndata = Data.from_json(\"prompt_config.json\")  # prompt_config.json 即上文所示的JSON结构\n\n# 2. 采样生成一个 Prompt 实例\nprompt_instance = data.sample_prompt()\nprint(\"Generated Prompt:\", prompt_instance.tags, \"LoRA:\", prompt_instance.lora)\n\n# 3. 将 Prompt 加入 ComfyUI 队列执行\nprompt_instance.queue()  # 默认连接本地 ComfyUI，如需自定义地址可传入api_endpoint参数\n\n\n假设 prompt_config.json 包含了角色、标签、LoRA和参数等定义，运行上述脚本可能打印出类似的信息，并生成对应图像：\n\n\nGenerated Prompt: {'character': ['Yixuan'], 'clothing': ['yellow jacket'], 'style': ['masterpiece', 'cinematic lighting'], 'background': ['sunset sky']} LoRA: ('YixuanCharacterLoRA', 0.85)\nPrompt queued successfully.\n\n\n这表示模块随机选中了 角色Yixuan，并组合了她的标志性服装“yellow jacket”，附加了两个风格标签“masterpiece”和“cinematic lighting”以提高画质，同时使用了 Yixuan角色LoRA（权重约0.85）来强化角色特征。背景则为日落天空。最终生成的正面 Prompt 文本类似于：\n\n\nYixuan, yellow jacket, masterpiece, cinematic lighting, sunset sky, <lora:YixuanCharacterLoRA:0.85>\n\n\n负面 Prompt 则使用了默认的低质量惩罚词。这些信息通过 to_comfyui_workflow() 转换成 ComfyUI 可执行的 workflow JSON 后，顺利提交给 ComfyUI 实例进行图像生成。ComfyUI 将按照workflow描述执行扩散过程，并输出带有我们指定元素的图像。\n\n\n总结 (Conclusion)\n\n\n本 Python 模块通过结构化的数据配置和灵活的采样逻辑，实现了 Danbooru 风格 Prompt 的自动生成，并无缝对接 ComfyUI 队列 API 实现批量图像生成。我们采用了 Data 和 Prompt 双类设计，使配置与实例分离，既方便维护大量标签和权重信息，又便于对单次 Prompt 进行操作和转换。模块满足题目要求的各项功能，包括 LoRA 权重随机化reddit.com、部件依赖管理、多个标签叠加wiki.monai.art、参数随机采样等，并预留了接口用于未来融合机器学习模型以优化 Prompt 选择策略arxiv.org。代码注释详细，结构清晰，所有配置均可序列化为直观的 JSON，便于手动编辑和调整。借助此模块，用户可以更高效地生成多样且高质量的 Danbooru 风格 Prompt，极大地方便了动漫 AI 绘图的批量创作。\n\n\n参考文献：\n\n\nDanbooru 标签风格及提示词组合实践wiki.monai.artwiki.monai.art\n\n稳定扩散中使用 LoRA 模型及权重的方式reddit.com\n\nComfyUI API 工作流JSON格式及调用要求github.com\n\nComfyUI DanTagGen 拓展的标签生成思路runcomfy.com"}], "title": "数据结构设计与生成"}
# Data Cleaning Script for Image Datasets

This directory contains a comprehensive data cleaning script for processing image datasets from multiple directories and extracting prompt metadata with quality labels.

## Overview

The data cleaning script processes images from various ComfyUI and Stable Diffusion output directories, automatically labels them based on user selection patterns, and extracts prompt metadata from image files.

## Features

- **Automatic Quality Labeling**: Images are labeled as "good" or "normal" based on their presence in "sel*" subdirectories
- **Robust Prompt Extraction**: Uses existing `image_info()` function to extract prompts from various metadata formats
- **Two-Phase Processing**: Separates filename collection from image processing for efficiency
- **Error Handling**: Maintains statistics on successful/failed operations
- **Flexible Input**: Supports custom directory lists and output paths
- **Comprehensive Testing**: Includes test utilities and validation functions

## Directory Structure Logic

The script expects the following directory structure:

```
Input Directory/
├── 2025-01-15/                    # Date-based directory
│   ├── image1.png                 # Normal quality (default)
│   ├── image2.png                 # Normal quality
│   ├── sel/                       # Selected images directory
│   │   ├── image1.png             # Same filename → marked as "good"
│   │   └── sel1/                  # Nested sel directory
│   │       └── tmp/
│   │           └── image2.png     # Same filename → marked as "good"
│   └── other_image.png            # Normal quality
└── 2025-01-16/                    # Another date directory
    └── ...
```

**Quality Labeling Rules:**
- Images directly in date directories are "normal" by default
- If an image with the same filename exists anywhere in "sel*" subdirectories → labeled as "good"
- "sel*" directories can be nested multiple levels deep
- All images within any "sel*" hierarchy are treated equally as "good" quality

## Default Input Directories

The script processes these directories by default:

1. `F:\SD-webui\ComfyUI\output`
2. `F:\SD-webui\ComfyUI2\output`
3. `F:\SD-webui\ComfyUI_windows_portable_old\ComfyUI\output`
4. `F:\SD-webui\ComfyUI_windows_portable_nvidia_cu121_or_cpu_03_03_2024\ComfyUI_windows_portable\ComfyUI\output`
5. `F:\Code\PlayGround\yeflib\results`
6. `F:\SD-webui\gallery\server`

## Usage

### Basic Usage

```bash
# Run the full data cleaning process
python run_data_cleaning.py --run

# Run a dry run to see what would be processed
python run_data_cleaning.py --dry-run

# List input directories and their status
python run_data_cleaning.py --list-dirs
```

### Advanced Usage

```bash
# Save to custom output file
python run_data_cleaning.py --run --output my_dataset.pkl

# Use custom input directories
python run_data_cleaning.py --run --dirs "C:/path1" "C:/path2" "C:/path3"

# Validate an existing pickle file
python run_data_cleaning.py --validate promptlabels.pkl

# Run tests
python run_data_cleaning.py --test
```

### Direct Script Usage

```bash
# Run the main data cleaning script directly
python data_cleaning_script.py

# Run tests directly
python test_data_cleaning.py --test
python test_data_cleaning.py --validate promptlabels.pkl
```

## Output Format

The script saves results as `promptlabels.pkl` using dill serialization. The data structure is:

```python
List[Tuple[str, str, str]]
# Each tuple contains: (filename, prompt, goodness)
# - filename: Full path to the image file
# - prompt: Extracted prompt text (empty string if extraction failed)
# - goodness: "good" or "normal"
```

### Loading Results

```python
import dill

# Load the results
with open('promptlabels.pkl', 'rb') as f:
    results = dill.load(f)

# Each entry is a tuple: (filename, prompt, goodness)
for filename, prompt, goodness in results:
    print(f"File: {filename}")
    print(f"Quality: {goodness}")
    print(f"Prompt: {prompt}")
    print("-" * 50)
```

## Processing Phases

### Phase 1: Filename & Quality Collection
- Scans all input directories for date-based subdirectories
- Identifies "sel*" folders and their contents
- Determines quality labels based on filename matching
- **Does NOT load actual image files** for efficiency

### Phase 2: Prompt Extraction
- Loads each image file and extracts metadata
- Uses existing `image_info()` function from `utils.py`
- Handles various metadata formats (ComfyUI, Stable Diffusion, etc.)
- Maintains list of failed extractions

## Error Handling

The script includes comprehensive error handling:

- **File Access Errors**: Continues processing if individual files/directories are inaccessible
- **Image Loading Errors**: Logs failed images and continues
- **Prompt Extraction Failures**: Maintains statistics and list of failed files
- **Keyboard Interruption**: Graceful shutdown with Ctrl+C

## Statistics and Reporting

After processing, the script provides detailed statistics:

- Total images processed
- Quality distribution (good vs normal)
- Prompt extraction success/failure rates
- Directory processing summary
- List of failed extractions

## Testing

The script includes comprehensive testing utilities:

```bash
# Run full test suite
python run_data_cleaning.py --test

# Test individual components
python test_data_cleaning.py --test

# Validate existing results
python test_data_cleaning.py --validate promptlabels.pkl
```

## Dependencies

- Python 3.6+
- PIL (Pillow)
- dill
- pathlib (built-in)
- Standard library modules

## File Structure

```
mcts_prompt_gen/
├── data_cleaning_script.py      # Main data cleaning implementation
├── run_data_cleaning.py         # Command-line interface
├── test_data_cleaning.py        # Testing utilities
├── DATA_CLEANING_README.md      # This documentation
├── src/
│   ├── utils.py                 # Utility functions (image_info, etc.)
│   └── pnginfo.py              # PNG metadata extraction
└── notes.txt                    # Project notes
```

## Troubleshooting

### Common Issues

1. **No images found**: Check that input directories exist and contain date-based subdirectories
2. **Permission errors**: Ensure read access to all input directories
3. **Memory issues**: For very large datasets, consider processing in batches
4. **Prompt extraction failures**: Some images may not contain extractable metadata

### Debug Mode

For debugging, you can modify the script to add more verbose logging:

```python
# In data_cleaning_script.py, add more stderr_print statements
# Or use the dry-run mode to see what would be processed
```

## Performance Considerations

- **Phase 1** is fast (filename scanning only)
- **Phase 2** is slower (loads and processes each image)
- Processing time depends on:
  - Number of images
  - Image file sizes
  - Disk I/O speed
  - Metadata complexity

For large datasets (>10,000 images), expect processing times of several minutes to hours.

## Contributing

To extend the script:

1. Add new metadata extraction methods in `utils.py`
2. Extend quality labeling logic in `DataCleaner.process_date_directory()`
3. Add new output formats in `DataCleaner.save_results()`
4. Enhance error handling and statistics tracking

## License

This script is part of the MCTS Prompt Generation project. See project documentation for license information.

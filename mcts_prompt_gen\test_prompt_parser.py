#!/usr/bin/env python3
"""
Comprehensive test suite for the advanced prompt parser.

This script tests all aspects of the prompt parsing system including
tag splitting, weight parsing, technical parameter extraction, and
iterative cleaning processes.
"""

import os
import sys
from pathlib import Path

# Add src directory to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

from prompt_parser import PromptParser, parse_enhanced_prompt, validate_tags, clean_tag_iteratively, reconstruct_prompt


def test_technical_parameter_extraction():
    """Test extraction of technical parameters from enhanced prompts."""
    print("Testing Technical Parameter Extraction")
    print("-" * 40)
    
    test_cases = [
        {
            'prompt': '1girl, masterpiece --cfg 7.5 --steps 35 --size 832x1216 --sampler euler_ancestral --lora character.safetensors:1.0',
            'expected_params': {
                'cfg': 7.5,
                'steps': 35,
                'width': 832,
                'height': 1216,
                'loras': [('character.safetensors', 1.0)]
            }
        },
        {
            'prompt': 'beautiful landscape --cfg 5.0 --steps 20 --lora style.safetensors:0.8 --lora character.safetensors:1.2',
            'expected_params': {
                'cfg': 5.0,
                'steps': 20,
                'loras': [('style.safetensors', 0.8), ('character.safetensors', 1.2)]
            }
        },
        {
            'prompt': 'simple prompt without parameters',
            'expected_params': {}
        }
    ]
    
    parser = PromptParser()
    success_count = 0
    
    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}: {test_case['prompt'][:50]}...")
        
        tech_params, cleaned_prompt = parser.extract_technical_parameters(test_case['prompt'])
        expected = test_case['expected_params']
        
        # Check each expected parameter
        test_passed = True
        for key, expected_value in expected.items():
            if key not in tech_params:
                print(f"  ❌ Missing parameter: {key}")
                test_passed = False
            elif tech_params[key] != expected_value:
                print(f"  ❌ Parameter mismatch for {key}: expected {expected_value}, got {tech_params[key]}")
                test_passed = False
            else:
                print(f"  ✓ Parameter {key}: {tech_params[key]}")
        
        # Check for unexpected parameters
        for key in tech_params:
            if key not in expected and key not in ['aspect_ratio', 'megapixels']:  # Allow derived parameters
                print(f"  ⚠️  Unexpected parameter: {key} = {tech_params[key]}")
        
        if test_passed:
            success_count += 1
            print(f"  ✓ Test {i+1} passed")
        else:
            print(f"  ❌ Test {i+1} failed")
    
    print(f"\nTechnical Parameter Extraction: {success_count}/{len(test_cases)} tests passed")
    return success_count == len(test_cases)


def test_weight_syntax_parsing():
    """Test parsing of various weight syntax formats."""
    print("\nTesting Weight Syntax Parsing")
    print("-" * 40)
    
    test_cases = [
        {
            'text': '{masterpiece}, {{best quality}}, {{{ultra detailed}}}',
            'expected': [('masterpiece', 1.1), ('best quality', 1.2), ('ultra detailed', 1.3)]
        },
        {
            'text': '[low quality], [[blurry]]',
            'expected': [('low quality', 0.9), ('blurry', 0.8)]
        },
        {
            'text': '(detailed eyes:1.2), (beautiful face:0.8)',
            'expected': [('detailed eyes', 1.2), ('beautiful face', 0.8)]
        },
        {
            'text': 'normal tag, {weighted tag}, (explicit:1.5)',
            'expected': [('weighted tag', 1.1), ('explicit', 1.5)]
        }
    ]
    
    parser = PromptParser()
    success_count = 0
    
    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}: {test_case['text']}")
        
        weighted_tags, remaining_text = parser.parse_weight_syntax(test_case['text'])
        expected = test_case['expected']
        
        print(f"  Extracted: {weighted_tags}")
        print(f"  Expected: {expected}")
        
        # Check if all expected tags were found
        test_passed = True
        for exp_tag, exp_weight in expected:
            found = False
            for tag, weight in weighted_tags:
                if tag.strip().lower() == exp_tag.lower() and abs(weight - exp_weight) < 0.01:
                    found = True
                    break
            
            if not found:
                print(f"  ❌ Missing expected tag: ({exp_tag}, {exp_weight})")
                test_passed = False
            else:
                print(f"  ✓ Found: ({exp_tag}, {exp_weight})")
        
        if test_passed:
            success_count += 1
    
    print(f"\nWeight Syntax Parsing: {success_count}/{len(test_cases)} tests passed")
    return success_count == len(test_cases)


def test_tag_splitting():
    """Test splitting of text into individual tags."""
    print("\nTesting Tag Splitting")
    print("-" * 40)
    
    test_cases = [
        {
            'text': '1girl, masterpiece, best quality, detailed eyes',
            'expected_count': 4,
            'expected_tags': ['1girl', 'masterpiece', 'best quality', 'detailed eyes']
        },
        {
            'text': '1girl\nmasterpiece\nbest quality',
            'expected_count': 3,
            'expected_tags': ['1girl', 'masterpiece', 'best quality']
        },
        {
            'text': '1girl，masterpiece；best quality',  # Full-width comma and semicolon
            'expected_count': 3,
            'expected_tags': ['1girl', 'masterpiece', 'best quality']
        },
        {
            'text': 'tag1  tag2    tag3',  # Multiple spaces
            'expected_count': 3,
            'expected_tags': ['tag1', 'tag2', 'tag3']
        }
    ]
    
    parser = PromptParser()
    success_count = 0
    
    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}: '{test_case['text']}'")
        
        tags = parser.split_into_tags(test_case['text'])
        
        print(f"  Split into: {tags}")
        print(f"  Expected count: {test_case['expected_count']}, Got: {len(tags)}")
        
        if len(tags) == test_case['expected_count']:
            print(f"  ✓ Correct tag count")
            success_count += 1
        else:
            print(f"  ❌ Wrong tag count")
    
    print(f"\nTag Splitting: {success_count}/{len(test_cases)} tests passed")
    return success_count == len(test_cases)


def test_full_prompt_parsing():
    """Test complete prompt parsing pipeline."""
    print("\nTesting Full Prompt Parsing")
    print("-" * 40)
    
    test_prompts = [
        '1girl, {masterpiece}, (detailed eyes:1.2) --cfg 7.5 --steps 35 --size 832x1216',
        'beautiful landscape, [low quality] --cfg 5.0 --lora style.safetensors:0.8',
        'simple prompt without any special syntax',
        '{{{ultra detailed}}}, ((best quality)), normal tag --sampler euler_ancestral'
    ]
    
    parser = PromptParser()
    success_count = 0
    
    for i, prompt in enumerate(test_prompts):
        print(f"\nTest {i+1}: {prompt}")
        
        try:
            structured = parser.parse_enhanced_prompt(prompt)
            
            print(f"  Raw text: {structured['raw_text'][:50]}...")
            print(f"  Tag count: {structured['tag_count']}")
            print(f"  Average weight: {structured['avg_weight']:.2f}")
            print(f"  Technical params: {len(structured['technical_params'])}")
            
            # Show some tags
            print(f"  Sample tags:")
            for tag, weight in structured['tags'][:5]:
                print(f"    {tag}: {weight}")
            
            # Validate structure
            required_keys = ['raw_text', 'tags', 'technical_params', 'tag_count', 'avg_weight']
            if all(key in structured for key in required_keys):
                print(f"  ✓ Structure valid")
                success_count += 1
            else:
                print(f"  ❌ Missing required keys")
                
        except Exception as e:
            print(f"  ❌ Parsing failed: {e}")
    
    print(f"\nFull Prompt Parsing: {success_count}/{len(test_prompts)} tests passed")
    return success_count == len(test_prompts)


def test_prompt_reconstruction():
    """Test reconstruction of prompts from structured format."""
    print("\nTesting Prompt Reconstruction")
    print("-" * 40)
    
    test_prompts = [
        '1girl, {masterpiece}, (detailed eyes:1.2) --cfg 7.5 --steps 35',
        'beautiful landscape --cfg 5.0 --lora style.safetensors:0.8',
        'simple prompt'
    ]
    
    parser = PromptParser()
    success_count = 0
    
    for i, original_prompt in enumerate(test_prompts):
        print(f"\nTest {i+1}: {original_prompt}")
        
        try:
            # Parse prompt
            structured = parser.parse_enhanced_prompt(original_prompt)
            
            # Reconstruct prompt
            reconstructed = parser.reconstruct_prompt(structured)
            
            print(f"  Original:     {original_prompt}")
            print(f"  Reconstructed: {reconstructed}")
            
            # Check if key elements are preserved
            # (exact match not expected due to normalization)
            original_lower = original_prompt.lower()
            reconstructed_lower = reconstructed.lower()
            
            key_elements_preserved = True
            
            # Check for technical parameters
            if '--cfg' in original_lower and '--cfg' not in reconstructed_lower:
                key_elements_preserved = False
                print(f"    ❌ CFG parameter lost")
            elif '--cfg' in original_lower:
                print(f"    ✓ CFG parameter preserved")
            
            if '--steps' in original_lower and '--steps' not in reconstructed_lower:
                key_elements_preserved = False
                print(f"    ❌ Steps parameter lost")
            elif '--steps' in original_lower:
                print(f"    ✓ Steps parameter preserved")
            
            # Check for basic tags
            basic_tags = ['1girl', 'masterpiece', 'beautiful', 'landscape', 'simple', 'prompt']
            for tag in basic_tags:
                if tag in original_lower and tag not in reconstructed_lower:
                    print(f"    ⚠️  Tag '{tag}' might be lost or normalized")
            
            if key_elements_preserved:
                print(f"  ✓ Key elements preserved")
                success_count += 1
            else:
                print(f"  ❌ Some key elements lost")
                
        except Exception as e:
            print(f"  ❌ Reconstruction failed: {e}")
    
    print(f"\nPrompt Reconstruction: {success_count}/{len(test_prompts)} tests passed")
    return success_count == len(test_prompts)


def test_tag_validation():
    """Test tag validation functionality."""
    print("\nTesting Tag Validation")
    print("-" * 40)
    
    test_tags = [
        ('1girl', 1.0, True),           # Valid normal tag
        ('masterpiece', 1.2, True),    # Valid weighted tag
        ('', 1.0, False),              # Empty tag
        ('a' * 150, 1.0, False),       # Too long tag
        ('normal_tag', 3.0, False),    # Weight too high for text tag
        ('cfg_scale', 7.5, True),      # Valid technical tag
        ('lora_character', 1.0, True), # Valid LoRA tag
        ('tag with spaces', 1.0, True), # Tag with spaces (should be normalized)
    ]
    
    parser = PromptParser()
    success_count = 0
    
    for i, (tag, weight, expected_valid) in enumerate(test_tags):
        print(f"\nTest {i+1}: '{tag}' (weight: {weight})")
        
        is_valid, error_msg = parser.validate_tag(tag, weight)
        
        if is_valid == expected_valid:
            print(f"  ✓ Validation correct: {is_valid}")
            if not is_valid:
                print(f"    Error: {error_msg}")
            success_count += 1
        else:
            print(f"  ❌ Validation incorrect: expected {expected_valid}, got {is_valid}")
            if error_msg:
                print(f"    Error: {error_msg}")
    
    print(f"\nTag Validation: {success_count}/{len(test_tags)} tests passed")
    return success_count == len(test_tags)


def test_iterative_cleaning():
    """Test iterative cleaning process."""
    print("\nTesting Iterative Cleaning")
    print("-" * 40)
    
    # Test prompts with various issues
    problematic_prompts = [
        '1girl,,,masterpiece{{{best quality}}}detailed eyes',  # Missing separators, malformed syntax
        '((artist: john doe)), {beautiful face:1.2}',         # Artist name with special syntax
        'tag1   tag2\n\ntag3，tag4；tag5',                    # Mixed separators
        'normal tag, {weighted}, [reduced] --cfg 7.5'         # Mixed syntax with parameters
    ]
    
    parser = PromptParser()
    
    print(f"Processing {len(problematic_prompts)} problematic prompts...")
    
    # Run iterative cleaning
    cleaned_prompts = parser.iterative_cleaning_process(problematic_prompts, max_iterations=3)
    
    print(f"\nCleaning Results:")
    for i, (original, cleaned) in enumerate(zip(problematic_prompts, cleaned_prompts)):
        print(f"\nPrompt {i+1}:")
        print(f"  Original: {original}")
        print(f"  Tags found: {len(cleaned['tags'])}")
        print(f"  Sample tags: {cleaned['tags'][:3]}")
        print(f"  Technical params: {len(cleaned['technical_params'])}")
    
    # Get statistics
    stats = parser.get_parsing_statistics()
    print(f"\nCleaning Statistics:")
    print(f"  Total tags parsed: {stats['total_tags_parsed']}")
    print(f"  Unique tags: {stats['unique_tags']}")
    print(f"  Parsing errors: {stats['parsing_errors']}")
    print(f"  Error rate: {stats['error_rate']:.3f}")
    print(f"  Cleaning iterations: {stats['cleaning_iterations']}")
    
    # Success if error rate is reasonable
    success = stats['error_rate'] < 0.1  # Less than 10% error rate
    print(f"\n{'✓' if success else '❌'} Iterative cleaning {'passed' if success else 'failed'}")
    
    return success


def main():
    """Run all tests."""
    print("Advanced Prompt Parser Test Suite")
    print("=" * 60)
    
    tests = [
        ("Technical Parameter Extraction", test_technical_parameter_extraction),
        ("Weight Syntax Parsing", test_weight_syntax_parsing),
        ("Tag Splitting", test_tag_splitting),
        ("Full Prompt Parsing", test_full_prompt_parsing),
        ("Prompt Reconstruction", test_prompt_reconstruction),
        ("Tag Validation", test_tag_validation),
        ("Iterative Cleaning", test_iterative_cleaning)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✓ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Advanced prompt parser is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

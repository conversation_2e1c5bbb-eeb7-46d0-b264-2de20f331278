#!/usr/bin/env python3
"""
Comprehensive Unit Tests for Enhanced Prompt Processor

This test suite validates all aspects of the enhanced prompt processing system:
- Tag weight parsing with all supported syntaxes
- LoRA extraction from ComfyUI workflows
- Technical parameter extraction and conversion
- Data structure transformation and validation
- Error handling and logging
- Parallel processing capabilities

Requirements Validation:
- >98% parsing accuracy across all test cases
- Support for {tag}, [tag], (tag:weight) syntaxes
- Multi-delimiter tag splitting
- Graph-based LoRA extraction validation
- Comprehensive error handling without silent failures

Author: AI Assistant
Date: 2025-06-23
"""

import unittest
import tempfile
import os
import sys
import dill
import json
from unittest.mock import patch, MagicMock
from typing import List, Tuple, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(__file__))

from enhanced_prompt_processor import EnhancedPromptProcessor


class TestEnhancedPromptProcessor(unittest.TestCase):
    """Comprehensive test suite for EnhancedPromptProcessor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = EnhancedPromptProcessor()
        
        # Test data for various parsing scenarios
        self.test_cases = [
            # Basic tags
            ("masterpiece, best quality, 1girl", [("masterpiece", 1.0), ("best_quality", 1.0), ("1girl", 1.0)]),
            
            # Weight syntax variations
            ("{masterpiece}, [low quality], (detailed:1.2)", 
             [("masterpiece", 1.1), ("low_quality", 0.9), ("detailed", 1.2)]),
            
            # Multiple bracket levels
            ("{{very detailed}}, [[blurry]], {{{amazing}}}", 
             [("very_detailed", 1.2), ("blurry", 0.8), ("amazing", 1.3)]),
            
            # LoRA tags
            ("--lora character.safetensors:0.8, --lora style.safetensors:1.0", 
             [("lora_character", 0.8), ("lora_style", 1.0)]),
            
            # Command line parameters
            ("--cfg 7.5, --steps 30, --size 1024x768", 
             [("cfg_value", 7.5), ("sampling_steps", 30.0), ("width", 1024.0), ("height", 768.0)]),
            
            # Mixed format complex example
            ("1girl, {masterpiece}, --lora test.safetensors:0.9, --cfg 7.5, (detailed face:1.3), [noise]", 
             [("1girl", 1.0), ("masterpiece", 1.1), ("lora_test", 0.9), ("cfg_value", 7.5), ("detailed_face", 1.3), ("noise", 0.9)]),
            
            # Multi-delimiter splitting
            ("tag1, tag2; tag3，tag4\ntag5", 
             [("tag1", 1.0), ("tag2", 1.0), ("tag3", 1.0), ("tag4", 1.0), ("tag5", 1.0)]),
            
            # Edge cases
            ("", []),  # Empty prompt
            ("   ,  ,  ", []),  # Only delimiters and whitespace
            ("(invalid:weight)", [("invalid", 1.0)]),  # Invalid weight format
        ]
        
        # Sample dataset entries for testing
        self.sample_entries = [
            ("test1.png", "1girl, masterpiece, {best quality}", "good"),
            ("test2.png", "landscape, --cfg 7.5, --lora nature.safetensors:0.8", "normal"),
            ("test3.png", "(detailed:1.2), [blurry], {{amazing}}", "good"),
        ]
    
    def test_tag_weight_parsing_accuracy(self):
        """Test parsing accuracy for all supported tag weight syntaxes."""
        print("\n" + "="*60)
        print("TESTING TAG WEIGHT PARSING ACCURACY")
        print("="*60)
        
        total_tests = len(self.test_cases)
        passed_tests = 0
        
        for i, (input_prompt, expected_tags) in enumerate(self.test_cases, 1):
            print(f"\nTest Case {i}: {input_prompt}")
            
            # Parse using the processor
            actual_tags = self.processor._parse_prompt_to_tags(input_prompt, "dummy.png")
            
            # Filter out workflow-related tags for this test
            actual_tags = [(tag, weight) for tag, weight in actual_tags 
                          if not tag.startswith(('lora_', 'cfg_', 'sampling_', 'width', 'height'))]
            
            print(f"Expected: {expected_tags}")
            print(f"Actual:   {actual_tags}")
            
            # Check if parsing is correct (allowing for normalization differences)
            if self._compare_tag_lists(actual_tags, expected_tags):
                print("✅ PASSED")
                passed_tests += 1
            else:
                print("❌ FAILED")
        
        accuracy = (passed_tests / total_tests) * 100
        print(f"\n📊 Parsing Accuracy: {accuracy:.1f}% ({passed_tests}/{total_tests})")
        
        # Requirement: >98% parsing accuracy
        self.assertGreaterEqual(accuracy, 98.0, f"Parsing accuracy {accuracy:.1f}% below required 98%")
    
    def _compare_tag_lists(self, actual: List[Tuple[str, float]], expected: List[Tuple[str, float]]) -> bool:
        """Compare two tag lists allowing for normalization differences."""
        if len(actual) != len(expected):
            return False
        
        # Convert to dictionaries for easier comparison
        actual_dict = {tag.lower().replace(' ', '_'): weight for tag, weight in actual}
        expected_dict = {tag.lower().replace(' ', '_'): weight for tag, weight in expected}
        
        # Check each expected tag
        for tag, weight in expected_dict.items():
            if tag not in actual_dict:
                return False
            if abs(actual_dict[tag] - weight) > 0.01:  # Allow small floating point differences
                return False
        
        return True
    
    def test_goodness_score_conversion(self):
        """Test conversion of goodness strings to numeric scores."""
        print("\n" + "="*60)
        print("TESTING GOODNESS SCORE CONVERSION")
        print("="*60)
        
        test_cases = [
            ("good", 1.0),
            ("normal", 0.0),
            ("bad", -1.0),
            ("excellent", 2.0),
            ("poor", -2.0),
            (1.5, 1.5),  # Already numeric
            ("unknown", 0.0),  # Default for unknown values
        ]
        
        for input_goodness, expected_score in test_cases:
            actual_score = self.processor._convert_goodness_to_score(input_goodness)
            print(f"'{input_goodness}' -> {actual_score} (expected: {expected_score})")
            self.assertEqual(actual_score, expected_score)
        
        print("✅ All goodness conversion tests passed")
    
    def test_single_entry_processing(self):
        """Test processing of individual dataset entries."""
        print("\n" + "="*60)
        print("TESTING SINGLE ENTRY PROCESSING")
        print("="*60)
        
        for i, entry in enumerate(self.sample_entries, 1):
            print(f"\nProcessing Entry {i}: {entry[0]}")
            
            # Process the entry
            filename, tags, goodness = self.processor.process_single_entry(entry)
            
            print(f"Filename: {filename}")
            print(f"Tags: {len(tags)} tags extracted")
            print(f"Goodness: {goodness}")
            print(f"Sample tags: {tags[:5]}")
            
            # Validate structure
            self.assertIsInstance(filename, str)
            self.assertIsInstance(tags, list)
            self.assertIsInstance(goodness, (int, float))
            
            # Validate tag structure
            for tag, weight in tags:
                self.assertIsInstance(tag, str)
                self.assertIsInstance(weight, (int, float))
                self.assertGreater(len(tag), 0)
        
        print("✅ All single entry processing tests passed")
    
    def test_tag_deduplication(self):
        """Test tag deduplication and weight combination."""
        print("\n" + "="*60)
        print("TESTING TAG DEDUPLICATION")
        print("="*60)
        
        # Test case with duplicate tags
        duplicate_tags = [
            ("masterpiece", 1.0),
            ("1girl", 1.1),
            ("masterpiece", 1.2),  # Duplicate with different weight
            ("detailed", 1.0),
            ("1girl", 0.9),  # Another duplicate
        ]
        
        deduplicated = self.processor._deduplicate_tags(duplicate_tags)
        
        print(f"Original: {duplicate_tags}")
        print(f"Deduplicated: {deduplicated}")
        
        # Check that duplicates are removed
        tag_names = [tag for tag, weight in deduplicated]
        self.assertEqual(len(tag_names), len(set(tag_names)), "Duplicates not properly removed")
        
        # Check that weights are properly combined (averaged)
        masterpiece_weight = next(weight for tag, weight in deduplicated if tag == "masterpiece")
        expected_masterpiece_weight = (1.0 + 1.2) / 2  # Average of 1.0 and 1.2
        self.assertAlmostEqual(masterpiece_weight, expected_masterpiece_weight, places=2)
        
        print("✅ Tag deduplication test passed")
    
    @patch('enhanced_prompt_processor.Image.open')
    @patch('enhanced_prompt_processor.image_info')
    def test_workflow_parameter_extraction(self, mock_image_info, mock_image_open):
        """Test extraction of ComfyUI workflow parameters."""
        print("\n" + "="*60)
        print("TESTING WORKFLOW PARAMETER EXTRACTION")
        print("="*60)
        
        # Mock workflow data
        mock_workflow = {
            "workflow": json.dumps({
                "1": {
                    "class_type": "KSampler",
                    "inputs": {"cfg": 7.5, "steps": 30}
                },
                "2": {
                    "class_type": "LoraLoader",
                    "inputs": {"text": "<lora:test.safetensors:0.8>"}
                }
            })
        }
        
        mock_image_info.return_value = mock_workflow
        mock_image_open.return_value.__enter__.return_value = MagicMock()
        
        # Test extraction
        workflow_tags = self.processor._extract_workflow_parameters("test.png")
        
        print(f"Extracted workflow tags: {workflow_tags}")
        
        # Validate extracted parameters
        self.assertIsInstance(workflow_tags, list)
        
        # Check for expected parameter types
        tag_names = [tag for tag, weight in workflow_tags]
        
        print("✅ Workflow parameter extraction test completed")
    
    def test_error_handling(self):
        """Test comprehensive error handling."""
        print("\n" + "="*60)
        print("TESTING ERROR HANDLING")
        print("="*60)
        
        # Test with invalid entry format
        invalid_entries = [
            ("incomplete",),  # Missing elements
            (None, None, None),  # None values
            ("", "", ""),  # Empty strings
        ]
        
        for i, invalid_entry in enumerate(invalid_entries, 1):
            print(f"\nTesting invalid entry {i}: {invalid_entry}")
            
            # Should not raise exception, should return error structure
            try:
                result = self.processor.process_single_entry(invalid_entry)
                filename, tags, goodness = result
                
                print(f"Result: {result}")
                
                # Should return valid structure even for invalid input
                self.assertIsInstance(filename, str)
                self.assertIsInstance(tags, list)
                self.assertIsInstance(goodness, (int, float))
                
                print("✅ Error handled gracefully")
                
            except Exception as e:
                self.fail(f"Error handling failed: {e}")
        
        print("✅ All error handling tests passed")
    
    def test_parallel_processing_simulation(self):
        """Test parallel processing capabilities with mock data."""
        print("\n" + "="*60)
        print("TESTING PARALLEL PROCESSING SIMULATION")
        print("="*60)
        
        # Create temporary files for testing
        with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.pkl') as input_file:
            with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.pkl') as output_file:
                try:
                    # Save test data
                    dill.dump(self.sample_entries, input_file)
                    input_file.flush()
                    
                    # Process with single worker to avoid complexity
                    stats = self.processor.process_dataset_parallel(
                        input_file.name, 
                        output_file.name, 
                        max_workers=1
                    )
                    
                    print(f"Processing statistics: {stats}")
                    
                    # Validate statistics
                    self.assertIsInstance(stats, dict)
                    self.assertIn('total_entries', stats)
                    self.assertIn('parsing_accuracy', stats)
                    self.assertGreaterEqual(stats['parsing_accuracy'], 0)
                    
                    # Load and validate output
                    with open(output_file.name, 'rb') as f:
                        processed_data = dill.load(f)
                    
                    self.assertEqual(len(processed_data), len(self.sample_entries))
                    
                    # Validate structure of processed data
                    for filename, tags, goodness in processed_data:
                        self.assertIsInstance(filename, str)
                        self.assertIsInstance(tags, list)
                        self.assertIsInstance(goodness, (int, float))
                    
                    print("✅ Parallel processing simulation passed")
                    
                finally:
                    # Clean up temporary files
                    try:
                        os.unlink(input_file.name)
                        os.unlink(output_file.name)
                    except:
                        pass


def run_comprehensive_tests():
    """Run all tests and generate detailed report."""
    print("🧪 ENHANCED PROMPT PROCESSOR - COMPREHENSIVE TEST SUITE")
    print("="*80)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedPromptProcessor)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    # Generate summary report
    print("\n" + "="*80)
    print("📊 TEST SUMMARY REPORT")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    if not result.failures and not result.errors:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Enhanced Prompt Processor is ready for production use")
        return True
    else:
        print("\n⚠️  Some tests failed. Please review and fix issues before proceeding.")
        return False


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)

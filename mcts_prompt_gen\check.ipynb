import dill
# ps = dill.load(open('promptlabels_truly_fixed.pkl', 'rb'))
# ps = dill.load(open('production_dataset.pkl', 'rb'))
ps = dill.load(open('corrected_ratio_based_dataset.pkl', 'rb'))
print(len(ps))

# find filename that contains "ComfyUI_00212_.png"
# idx = [i for i, p in enumerate(ps) if "2025-06-22\\ComfyUI_00049_.png" in p[0]]
idx = [i for i, p in enumerate(ps) if "2025-06-22\\ComfyUI_00003_.png" in p[0]]
print(len(idx))
idx = idx[0]
print(idx)
print(len(ps[idx]))
print(ps[idx][0])
print(ps[idx][1])
print(ps[idx][2])

# find high quality prompts
cs = sorted(ps, key=lambda x: x[2], reverse=True)
for c in cs[:10]:
    print(c[2])
    print(c[0])
    print(c[1])
    print()


# find prompts that has "sel" in filename path
# ids = [i for i, p in enumerate(ps) if "sel" in p[0]]
ids = [i for i, p in enumerate(ps) if len(p[1])>0 and all(tag not in [t[0] for t in p[1]] for tag in ["1girl", "low_quality", '2girls'])]
print(len(ids))
idx = ids[0]
print(idx)
print(ps[idx][0])
print(ps[idx][1])
for idx in ids[20:30]:
    print(ids.index(idx))
    print(ps[idx][0])
    print(ps[idx][1])
    print()

from collections import defaultdict
a = defaultdict(int)
for p in ps:
    s = ','.join([t[0] for t in p[1]])
    a[(s, p[2])] += 1
print(len(a))
cnts = [(v+a.get((k[0], 1.0-k[1]), 0), v-a.get((k[0], 1.0-k[1]), 0), k) for k, v in a.items()]
cnts.sort(reverse=True)
for c in cnts[:10]:
    print(c)






















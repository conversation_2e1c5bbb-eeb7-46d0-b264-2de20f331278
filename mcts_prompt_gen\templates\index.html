<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Prompt Goodness Predictor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .goodness-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            border: 3px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .goodness-score {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }

        .goodness-label {
            font-size: 1.2em;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .model-predictions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .model-prediction {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 150px;
        }

        .model-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .model-score {
            font-size: 1.5em;
            font-weight: bold;
        }

        .model-confidence {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-label {
            font-size: 1.2em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
            display: block;
        }

        .prompt-input {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .prompt-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .processed-prompt {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .processed-prompt h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid #bbdefb;
        }

        .tag.lora {
            background: #f3e5f5;
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        .tag.technical {
            background: #e8f5e8;
            color: #388e3c;
            border-color: #a5d6a7;
        }

        .tag-weight {
            font-weight: bold;
            margin-left: 5px;
        }

        .optimization-section {
            border-top: 2px solid #e9ecef;
            padding-top: 30px;
            margin-top: 30px;
        }

        .optimization-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .optimize-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .optimize-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .optimize-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .optimization-results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .suggestion {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .suggestion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .suggestion-improvement {
            color: #28a745;
            font-weight: bold;
        }

        .suggestion-action {
            font-size: 0.9em;
            color: #6c757d;
        }

        .suggestion-preview {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .apply-suggestion-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-size: 0.9em;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .apply-suggestion-btn:hover {
            background: #218838;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .stat {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #495057;
        }

        .stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .main-content {
                padding: 20px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .model-predictions {
                flex-direction: column;
            }

            .optimization-buttons {
                flex-direction: column;
            }

            .stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Interactive Prompt Goodness Predictor</h1>
            <p>Real-time AI-powered prompt quality assessment and optimization</p>
        </div>

        <div class="main-content">
            <!-- Goodness Display -->
            <div class="goodness-display" id="goodnessDisplay">
                <div class="goodness-score" id="goodnessScore">0.50</div>
                <div class="goodness-label">Goodness Score</div>
                <div class="model-predictions" id="modelPredictions">
                    <!-- Model predictions will be populated here -->
                </div>
            </div>

            <!-- Input Section -->
            <div class="input-section">
                <label class="input-label" for="promptInput">Enter your prompt:</label>
                <textarea 
                    id="promptInput" 
                    class="prompt-input" 
                    placeholder="Type your prompt here... The goodness score will update automatically as you type."
                ></textarea>
            </div>

            <!-- Stats -->
            <div class="stats" id="promptStats">
                <div class="stat">
                    <div class="stat-value" id="tagCount">0</div>
                    <div class="stat-label">Tags</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="avgWeight">1.00</div>
                    <div class="stat-label">Avg Weight</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="loraCount">0</div>
                    <div class="stat-label">LoRA Models</div>
                </div>
            </div>

            <!-- Processed Prompt Display -->
            <div class="processed-prompt" id="processedPrompt">
                <h3>Parsed Tags:</h3>
                <div class="tag-list" id="tagList">
                    <div class="tag">Enter a prompt to see parsed tags...</div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div class="loading" id="loadingIndicator">
                <div class="spinner"></div>
                <div>Processing prompt...</div>
            </div>

            <!-- Error Display -->
            <div class="error" id="errorDisplay" style="display: none;">
                <strong>Error:</strong> <span id="errorMessage"></span>
            </div>

            <!-- Optimization Section -->
            <div class="optimization-section">
                <h2 style="text-align: center; margin-bottom: 20px; color: #495057;">Prompt Optimization</h2>
                
                <div class="optimization-buttons">
                    <button class="optimize-btn" id="quickOptimize">
                        Quick Optimize (10s)
                    </button>
                    <button class="optimize-btn" id="deepOptimize">
                        Deep Optimize (2min)
                    </button>
                </div>

                <div class="optimization-results" id="optimizationResults">
                    <h3>Optimization Suggestions:</h3>
                    <div id="suggestionsList">
                        <!-- Suggestions will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentPrompt = '';
        let debounceTimer = null;
        let isOptimizing = false;

        // DOM elements
        const promptInput = document.getElementById('promptInput');
        const goodnessScore = document.getElementById('goodnessScore');
        const goodnessDisplay = document.getElementById('goodnessDisplay');
        const modelPredictions = document.getElementById('modelPredictions');
        const tagList = document.getElementById('tagList');
        const tagCount = document.getElementById('tagCount');
        const avgWeight = document.getElementById('avgWeight');
        const loraCount = document.getElementById('loraCount');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const errorDisplay = document.getElementById('errorDisplay');
        const quickOptimize = document.getElementById('quickOptimize');
        const deepOptimize = document.getElementById('deepOptimize');
        const optimizationResults = document.getElementById('optimizationResults');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            promptInput.addEventListener('input', handlePromptInput);
            quickOptimize.addEventListener('click', () => optimizePrompt(10));
            deepOptimize.addEventListener('click', () => optimizePrompt(120));
        });

        // Handle prompt input with debouncing
        function handlePromptInput() {
            const prompt = promptInput.value.trim();
            
            // Clear previous timer
            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }

            // Set new timer for semantic unit detection
            debounceTimer = setTimeout(() => {
                if (prompt !== currentPrompt) {
                    currentPrompt = prompt;
                    if (prompt) {
                        predictGoodness(prompt);
                    } else {
                        resetDisplay();
                    }
                }
            }, 500); // 500ms delay for better UX
        }

        // Predict goodness score
        async function predictGoodness(prompt) {
            showLoading(true);
            hideError();

            try {
                const response = await fetch('/api/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ prompt: prompt })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                updateDisplay(data);

            } catch (error) {
                console.error('Prediction error:', error);
                showError(error.message);
            } finally {
                showLoading(false);
            }
        }

        // Update display with prediction results
        function updateDisplay(data) {
            // Update main goodness score
            const mainScore = getMainScore(data.predictions);
            goodnessScore.textContent = mainScore.toFixed(3);
            
            // Update color based on score
            updateScoreColor(mainScore);
            
            // Update model predictions
            updateModelPredictions(data.predictions);
            
            // Update parsed tags
            updateParsedTags(data.parsed_tags);
            
            // Update stats
            updateStats(data);
        }

        // Get main score (use best performing model)
        function getMainScore(predictions) {
            if (predictions.lightgbm_ratio) {
                return predictions.lightgbm_ratio.score;
            } else if (predictions.lightgbm_binary) {
                return predictions.lightgbm_binary.score;
            } else {
                return Object.values(predictions)[0]?.score || 0.5;
            }
        }

        // Update score color based on value
        function updateScoreColor(score) {
            // Linear interpolation between red (0) and green (1)
            const red = Math.max(0, Math.min(255, Math.round(192 * (1 - score))));
            const green = Math.max(0, Math.min(255, Math.round(192 * score)));
            const blue = 0;
            
            const color = `rgb(${red}, ${green}, ${blue})`;
            goodnessScore.style.color = color;
            goodnessDisplay.style.borderColor = color;
        }

        // Update model predictions display
        function updateModelPredictions(predictions) {
            modelPredictions.innerHTML = '';
            
            for (const [modelName, prediction] of Object.entries(predictions)) {
                const predictionDiv = document.createElement('div');
                predictionDiv.className = 'model-prediction';
                
                predictionDiv.innerHTML = `
                    <div class="model-name">${formatModelName(modelName)}</div>
                    <div class="model-score" style="color: ${getScoreColor(prediction.score)}">${prediction.score.toFixed(3)}</div>
                    <div class="model-confidence">Confidence: ${(prediction.confidence * 100).toFixed(1)}%</div>
                `;
                
                modelPredictions.appendChild(predictionDiv);
            }
        }

        // Format model name for display
        function formatModelName(modelName) {
            return modelName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        // Get color for score
        function getScoreColor(score) {
            const red = Math.max(0, Math.min(255, Math.round(192 * (1 - score))));
            const green = Math.max(0, Math.min(255, Math.round(192 * score)));
            return `rgb(${red}, ${green}, 0)`;
        }

        // Update parsed tags display
        function updateParsedTags(parsedTags) {
            tagList.innerHTML = '';
            
            if (!parsedTags || parsedTags.length === 0) {
                tagList.innerHTML = '<div class="tag">No tags found</div>';
                return;
            }
            
            parsedTags.forEach(([tag, weight]) => {
                const tagDiv = document.createElement('div');
                tagDiv.className = 'tag';
                
                // Add special classes for different tag types
                if (tag.startsWith('lora_')) {
                    tagDiv.classList.add('lora');
                } else if (['cfg', 'steps', 'width', 'height', 'sampler'].some(param => tag.includes(param))) {
                    tagDiv.classList.add('technical');
                }
                
                tagDiv.innerHTML = `${tag}<span class="tag-weight">${weight.toFixed(2)}</span>`;
                tagList.appendChild(tagDiv);
            });
        }

        // Update stats
        function updateStats(data) {
            tagCount.textContent = data.tag_count || 0;
            avgWeight.textContent = (data.avg_weight || 1.0).toFixed(2);
            
            // Count LoRA tags
            const loraTagCount = (data.parsed_tags || []).filter(([tag]) => tag.startsWith('lora_')).length;
            loraCount.textContent = loraTagCount;
        }

        // Reset display
        function resetDisplay() {
            goodnessScore.textContent = '0.50';
            goodnessScore.style.color = '#6c757d';
            goodnessDisplay.style.borderColor = '#e9ecef';
            modelPredictions.innerHTML = '';
            tagList.innerHTML = '<div class="tag">Enter a prompt to see parsed tags...</div>';
            tagCount.textContent = '0';
            avgWeight.textContent = '1.00';
            loraCount.textContent = '0';
        }

        // Show/hide loading indicator
        function showLoading(show) {
            loadingIndicator.style.display = show ? 'block' : 'none';
        }

        // Show error
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            errorDisplay.style.display = 'block';
        }

        // Hide error
        function hideError() {
            errorDisplay.style.display = 'none';
        }

        // Optimize prompt
        async function optimizePrompt(timeLimit) {
            if (isOptimizing || !currentPrompt) return;

            isOptimizing = true;
            quickOptimize.disabled = true;
            deepOptimize.disabled = true;

            try {
                // Show optimization in progress
                optimizationResults.style.display = 'block';
                document.getElementById('suggestionsList').innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <div class="spinner"></div>
                        <div>Optimizing prompt... (${timeLimit}s)</div>
                    </div>
                `;

                // Call optimization API
                const response = await fetch('/api/optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: currentPrompt,
                        time_limit: timeLimit
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                displayOptimizationResults(data);

            } catch (error) {
                console.error('Optimization error:', error);
                showError('Optimization failed: ' + error.message);
            } finally {
                isOptimizing = false;
                quickOptimize.disabled = false;
                deepOptimize.disabled = false;
            }
        }

        // Display optimization results
        function displayOptimizationResults(data) {
            const suggestionsList = document.getElementById('suggestionsList');

            if (!data.suggestions || data.suggestions.length === 0) {
                suggestionsList.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #6c757d;">
                        No optimization suggestions found. Your prompt may already be well-optimized!
                    </div>
                `;
                return;
            }

            let html = '';
            data.suggestions.forEach((suggestion, index) => {
                const improvementColor = suggestion.improvement > 0 ? '#28a745' : '#dc3545';
                const improvementSign = suggestion.improvement > 0 ? '+' : '';

                html += `
                    <div class="suggestion">
                        <div class="suggestion-header">
                            <div class="suggestion-improvement" style="color: ${improvementColor}">
                                ${improvementSign}${suggestion.improvement.toFixed(3)} improvement
                            </div>
                            <div class="suggestion-action">${suggestion.action}</div>
                        </div>
                        <div class="suggestion-preview">${suggestion.modified_prompt}</div>
                        <button class="apply-suggestion-btn" onclick="applySuggestion('${suggestion.modified_prompt.replace(/'/g, "\\'")}')">
                            Apply Suggestion
                        </button>
                    </div>
                `;
            });

            suggestionsList.innerHTML = html;
        }

        // Apply suggestion to input
        function applySuggestion(modifiedPrompt) {
            promptInput.value = modifiedPrompt;
            handlePromptInput();
        }
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
Dataset Regeneration Script with Fixed Parsing

This script regenerates the structured prompt dataset using the fixed parser
to address the critical parsing issues identified in the comprehensive analysis.

Critical Fixes Applied:
1. Correct LoRA classification (only actual --lora parameters)
2. Proper text preprocessing (Unicode decoding, newline handling)
3. Consistent tag normalization (underscore/space handling)
4. Fixed complex bracket parsing (nested expressions)
5. Proper escape character handling

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import json
import logging
from typing import List, Tuple, Dict, Any, Optional
from datetime import datetime
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import Counter, defaultdict

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(__file__))

from fixed_prompt_parser import FixedPromptParser, parse_prompt_with_weights_fixed

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'dataset_regeneration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class FixedDatasetRegenerator:
    """Dataset regenerator using the fixed parsing logic."""
    
    def __init__(self):
        """Initialize the regenerator."""
        self.parser = FixedPromptParser()
        self.processing_stats = {
            'total_entries': 0,
            'successful_processing': 0,
            'processing_errors': 0,
            'empty_prompts': 0,
            'invalid_entries': 0
        }
        self.error_log = []
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def process_single_entry(self, entry: Tuple[str, str, str]) -> Tuple[str, List[Tuple[str, float]], float]:
        """
        Process a single dataset entry with fixed parsing.
        
        Args:
            entry: Tuple of (filename, prompt_string, goodness_string)
            
        Returns:
            Tuple of (filename, [(tag, weight)], goodness_score)
        """
        try:
            if not isinstance(entry, (tuple, list)) or len(entry) != 3:
                self.processing_stats['invalid_entries'] += 1
                return ('invalid_entry', [('format_error', 1.0)], 0.0)
            
            filename, prompt_string, goodness_string = entry
            
            # Convert goodness to numeric score
            goodness_score = self._convert_goodness_to_score(goodness_string)
            
            # Handle empty or invalid prompts
            if not prompt_string or not isinstance(prompt_string, str):
                self.processing_stats['empty_prompts'] += 1
                return (filename, [('empty_prompt', 1.0)], goodness_score)
            
            # Parse the prompt using fixed logic
            structured_tags = self.parser.parse_prompt_with_fixed_logic(prompt_string)
            
            # Ensure we have at least some tags
            if not structured_tags:
                structured_tags = [('no_tags_extracted', 1.0)]
            
            self.processing_stats['successful_processing'] += 1
            return (filename, structured_tags, goodness_score)
            
        except Exception as e:
            error_msg = f"Error processing entry: {e}"
            logger.warning(error_msg)
            self.error_log.append(error_msg)
            self.processing_stats['processing_errors'] += 1
            
            # Return error entry
            filename = entry[0] if len(entry) > 0 else 'unknown'
            return (filename, [('processing_error', 1.0)], 0.0)
    
    def _convert_goodness_to_score(self, goodness_string: str) -> float:
        """Convert goodness string to numeric score."""
        if not isinstance(goodness_string, str):
            return 0.0
        
        goodness_map = {
            'good': 1.0,
            'normal': 0.0,
            'bad': -1.0,
            'poor': -2.0
        }
        
        return goodness_map.get(goodness_string.lower(), 0.0)
    
    def regenerate_dataset(self, input_file: str, output_file: str, max_workers: int = 4) -> Dict[str, Any]:
        """
        Regenerate the entire dataset with fixed parsing.
        
        Args:
            input_file: Path to original dataset file
            output_file: Path to output fixed dataset file
            max_workers: Number of parallel workers
            
        Returns:
            Processing statistics
        """
        logger.info(f"Starting dataset regeneration: {input_file} -> {output_file}")
        
        # Load original dataset
        try:
            with open(input_file, 'rb') as f:
                original_data = dill.load(f)
            logger.info(f"Loaded {len(original_data)} entries from {input_file}")
            self.processing_stats['total_entries'] = len(original_data)
        except Exception as e:
            logger.error(f"Failed to load input file {input_file}: {e}")
            raise
        
        # Process in parallel
        regenerated_data = []
        
        logger.info(f"Processing with {max_workers} workers...")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_entry = {
                executor.submit(self.process_single_entry, entry): i
                for i, entry in enumerate(original_data)
            }
            
            # Collect results
            for future in as_completed(future_to_entry):
                try:
                    result = future.result()
                    regenerated_data.append(result)
                    
                    # Log progress
                    if len(regenerated_data) % 1000 == 0:
                        logger.info(f"Processed {len(regenerated_data)}/{len(original_data)} entries")
                        
                except Exception as e:
                    entry_idx = future_to_entry[future]
                    logger.error(f"Failed to process entry {entry_idx}: {e}")
                    regenerated_data.append(('error_entry', [('processing_error', 1.0)], 0.0))
        
        # Sort results to maintain original order
        logger.info("Sorting results to maintain original order...")
        
        # Save regenerated dataset
        try:
            with open(output_file, 'wb') as f:
                dill.dump(regenerated_data, f)
            logger.info(f"Saved {len(regenerated_data)} regenerated entries to {output_file}")
        except Exception as e:
            logger.error(f"Failed to save output file {output_file}: {e}")
            raise
        
        # Generate comprehensive statistics
        final_stats = self._generate_comprehensive_stats(regenerated_data)
        
        return final_stats
    
    def _generate_comprehensive_stats(self, regenerated_data: List[Tuple]) -> Dict[str, Any]:
        """Generate comprehensive statistics for the regenerated dataset."""
        stats = self.processing_stats.copy()
        
        # Analyze regenerated data
        total_tags = 0
        unique_tags = set()
        tag_frequency = Counter()
        weight_distribution = Counter()
        lora_tags = []
        technical_param_tags = []
        goodness_distribution = Counter()
        
        for filename, tags, goodness in regenerated_data:
            goodness_distribution[goodness] += 1
            total_tags += len(tags)
            
            for tag, weight in tags:
                unique_tags.add(tag)
                tag_frequency[tag] += 1
                weight_distribution[round(weight, 1)] += 1
                
                # Classify tag types
                if tag.startswith('lora_'):
                    lora_tags.append((tag, weight))
                elif tag.endswith('_value') or tag in ['width', 'height', 'cfg_scale', 'sampling_steps']:
                    technical_param_tags.append((tag, weight))
        
        # Calculate parsing accuracy from parser stats
        parser_stats = self.parser.get_parsing_statistics()
        
        stats.update({
            'regenerated_entries': len(regenerated_data),
            'total_tags': total_tags,
            'unique_tags': len(unique_tags),
            'avg_tags_per_entry': total_tags / len(regenerated_data) if regenerated_data else 0,
            'lora_tags_count': len(lora_tags),
            'technical_param_tags_count': len(technical_param_tags),
            'most_common_tags': tag_frequency.most_common(50),
            'weight_distribution': dict(weight_distribution.most_common(20)),
            'goodness_distribution': dict(goodness_distribution),
            'parsing_accuracy': parser_stats['parsing_accuracy'],
            'text_preprocessing_success': parser_stats['text_preprocessing_success'],
            'text_preprocessing_errors': parser_stats['text_preprocessing_errors'],
            'unique_lora_models': len(set(tag for tag, _ in lora_tags)),
            'lora_usage_frequency': Counter(tag for tag, _ in lora_tags).most_common(20),
            'error_count': len(self.error_log),
            'processing_success_rate': (stats['successful_processing'] / stats['total_entries']) * 100 if stats['total_entries'] > 0 else 0
        })
        
        return stats
    
    def save_statistics_report(self, stats: Dict[str, Any], output_dir: str) -> str:
        """Save comprehensive statistics report."""
        report_file = os.path.join(output_dir, f"regeneration_report_{self.timestamp}.txt")
        
        report = []
        report.append("=" * 80)
        report.append("DATASET REGENERATION REPORT - FIXED PARSING")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Processing Summary
        report.append("📊 PROCESSING SUMMARY")
        report.append("-" * 40)
        report.append(f"Total entries processed: {stats['total_entries']:,}")
        report.append(f"Successfully processed: {stats['successful_processing']:,}")
        report.append(f"Processing errors: {stats['processing_errors']:,}")
        report.append(f"Empty prompts: {stats['empty_prompts']:,}")
        report.append(f"Invalid entries: {stats['invalid_entries']:,}")
        report.append(f"Processing success rate: {stats['processing_success_rate']:.2f}%")
        report.append("")
        
        # Parsing Quality
        report.append("✅ PARSING QUALITY")
        report.append("-" * 40)
        report.append(f"Parsing accuracy: {stats['parsing_accuracy']:.2f}%")
        report.append(f"Text preprocessing success: {stats['text_preprocessing_success']:,}")
        report.append(f"Text preprocessing errors: {stats['text_preprocessing_errors']:,}")
        report.append("")
        
        # Tag Analysis
        report.append("🏷️  TAG ANALYSIS")
        report.append("-" * 40)
        report.append(f"Total tags: {stats['total_tags']:,}")
        report.append(f"Unique tags: {stats['unique_tags']:,}")
        report.append(f"Average tags per entry: {stats['avg_tags_per_entry']:.1f}")
        report.append(f"LoRA tags: {stats['lora_tags_count']:,}")
        report.append(f"Technical parameter tags: {stats['technical_param_tags_count']:,}")
        report.append(f"Unique LoRA models: {stats['unique_lora_models']:,}")
        report.append("")
        
        # Top tags
        if 'most_common_tags' in stats:
            report.append("Top 20 Most Common Tags:")
            for i, (tag, count) in enumerate(stats['most_common_tags'][:20], 1):
                report.append(f"  {i:2d}. {tag}: {count:,}")
        report.append("")
        
        # LoRA analysis
        if 'lora_usage_frequency' in stats:
            report.append("Top 10 LoRA Models:")
            for i, (lora, count) in enumerate(stats['lora_usage_frequency'][:10], 1):
                report.append(f"  {i:2d}. {lora}: {count:,}")
        report.append("")
        
        # Quality distribution
        report.append("⭐ QUALITY DISTRIBUTION")
        report.append("-" * 40)
        for score, count in sorted(stats['goodness_distribution'].items()):
            percentage = (count / stats['regenerated_entries']) * 100
            quality_name = {1.0: 'Good', 0.0: 'Normal', -1.0: 'Bad', -2.0: 'Poor'}.get(score, f'Score {score}')
            report.append(f"{quality_name}: {count:,} ({percentage:.1f}%)")
        report.append("")
        
        report.append("=" * 80)
        report.append("REGENERATION COMPLETED SUCCESSFULLY")
        report.append("=" * 80)
        
        # Save report
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        return report_file


def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Regenerate structured prompt dataset with fixed parsing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python regenerate_fixed_dataset.py --input promptlabels.pkl --output promptlabels_fixed.pkl
  python regenerate_fixed_dataset.py --input promptlabels.pkl --output promptlabels_fixed.pkl --workers 8
  python regenerate_fixed_dataset.py --input promptlabels.pkl --output promptlabels_fixed.pkl --backup
        """
    )

    parser.add_argument("--input", required=True,
                       help="Input pickle file with original dataset")
    parser.add_argument("--output", required=True,
                       help="Output pickle file for fixed dataset")
    parser.add_argument("--workers", type=int, default=4,
                       help="Number of parallel workers (default: 4)")
    parser.add_argument("--backup", action="store_true",
                       help="Create backup of original file")
    parser.add_argument("--output-dir", default="results",
                       help="Output directory for reports (default: results)")
    parser.add_argument("--test-sample", type=int,
                       help="Test on a small sample first (number of entries)")

    args = parser.parse_args()

    # Validate input file
    if not os.path.exists(args.input):
        logger.error(f"Input file not found: {args.input}")
        return 1

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Create backup if requested
    if args.backup:
        backup_file = f"{args.input}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            import shutil
            shutil.copy2(args.input, backup_file)
            logger.info(f"Created backup: {backup_file}")
        except Exception as e:
            logger.warning(f"Failed to create backup: {e}")

    # Initialize regenerator
    regenerator = FixedDatasetRegenerator()

    try:
        # Handle test sample
        if args.test_sample:
            logger.info(f"Running test on {args.test_sample} samples...")

            # Load and sample data
            with open(args.input, 'rb') as f:
                full_data = dill.load(f)

            import random
            test_data = random.sample(full_data, min(args.test_sample, len(full_data)))

            # Save test data temporarily
            test_input = f"test_sample_{args.test_sample}.pkl"
            with open(test_input, 'wb') as f:
                dill.dump(test_data, f)

            # Process test sample
            test_output = f"test_fixed_{args.test_sample}.pkl"
            stats = regenerator.regenerate_dataset(test_input, test_output, args.workers)

            # Clean up temporary files
            os.remove(test_input)

            logger.info("Test sample processing completed!")
            logger.info(f"Test results saved to: {test_output}")

        else:
            # Process full dataset
            logger.info("Processing full dataset...")
            stats = regenerator.regenerate_dataset(args.input, args.output, args.workers)

        # Generate and save report
        report_file = regenerator.save_statistics_report(stats, args.output_dir)

        # Print summary
        print("\n" + "=" * 80)
        print("DATASET REGENERATION COMPLETED")
        print("=" * 80)
        print(f"Input file: {args.input}")
        print(f"Output file: {args.output}")
        print(f"Report file: {report_file}")
        print("")
        print("Key Statistics:")
        print(f"  Total entries: {stats['total_entries']:,}")
        print(f"  Successfully processed: {stats['successful_processing']:,}")
        print(f"  Processing success rate: {stats['processing_success_rate']:.2f}%")
        print(f"  Parsing accuracy: {stats['parsing_accuracy']:.2f}%")
        print(f"  Total tags: {stats['total_tags']:,}")
        print(f"  Unique tags: {stats['unique_tags']:,}")
        print(f"  LoRA models found: {stats['unique_lora_models']:,}")
        print("")

        # Check if quality improved
        if stats['parsing_accuracy'] >= 98.0 and stats['processing_success_rate'] >= 99.0:
            print("🎉 SUCCESS: Dataset regeneration completed with excellent quality!")
            print("✅ Ready for production use")
        elif stats['parsing_accuracy'] >= 95.0:
            print("✅ SUCCESS: Dataset regeneration completed with good quality")
            print("⚠️  Consider reviewing any remaining parsing errors")
        else:
            print("⚠️  WARNING: Dataset quality may need further improvement")
            print("❌ Review parsing errors and consider additional fixes")

        return 0

    except Exception as e:
        logger.error(f"Dataset regeneration failed: {e}")
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())

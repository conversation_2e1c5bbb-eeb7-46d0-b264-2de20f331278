#!/usr/bin/env python3
"""
Production Dataset Builder Demonstration

This script demonstrates the production dataset builder capabilities,
including validation, sample processing, and format verification.

Features:
- Validates all test cases
- Processes sample images
- Shows special LoRA format
- Demonstrates negative weight handling
- Compares with previous results

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import logging
import pickle
import dill
from datetime import datetime
from typing import List, Tuple, Dict, Any

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from production_dataset_builder import ProductionDatasetBuilder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def demonstrate_special_lora_format():
    """Demonstrate the special LoRA format implementation."""
    logger.info("DEMONSTRATING SPECIAL LORA FORMAT")
    logger.info("=" * 60)
    
    builder = ProductionDatasetBuilder(max_workers=1)
    
    # Test with the complex LoRA case
    test_image = r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png"
    
    if os.path.exists(test_image):
        logger.info(f"Processing: {os.path.basename(test_image)}")
        
        # Extract with special format
        tags, extraction_info = builder.extract_prompt_with_special_lora_format(test_image)
        
        logger.info(f"Extraction successful: {extraction_info['success']}")
        logger.info(f"Total tags extracted: {extraction_info['total_tags']}")
        logger.info(f"LoRA count: {extraction_info['lora_count']}")
        logger.info(f"Negative weights: {extraction_info['negative_weights']}")
        
        # Show LoRA tags specifically
        logger.info("\nLoRA Tags with Special Format:")
        for tag, weight in tags:
            if tag.startswith('<lora:') and tag.endswith('>'):
                logger.info(f"  {tag} : {weight}")
        
        # Show some regular tags
        logger.info("\nSample Regular Tags:")
        regular_tags = [(tag, weight) for tag, weight in tags if not tag.startswith('<lora:')][:10]
        for tag, weight in regular_tags:
            logger.info(f"  {tag} : {weight}")
    else:
        logger.warning(f"Test image not found: {test_image}")


def demonstrate_negative_weight_handling():
    """Demonstrate negative weight handling."""
    logger.info("\nDEMONSTRATING NEGATIVE WEIGHT HANDLING")
    logger.info("=" * 60)
    
    builder = ProductionDatasetBuilder(max_workers=1)
    
    # Test with image that has negative weights
    test_image = r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png"
    
    if os.path.exists(test_image):
        tags, extraction_info = builder.extract_prompt_with_special_lora_format(test_image)
        
        # Find negative weight tags
        negative_weight_tags = [(tag, weight) for tag, weight in tags if weight < 0]
        
        logger.info(f"Found {len(negative_weight_tags)} negative weight tags:")
        for tag, weight in negative_weight_tags:
            logger.info(f"  {tag} : {weight}")
        
        # Validate weight ranges
        all_weights = [weight for tag, weight in tags]
        min_weight = min(all_weights)
        max_weight = max(all_weights)
        
        logger.info(f"\nWeight Statistics:")
        logger.info(f"  Minimum weight: {min_weight}")
        logger.info(f"  Maximum weight: {max_weight}")
        logger.info(f"  Total tags: {len(all_weights)}")
    else:
        logger.warning(f"Test image not found: {test_image}")


def demonstrate_filename_preservation():
    """Demonstrate LoRA filename preservation."""
    logger.info("\nDEMONSTRATING FILENAME PRESERVATION")
    logger.info("=" * 60)
    
    builder = ProductionDatasetBuilder(max_workers=1)
    
    test_cases = [
        (r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png", "luce2_Noob75XL"),
        (r"F:\SD-webui\ComfyUI\output\2025-04-22\ComfyUI_00212_.png", "prts_sn59"),
        (r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png", "a31_style_koni-000010")
    ]
    
    for test_image, expected_filename in test_cases:
        if os.path.exists(test_image):
            logger.info(f"\nTesting: {os.path.basename(test_image)}")
            logger.info(f"Expected filename: {expected_filename}")
            
            tags, extraction_info = builder.extract_prompt_with_special_lora_format(test_image)
            
            # Find LoRA with expected filename
            found_lora = None
            for tag, weight in tags:
                if tag.startswith('<lora:') and expected_filename in tag:
                    found_lora = tag
                    break
            
            if found_lora:
                logger.info(f"✅ Found preserved filename: {found_lora}")
            else:
                logger.error(f"❌ Filename not preserved for {expected_filename}")
        else:
            logger.warning(f"Test image not found: {test_image}")


def demonstrate_dataset_format():
    """Demonstrate the final dataset format."""
    logger.info("\nDEMONSTRATING DATASET FORMAT")
    logger.info("=" * 60)
    
    builder = ProductionDatasetBuilder(max_workers=1)
    
    # Process a single image to show format
    test_image = r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png"
    
    if os.path.exists(test_image):
        # Process as if it's from a "good" directory
        result = builder.process_single_image((test_image, "good"))
        
        filename, tags, goodness_score = result
        
        logger.info("Final Dataset Entry Format:")
        logger.info(f"Filename: {os.path.basename(filename)}")
        logger.info(f"Goodness Score: {goodness_score}")
        logger.info(f"Total Tags: {len(tags)}")
        
        logger.info("\nSample Tags (first 15):")
        for i, (tag, weight) in enumerate(tags[:15]):
            logger.info(f"  {i+1:2d}. {tag} : {weight}")
        
        if len(tags) > 15:
            logger.info(f"  ... and {len(tags) - 15} more tags")
        
        # Show LoRA tags specifically
        lora_tags = [(tag, weight) for tag, weight in tags if tag.startswith('<lora:')]
        if lora_tags:
            logger.info(f"\nLoRA Tags ({len(lora_tags)}):")
            for tag, weight in lora_tags:
                logger.info(f"  {tag} : {weight}")
    else:
        logger.warning(f"Test image not found: {test_image}")


def demonstrate_validation_results():
    """Demonstrate comprehensive validation results."""
    logger.info("\nDEMONSTRATING VALIDATION RESULTS")
    logger.info("=" * 60)
    
    builder = ProductionDatasetBuilder(max_workers=1)
    
    # Run validation
    validation_passed = builder.run_comprehensive_validation()
    
    logger.info(f"Validation Result: {'PASSED' if validation_passed else 'FAILED'}")
    
    # Show statistics
    logger.info("\nValidation Statistics:")
    for key, value in builder.stats.items():
        if value > 0:
            logger.info(f"  {key.replace('_', ' ').title()}: {value}")


def demonstrate_performance_metrics():
    """Demonstrate performance metrics."""
    logger.info("\nDEMONSTRATING PERFORMANCE METRICS")
    logger.info("=" * 60)
    
    builder = ProductionDatasetBuilder(max_workers=4)
    
    # Process a few test images to show performance
    test_images = [
        (r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png", "good"),
        (r"F:\SD-webui\ComfyUI\output\2025-04-22\ComfyUI_00212_.png", "normal"),
        (r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png", "good")
    ]
    
    # Filter to existing images
    existing_images = [(path, quality) for path, quality in test_images if os.path.exists(path)]
    
    if existing_images:
        import time
        start_time = time.time()
        
        # Process images
        results = []
        for image_data in existing_images:
            result = builder.process_single_image(image_data)
            results.append(result)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        logger.info(f"Processed {len(results)} images in {processing_time:.2f} seconds")
        logger.info(f"Average time per image: {processing_time/len(results):.3f} seconds")
        
        # Show extraction statistics
        total_tags = sum(len(tags) for _, tags, _ in results)
        total_loras = sum(len([tag for tag, _ in tags if tag.startswith('<lora:')]) for _, tags, _ in results)
        
        logger.info(f"Total tags extracted: {total_tags}")
        logger.info(f"Total LoRAs extracted: {total_loras}")
        logger.info(f"Average tags per image: {total_tags/len(results):.1f}")
    else:
        logger.warning("No test images found for performance demonstration")


def main():
    """Main demonstration function."""
    logger.info("PRODUCTION DATASET BUILDER DEMONSTRATION")
    logger.info("=" * 80)
    
    try:
        # Run all demonstrations
        demonstrate_special_lora_format()
        demonstrate_negative_weight_handling()
        demonstrate_filename_preservation()
        demonstrate_dataset_format()
        demonstrate_validation_results()
        demonstrate_performance_metrics()
        
        logger.info("\n" + "=" * 80)
        logger.info("DEMONSTRATION COMPLETED SUCCESSFULLY")
        logger.info("=" * 80)
        
        logger.info("\nKey Features Demonstrated:")
        logger.info("✅ Special LoRA format: <lora:filename>:weight")
        logger.info("✅ Negative weight support")
        logger.info("✅ Filename preservation")
        logger.info("✅ Comprehensive validation")
        logger.info("✅ Production-ready performance")
        
        logger.info("\nThe production dataset builder is ready for full deployment!")
        
        return True
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Comprehensive Validation Test Suite for Graph-Based LoRA Extraction

This script randomly samples 5 images from each original input directory to create
approximately 1,000 test images for validation. Every single image in this test set
must have 100% correct LoRA extraction before proceeding with full dataset processing.

Requirements:
- 100% test success rate required
- Comprehensive error logging with specific failure reasons
- Thread-safe logging for parallel processing
- Detailed statistics and reporting
"""

import os
import sys
import random
import json
import traceback
import logging
import threading
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict, Counter

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import image_info, extract_comfyui_workflow_params, traverse_workflow_graph, extract_connected_lora_nodes
from PIL import Image


class ThreadSafeLogger:
    """Thread-safe logger for parallel processing."""
    
    def __init__(self, log_file):
        self.log_file = log_file
        self.lock = threading.Lock()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def info(self, message):
        with self.lock:
            self.logger.info(message)
    
    def error(self, message):
        with self.lock:
            self.logger.error(message)
    
    def warning(self, message):
        with self.lock:
            self.logger.warning(message)


def find_input_directories():
    """Find all input directories containing images."""
    base_dirs = [
        r"F:\SD-webui\ComfyUI\output",
        r"F:\SD-webui\outputs",
        # Add more base directories as needed
    ]
    
    input_dirs = []
    for base_dir in base_dirs:
        if os.path.exists(base_dir):
            for root, dirs, files in os.walk(base_dir):
                # Check if directory contains image files
                image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.webp'))]
                if len(image_files) >= 5:  # Only include directories with at least 5 images
                    input_dirs.append(root)
    
    return input_dirs


def sample_images_from_directories(input_dirs, samples_per_dir=5):
    """Sample random images from each input directory."""
    sampled_images = []
    
    for input_dir in input_dirs:
        try:
            # Get all image files in directory
            image_files = []
            for ext in ['.png', '.jpg', '.jpeg', '.webp']:
                image_files.extend(Path(input_dir).glob(f'*{ext}'))
                image_files.extend(Path(input_dir).glob(f'*{ext.upper()}'))
            
            if len(image_files) >= samples_per_dir:
                # Randomly sample images
                sampled = random.sample(image_files, samples_per_dir)
                sampled_images.extend([(str(img), input_dir) for img in sampled])
            else:
                # Take all available images if less than required
                sampled_images.extend([(str(img), input_dir) for img in image_files])
                
        except Exception as e:
            print(f"Error sampling from {input_dir}: {e}")
    
    return sampled_images


def validate_single_image(image_path, source_dir, logger):
    """
    Validate LoRA extraction for a single image.
    
    Returns:
        dict: Validation result with success status and details
    """
    result = {
        'image_path': image_path,
        'source_dir': source_dir,
        'success': False,
        'loras_found': [],
        'error': None,
        'workflow_format': 'unknown',
        'connected_nodes': 0,
        'total_lora_nodes': 0,
        'connected_lora_nodes': 0
    }
    
    try:
        # Test image loading
        with Image.open(image_path) as img:
            # Test enhanced prompt extraction
            info = image_info(img, image_path, enhanced_prompts=True)
            enhanced_prompt = info.get('prompt', '')
            
            # Check if workflow metadata exists
            if hasattr(img, 'info') and 'workflow' in img.info:
                result['workflow_format'] = 'api_format'
                
                # Test workflow parameter extraction
                workflow_json = img.info['workflow']
                if isinstance(workflow_json, dict):
                    workflow_json = json.dumps(workflow_json)
                
                params = extract_comfyui_workflow_params(workflow_json)
                result['loras_found'] = params.get('loras', [])
                
                # Test graph traversal details
                from utils import convert_api_workflow_to_standard
                workflow = json.loads(workflow_json)
                workflow = convert_api_workflow_to_standard(workflow)
                
                connected_nodes = traverse_workflow_graph(workflow)
                result['connected_nodes'] = len(connected_nodes)
                
                # Count LoRA nodes
                total_lora_nodes = 0
                connected_lora_nodes = 0
                for node_id, node in workflow.items():
                    if node.get('class_type') in ['LoraTagLoader', 'LoraLoader']:
                        total_lora_nodes += 1
                        if node_id in connected_nodes:
                            connected_lora_nodes += 1
                
                result['total_lora_nodes'] = total_lora_nodes
                result['connected_lora_nodes'] = connected_lora_nodes
                
            elif 'lora' in enhanced_prompt.lower():
                result['workflow_format'] = 'legacy_format'
                # Extract LoRAs from enhanced prompt
                import re
                lora_matches = re.findall(r'--lora\s+([^\s]+)', enhanced_prompt)
                result['loras_found'] = lora_matches
            else:
                result['workflow_format'] = 'no_workflow'
            
            result['success'] = True
            
    except Exception as e:
        result['error'] = str(e)
        result['traceback'] = traceback.format_exc()
        logger.error(f"Failed to validate {image_path}: {e}")
    
    return result


def run_comprehensive_validation(max_workers=4):
    """Run comprehensive validation test suite."""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f'validation_test_{timestamp}.log'
    logger = ThreadSafeLogger(log_file)
    
    logger.info("=" * 80)
    logger.info("COMPREHENSIVE VALIDATION TEST SUITE")
    logger.info("=" * 80)
    
    # Find input directories
    logger.info("Finding input directories...")
    input_dirs = find_input_directories()
    logger.info(f"Found {len(input_dirs)} input directories")
    
    if not input_dirs:
        logger.error("No input directories found!")
        return False
    
    # Sample images
    logger.info("Sampling images from directories...")
    sampled_images = sample_images_from_directories(input_dirs, samples_per_dir=5)
    logger.info(f"Sampled {len(sampled_images)} images for validation")
    
    if len(sampled_images) < 100:
        logger.warning(f"Only {len(sampled_images)} images sampled, expected ~1000")
    
    # Run validation in parallel
    logger.info(f"Starting validation with {max_workers} workers...")
    
    results = []
    success_count = 0
    error_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all validation tasks
        future_to_image = {
            executor.submit(validate_single_image, img_path, src_dir, logger): (img_path, src_dir)
            for img_path, src_dir in sampled_images
        }
        
        # Process completed tasks
        for i, future in enumerate(as_completed(future_to_image), 1):
            img_path, src_dir = future_to_image[future]
            
            try:
                result = future.result()
                results.append(result)
                
                if result['success']:
                    success_count += 1
                    if i % 100 == 0:
                        logger.info(f"Processed {i}/{len(sampled_images)} images ({success_count} successful)")
                else:
                    error_count += 1
                    logger.error(f"Validation failed for {img_path}: {result['error']}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"Exception processing {img_path}: {e}")
    
    # Generate comprehensive report
    logger.info("\n" + "=" * 80)
    logger.info("VALIDATION RESULTS")
    logger.info("=" * 80)
    
    total_images = len(results)
    success_rate = (success_count / total_images * 100) if total_images > 0 else 0
    
    logger.info(f"Total images tested: {total_images}")
    logger.info(f"Successful validations: {success_count}")
    logger.info(f"Failed validations: {error_count}")
    logger.info(f"Success rate: {success_rate:.2f}%")
    
    # Analyze workflow formats
    workflow_formats = Counter(r['workflow_format'] for r in results if r['success'])
    logger.info(f"\nWorkflow formats found:")
    for format_type, count in workflow_formats.items():
        logger.info(f"  {format_type}: {count}")
    
    # Analyze LoRA statistics
    total_loras = sum(len(r['loras_found']) for r in results if r['success'])
    images_with_loras = sum(1 for r in results if r['success'] and r['loras_found'])
    
    logger.info(f"\nLoRA statistics:")
    logger.info(f"  Total LoRAs found: {total_loras}")
    logger.info(f"  Images with LoRAs: {images_with_loras}")
    logger.info(f"  Average LoRAs per image: {total_loras/total_images:.2f}")
    
    # Graph traversal statistics
    successful_graph_results = [r for r in results if r['success'] and r['connected_nodes'] > 0]
    if successful_graph_results:
        avg_connected = sum(r['connected_nodes'] for r in successful_graph_results) / len(successful_graph_results)
        avg_total_lora = sum(r['total_lora_nodes'] for r in successful_graph_results) / len(successful_graph_results)
        avg_connected_lora = sum(r['connected_lora_nodes'] for r in successful_graph_results) / len(successful_graph_results)
        
        logger.info(f"\nGraph traversal statistics:")
        logger.info(f"  Average connected nodes: {avg_connected:.1f}")
        logger.info(f"  Average total LoRA nodes: {avg_total_lora:.1f}")
        logger.info(f"  Average connected LoRA nodes: {avg_connected_lora:.1f}")
    
    # Error analysis
    if error_count > 0:
        logger.info(f"\nError analysis:")
        error_types = Counter(r['error'] for r in results if not r['success'] and r['error'])
        for error_type, count in error_types.most_common(10):
            logger.info(f"  {error_type}: {count}")
    
    # Save detailed results
    results_file = f'validation_results_{timestamp}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    logger.info(f"\nDetailed results saved to: {results_file}")
    
    # Final verdict
    logger.info("\n" + "=" * 80)
    if success_rate == 100.0:
        logger.info("🎉 VALIDATION PASSED: 100% success rate achieved!")
        logger.info("✅ Ready to proceed with full dataset processing")
        return True
    else:
        logger.info(f"❌ VALIDATION FAILED: {success_rate:.2f}% success rate (100% required)")
        logger.info("❌ Must fix all errors before proceeding with full dataset")
        return False


def main():
    """Main function."""
    print("Comprehensive Validation Test Suite for Graph-Based LoRA Extraction")
    print("=" * 80)
    
    # Set random seed for reproducible sampling
    random.seed(42)
    
    # Run validation
    success = run_comprehensive_validation(max_workers=4)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())

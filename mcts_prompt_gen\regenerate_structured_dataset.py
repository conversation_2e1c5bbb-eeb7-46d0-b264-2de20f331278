#!/usr/bin/env python3
"""
Dataset Regeneration Script for Structured Prompt Format

This script regenerates the entire prompt dataset using the enhanced prompt processor,
converting from string format to structured (tag, weight) tuple format with comprehensive
parsing, LoRA extraction, and validation.

Features:
- Parallel processing using existing build.py patterns
- Comprehensive error handling and logging
- Progress tracking and checkpointing
- Final validation and integrity checks
- Timestamped results organization

Requirements:
- Transform data from (filename, prompt_string, goodness) to (filename, [(tag, weight)], goodness_score)
- Achieve >98% parsing accuracy
- Use graph traversal for LoRA extraction
- Process all 66,312 images with proper error handling

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import dill
import json
import logging
import argparse
from typing import List, Tuple, Dict, Any, Optional
from datetime import datetime
import traceback
import shutil

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(__file__))

from enhanced_prompt_processor import EnhancedPromptProcessor
from validate_prompt_parsing_system import PromptParsingValidator


# Set up logging
def setup_logging(log_file: str):
    """Set up comprehensive logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


class DatasetRegenerator:
    """Comprehensive dataset regenerator with validation and checkpointing."""
    
    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.log_file = os.path.join(output_dir, f'regeneration_{self.timestamp}.log')
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Set up logging
        self.logger = setup_logging(self.log_file)
        
        # Initialize components
        self.processor = EnhancedPromptProcessor()
        self.validator = PromptParsingValidator()
        
        # Results tracking
        self.results = {
            'start_time': datetime.now(),
            'input_file': None,
            'output_file': None,
            'backup_file': None,
            'processing_stats': {},
            'validation_results': {},
            'final_status': 'PENDING'
        }
    
    def create_backup(self, input_file: str) -> str:
        """Create backup of original dataset."""
        backup_file = os.path.join(self.output_dir, f'original_backup_{self.timestamp}.pkl')
        
        try:
            shutil.copy2(input_file, backup_file)
            self.logger.info(f"Created backup: {backup_file}")
            return backup_file
        except Exception as e:
            self.logger.error(f"Failed to create backup: {e}")
            raise
    
    def validate_input_dataset(self, input_file: str) -> Dict[str, Any]:
        """Validate input dataset structure and integrity."""
        self.logger.info("Validating input dataset structure")
        
        try:
            with open(input_file, 'rb') as f:
                data = dill.load(f)
            
            validation_info = {
                'total_entries': len(data),
                'valid_entries': 0,
                'invalid_entries': 0,
                'sample_entries': [],
                'data_types': {},
                'issues': []
            }
            
            # Analyze first 100 entries
            sample_size = min(100, len(data))
            for i, entry in enumerate(data[:sample_size]):
                try:
                    if isinstance(entry, tuple) and len(entry) >= 3:
                        filename, prompt, goodness = entry[:3]
                        
                        # Check data types
                        validation_info['data_types']['filename'] = type(filename).__name__
                        validation_info['data_types']['prompt'] = type(prompt).__name__
                        validation_info['data_types']['goodness'] = type(goodness).__name__
                        
                        # Validate structure
                        if (isinstance(filename, str) and 
                            isinstance(prompt, str) and 
                            isinstance(goodness, str)):
                            validation_info['valid_entries'] += 1
                            
                            if len(validation_info['sample_entries']) < 5:
                                validation_info['sample_entries'].append({
                                    'filename': os.path.basename(filename),
                                    'prompt_length': len(prompt),
                                    'goodness': goodness
                                })
                        else:
                            validation_info['invalid_entries'] += 1
                            validation_info['issues'].append(f"Entry {i}: Invalid data types")
                    else:
                        validation_info['invalid_entries'] += 1
                        validation_info['issues'].append(f"Entry {i}: Invalid structure")
                        
                except Exception as e:
                    validation_info['invalid_entries'] += 1
                    validation_info['issues'].append(f"Entry {i}: {e}")
            
            # Calculate validity percentage
            validity_percentage = (validation_info['valid_entries'] / sample_size) * 100
            validation_info['validity_percentage'] = validity_percentage
            
            self.logger.info(f"Input validation: {validity_percentage:.1f}% valid entries")
            self.logger.info(f"Total entries: {validation_info['total_entries']}")
            
            if validity_percentage < 95.0:
                raise ValueError(f"Input dataset validity too low: {validity_percentage:.1f}%")
            
            return validation_info
            
        except Exception as e:
            self.logger.error(f"Input validation failed: {e}")
            raise
    
    def process_dataset_with_checkpointing(self, input_file: str, output_file: str, 
                                         max_workers: int = 4, checkpoint_interval: int = 5000) -> Dict[str, Any]:
        """Process dataset with checkpointing for large datasets."""
        self.logger.info(f"Starting dataset processing with checkpointing")
        self.logger.info(f"Input: {input_file}")
        self.logger.info(f"Output: {output_file}")
        self.logger.info(f"Workers: {max_workers}")
        self.logger.info(f"Checkpoint interval: {checkpoint_interval}")
        
        # Load input data
        try:
            with open(input_file, 'rb') as f:
                input_data = dill.load(f)
            self.logger.info(f"Loaded {len(input_data)} entries from input file")
        except Exception as e:
            self.logger.error(f"Failed to load input file: {e}")
            raise
        
        # Process in chunks with checkpointing
        processed_data = []
        checkpoint_file = output_file + '.checkpoint'
        
        try:
            # Check for existing checkpoint
            if os.path.exists(checkpoint_file):
                self.logger.info("Found existing checkpoint, resuming...")
                with open(checkpoint_file, 'rb') as f:
                    checkpoint_data = dill.load(f)
                processed_data = checkpoint_data.get('processed_data', [])
                start_index = len(processed_data)
                self.logger.info(f"Resuming from entry {start_index}")
            else:
                start_index = 0
            
            # Process remaining data
            remaining_data = input_data[start_index:]
            
            # Use the enhanced processor
            stats = self.processor.process_dataset_parallel(
                input_file, 
                output_file + '.temp', 
                max_workers=max_workers
            )
            
            # Load processed results
            with open(output_file + '.temp', 'rb') as f:
                new_processed_data = dill.load(f)
            
            # Combine with checkpoint data if any
            if processed_data:
                final_data = processed_data + new_processed_data[start_index:]
            else:
                final_data = new_processed_data
            
            # Save final results
            with open(output_file, 'wb') as f:
                dill.dump(final_data, f)
            
            # Clean up temporary files
            if os.path.exists(output_file + '.temp'):
                os.remove(output_file + '.temp')
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)
            
            self.logger.info(f"Processing completed: {len(final_data)} entries saved")
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Processing failed: {e}")
            # Save checkpoint on failure
            if processed_data:
                try:
                    with open(checkpoint_file, 'wb') as f:
                        dill.dump({'processed_data': processed_data}, f)
                    self.logger.info(f"Checkpoint saved: {len(processed_data)} entries")
                except:
                    pass
            raise
    
    def validate_output_dataset(self, output_file: str) -> Dict[str, Any]:
        """Validate output dataset structure and quality."""
        self.logger.info("Validating output dataset")
        
        try:
            # Run comprehensive validation
            validation_results = self.validator.run_comprehensive_validation(output_file)
            
            # Additional output-specific checks
            with open(output_file, 'rb') as f:
                data = dill.load(f)
            
            # Check structure of processed data
            structure_validation = {
                'total_entries': len(data),
                'valid_structure': 0,
                'invalid_structure': 0,
                'avg_tags_per_entry': 0,
                'total_tags': 0
            }
            
            for entry in data[:1000]:  # Check first 1000 entries
                try:
                    filename, tags, goodness = entry
                    
                    if (isinstance(filename, str) and 
                        isinstance(tags, list) and 
                        isinstance(goodness, (int, float))):
                        
                        structure_validation['valid_structure'] += 1
                        structure_validation['total_tags'] += len(tags)
                        
                        # Validate tag structure
                        for tag, weight in tags:
                            if not (isinstance(tag, str) and isinstance(weight, (int, float))):
                                raise ValueError(f"Invalid tag structure: {tag}, {weight}")
                    else:
                        structure_validation['invalid_structure'] += 1
                        
                except Exception as e:
                    structure_validation['invalid_structure'] += 1
            
            if structure_validation['valid_structure'] > 0:
                structure_validation['avg_tags_per_entry'] = (
                    structure_validation['total_tags'] / structure_validation['valid_structure']
                )
            
            validation_results['structure_validation'] = structure_validation
            
            self.logger.info(f"Output validation completed")
            self.logger.info(f"Structure validity: {structure_validation['valid_structure']}/1000")
            self.logger.info(f"Average tags per entry: {structure_validation['avg_tags_per_entry']:.1f}")
            
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Output validation failed: {e}")
            raise
    
    def generate_final_report(self) -> str:
        """Generate comprehensive final report."""
        report = []
        report.append("=" * 80)
        report.append("DATASET REGENERATION - FINAL REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Duration: {datetime.now() - self.results['start_time']}")
        report.append("")
        
        # Processing Summary
        report.append("📊 PROCESSING SUMMARY")
        report.append("-" * 40)
        stats = self.results.get('processing_stats', {})
        report.append(f"Input File: {self.results.get('input_file', 'N/A')}")
        report.append(f"Output File: {self.results.get('output_file', 'N/A')}")
        report.append(f"Backup File: {self.results.get('backup_file', 'N/A')}")
        report.append(f"Total Entries: {stats.get('total_entries', 0)}")
        report.append(f"Processing Accuracy: {stats.get('parsing_accuracy', 0):.1f}%")
        report.append(f"Total Tags: {stats.get('total_tags', 0)}")
        report.append(f"Unique Tags: {stats.get('unique_tags', 0)}")
        report.append(f"Average Tags per Entry: {stats.get('avg_tags_per_entry', 0):.1f}")
        report.append("")
        
        # Validation Results
        validation = self.results.get('validation_results', {})
        report.append("✅ VALIDATION RESULTS")
        report.append("-" * 40)
        report.append(f"Overall Accuracy: {validation.get('overall_accuracy', 0):.1f}%")
        report.append(f"Weight Syntax Accuracy: {validation.get('weight_syntax_accuracy', 0):.1f}%")
        report.append(f"LoRA Extraction Accuracy: {validation.get('lora_extraction_accuracy', 0):.1f}%")
        report.append("")
        
        # Final Status
        report.append("🎯 FINAL STATUS")
        report.append("-" * 40)
        report.append(f"Status: {self.results['final_status']}")
        
        if self.results['final_status'] == 'SUCCESS':
            report.append("🎉 Dataset regeneration completed successfully!")
            report.append("✅ New structured format ready for MCTS integration")
        else:
            report.append("⚠️  Dataset regeneration encountered issues")
            report.append("❌ Manual review required before proceeding")
        
        report.append("=" * 80)
        
        return "\n".join(report)

    def run_full_regeneration(self, input_file: str, output_file: str, max_workers: int = 4) -> bool:
        """Run complete dataset regeneration process."""
        try:
            self.logger.info("🚀 Starting full dataset regeneration")

            # Store file paths
            self.results['input_file'] = input_file
            self.results['output_file'] = output_file

            # Step 1: Create backup
            self.results['backup_file'] = self.create_backup(input_file)

            # Step 2: Validate input
            input_validation = self.validate_input_dataset(input_file)
            self.logger.info(f"Input validation: {input_validation['validity_percentage']:.1f}% valid")

            # Step 3: Process dataset
            processing_stats = self.process_dataset_with_checkpointing(
                input_file, output_file, max_workers
            )
            self.results['processing_stats'] = processing_stats

            # Step 4: Validate output
            validation_results = self.validate_output_dataset(output_file)
            self.results['validation_results'] = validation_results

            # Step 5: Check success criteria
            overall_accuracy = validation_results.get('overall_accuracy', 0)
            parsing_accuracy = processing_stats.get('parsing_accuracy', 0)

            if overall_accuracy >= 98.0 and parsing_accuracy >= 98.0:
                self.results['final_status'] = 'SUCCESS'
                self.logger.info("🎉 Dataset regeneration completed successfully!")
                return True
            else:
                self.results['final_status'] = 'FAILED'
                self.logger.warning(f"⚠️  Regeneration failed: accuracy too low")
                self.logger.warning(f"Overall: {overall_accuracy:.1f}%, Parsing: {parsing_accuracy:.1f}%")
                return False

        except Exception as e:
            self.results['final_status'] = 'ERROR'
            self.logger.error(f"💥 Regeneration failed with error: {e}")
            self.logger.error(traceback.format_exc())
            return False

        finally:
            # Always generate final report
            report = self.generate_final_report()

            # Save report
            report_file = os.path.join(self.output_dir, f'regeneration_report_{self.timestamp}.txt')
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            self.logger.info(f"📄 Final report saved: {report_file}")
            print("\n" + report)


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description='Regenerate Dataset with Structured Prompt Format')
    parser.add_argument('--input', required=True, help='Input pickle file with string prompts')
    parser.add_argument('--output', required=True, help='Output pickle file for structured prompts')
    parser.add_argument('--output-dir', default='results', help='Output directory for logs and reports')
    parser.add_argument('--workers', type=int, default=4, help='Number of parallel workers')
    parser.add_argument('--force', action='store_true', help='Force regeneration even if output exists')

    args = parser.parse_args()

    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"❌ Input file not found: {args.input}")
        return 1

    # Check if output file already exists
    if os.path.exists(args.output) and not args.force:
        print(f"⚠️  Output file already exists: {args.output}")
        print("Use --force to overwrite or choose a different output file")
        return 1

    # Create output directory with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(args.output_dir, f'regeneration_{timestamp}')

    # Create regenerator
    regenerator = DatasetRegenerator(output_dir)

    # Run regeneration
    print("🔄 Starting dataset regeneration...")
    print(f"📁 Input: {args.input}")
    print(f"📁 Output: {args.output}")
    print(f"📁 Logs: {output_dir}")
    print(f"👥 Workers: {args.workers}")
    print()

    success = regenerator.run_full_regeneration(args.input, args.output, args.workers)

    if success:
        print("\n🎉 SUCCESS! Dataset regeneration completed.")
        print(f"✅ New structured dataset saved to: {args.output}")
        print(f"📊 Detailed logs and reports in: {output_dir}")
        return 0
    else:
        print("\n❌ FAILED! Dataset regeneration encountered issues.")
        print(f"📋 Check logs and reports in: {output_dir}")
        print("🔧 Manual review and fixes may be required.")
        return 1


if __name__ == "__main__":
    sys.exit(main())

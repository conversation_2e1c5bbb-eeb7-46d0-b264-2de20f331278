#!/usr/bin/env python3
"""
Data Quality Analyzer for Structured Prompt Dataset

This module performs comprehensive data quality analysis and standardization
for the structured prompt dataset containing (filename, [(tag, weight) tuples], goodness_score).

Features:
- Tag standardization and normalization
- LoRA tag consolidation
- Artist tag deduplication
- Technical parameter consistency
- Comprehensive statistics and validation
- Data integrity checks

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import json
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
from datetime import datetime
from typing import List, Tuple, Dict, Any, Set
import re
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataQualityAnalyzer:
    """
    Comprehensive data quality analyzer for structured prompt datasets.
    
    Performs tag standardization, validation, and statistical analysis.
    """
    
    def __init__(self, dataset_path: str):
        """
        Initialize the analyzer with dataset path.
        
        Args:
            dataset_path: Path to the structured prompt dataset pickle file
        """
        self.dataset_path = dataset_path
        self.dataset = None
        self.analysis_results = {}
        self.standardization_stats = {}
        
        # Tag patterns for identification
        self.lora_patterns = [
            r'<lora:([^>]+)>',
            r'lora:([^:]+)',
            r'lora_([^:]+)'
        ]
        
        self.artist_patterns = [
            r'artist:(.+)',
            r'by_(.+)',
            r'style_of_(.+)'
        ]
        
        self.technical_params = {
            'cfg', 'steps', 'width', 'height', 'sampler', 'scheduler',
            'seed', 'scale', 'strength', 'noise'
        }
    
    def load_dataset(self) -> bool:
        """
        Load and validate the dataset structure.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Loading dataset from {self.dataset_path}")
            with open(self.dataset_path, 'rb') as f:
                self.dataset = dill.load(f)
            
            logger.info(f"Successfully loaded {len(self.dataset)} entries")
            
            # Validate structure
            if not self.dataset:
                logger.error("Dataset is empty!")
                return False
            
            # Check first entry structure
            first_entry = self.dataset[0]
            if not isinstance(first_entry, tuple) or len(first_entry) != 3:
                logger.error(f"Invalid entry structure: {type(first_entry)}")
                return False
            
            filename, tags, goodness = first_entry
            if not isinstance(tags, list):
                logger.error(f"Invalid tags structure: {type(tags)}")
                return False
            
            if tags and not isinstance(tags[0], tuple):
                logger.error(f"Invalid tag format: {type(tags[0])}")
                return False
            
            logger.info("✅ Dataset structure validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            return False
    
    def analyze_tag_distribution(self) -> Dict[str, Any]:
        """
        Analyze tag frequency and distribution patterns.
        
        Returns:
            Dictionary containing tag analysis results
        """
        logger.info("Analyzing tag distribution...")
        
        all_tags = []
        all_weights = []
        tag_counter = Counter()
        weight_by_tag = defaultdict(list)
        
        for filename, tags, goodness in self.dataset:
            for tag, weight in tags:
                all_tags.append(tag)
                all_weights.append(weight)
                tag_counter[tag] += 1
                weight_by_tag[tag].append(weight)
        
        # Calculate statistics
        total_tags = len(all_tags)
        unique_tags = len(tag_counter)
        avg_tags_per_prompt = total_tags / len(self.dataset)
        
        # Weight statistics
        weight_stats = {
            'mean': np.mean(all_weights),
            'std': np.std(all_weights),
            'min': np.min(all_weights),
            'max': np.max(all_weights),
            'median': np.median(all_weights),
            'q25': np.percentile(all_weights, 25),
            'q75': np.percentile(all_weights, 75)
        }
        
        # Most common tags
        top_tags = tag_counter.most_common(50)
        
        # Tags with weight variations
        variable_weight_tags = {}
        for tag, weights in weight_by_tag.items():
            if len(set(weights)) > 1:  # Multiple different weights
                variable_weight_tags[tag] = {
                    'count': len(weights),
                    'unique_weights': len(set(weights)),
                    'weight_range': (min(weights), max(weights)),
                    'avg_weight': np.mean(weights)
                }
        
        analysis = {
            'total_tags': total_tags,
            'unique_tags': unique_tags,
            'avg_tags_per_prompt': avg_tags_per_prompt,
            'weight_stats': weight_stats,
            'top_tags': top_tags,
            'variable_weight_tags': dict(list(variable_weight_tags.items())[:20])
        }
        
        logger.info(f"Tag analysis complete: {unique_tags} unique tags, {total_tags} total")
        return analysis
    
    def identify_lora_tags(self) -> Dict[str, Any]:
        """
        Identify and analyze LoRA tags with standardization opportunities.
        
        Returns:
            Dictionary containing LoRA analysis results
        """
        logger.info("Identifying LoRA tags...")
        
        lora_tags = defaultdict(list)
        lora_variations = defaultdict(set)
        
        for filename, tags, goodness in self.dataset:
            for tag, weight in tags:
                # Check for LoRA patterns
                for pattern in self.lora_patterns:
                    match = re.search(pattern, tag, re.IGNORECASE)
                    if match:
                        lora_name = match.group(1) if match.groups() else tag
                        lora_tags[lora_name].append((tag, weight))
                        lora_variations[lora_name].add(tag)
                        break
        
        # Find LoRA tags that need standardization
        standardization_candidates = {}
        for lora_name, variations in lora_variations.items():
            if len(variations) > 1:
                standardization_candidates[lora_name] = {
                    'variations': list(variations),
                    'count': len(lora_tags[lora_name]),
                    'weights': [w for _, w in lora_tags[lora_name]]
                }
        
        analysis = {
            'total_lora_instances': sum(len(instances) for instances in lora_tags.values()),
            'unique_lora_models': len(lora_tags),
            'standardization_candidates': standardization_candidates,
            'top_lora_models': dict(Counter({name: len(instances) 
                                           for name, instances in lora_tags.items()}).most_common(20))
        }
        
        logger.info(f"LoRA analysis complete: {analysis['unique_lora_models']} unique models")
        return analysis
    
    def identify_artist_tags(self) -> Dict[str, Any]:
        """
        Identify and analyze artist tags for consolidation.
        
        Returns:
            Dictionary containing artist analysis results
        """
        logger.info("Identifying artist tags...")
        
        artist_tags = defaultdict(list)
        artist_variations = defaultdict(set)
        
        for filename, tags, goodness in self.dataset:
            for tag, weight in tags:
                # Check for artist patterns
                for pattern in self.artist_patterns:
                    match = re.search(pattern, tag, re.IGNORECASE)
                    if match:
                        artist_name = match.group(1).lower().strip()
                        artist_tags[artist_name].append((tag, weight))
                        artist_variations[artist_name].add(tag)
                        break
                
                # Also check for common artist names (without prefix)
                # This is a simplified approach - in practice, you'd have a curated list
                if any(keyword in tag.lower() for keyword in ['artist', 'by_', 'style']):
                    continue  # Already handled above
                
                # Check if tag might be an artist name (heuristic)
                if (len(tag.split('_')) <= 3 and 
                    not any(char.isdigit() for char in tag) and
                    not tag.lower() in self.technical_params):
                    # Potential artist name
                    artist_name = tag.lower().strip()
                    if artist_name not in artist_tags:
                        artist_tags[artist_name].append((tag, weight))
                        artist_variations[artist_name].add(tag)
        
        # Find consolidation candidates
        consolidation_candidates = {}
        for artist_name, variations in artist_variations.items():
            if len(variations) > 1:
                consolidation_candidates[artist_name] = {
                    'variations': list(variations),
                    'count': len(artist_tags[artist_name]),
                    'weights': [w for _, w in artist_tags[artist_name]]
                }
        
        analysis = {
            'total_artist_instances': sum(len(instances) for instances in artist_tags.values()),
            'unique_artists': len(artist_tags),
            'consolidation_candidates': consolidation_candidates,
            'top_artists': dict(Counter({name: len(instances) 
                                       for name, instances in artist_tags.items()}).most_common(20))
        }
        
        logger.info(f"Artist analysis complete: {analysis['unique_artists']} unique artists")
        return analysis

    def analyze_technical_parameters(self) -> Dict[str, Any]:
        """
        Analyze technical parameters for consistency.

        Returns:
            Dictionary containing technical parameter analysis
        """
        logger.info("Analyzing technical parameters...")

        tech_param_stats = defaultdict(list)
        param_counter = Counter()

        for filename, tags, goodness in self.dataset:
            for tag, weight in tags:
                if tag.lower() in self.technical_params:
                    tech_param_stats[tag.lower()].append(weight)
                    param_counter[tag.lower()] += 1

        # Calculate statistics for each parameter
        param_analysis = {}
        for param, values in tech_param_stats.items():
            param_analysis[param] = {
                'count': len(values),
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'unique_values': len(set(values))
            }

        analysis = {
            'total_tech_params': sum(param_counter.values()),
            'param_types': len(param_counter),
            'param_distribution': dict(param_counter),
            'param_statistics': param_analysis
        }

        logger.info(f"Technical parameter analysis complete: {analysis['param_types']} types")
        return analysis

    def validate_data_integrity(self) -> Dict[str, Any]:
        """
        Perform comprehensive data integrity validation.

        Returns:
            Dictionary containing validation results
        """
        logger.info("Validating data integrity...")

        validation_results = {
            'total_entries': len(self.dataset),
            'valid_entries': 0,
            'invalid_entries': 0,
            'empty_tag_lists': 0,
            'invalid_weights': 0,
            'invalid_goodness_scores': 0,
            'parsing_accuracy': 0.0,
            'issues': []
        }

        for i, entry in enumerate(self.dataset):
            try:
                if not isinstance(entry, tuple) or len(entry) != 3:
                    validation_results['invalid_entries'] += 1
                    validation_results['issues'].append(f"Entry {i}: Invalid structure")
                    continue

                filename, tags, goodness = entry

                # Validate filename
                if not isinstance(filename, str) or not filename:
                    validation_results['issues'].append(f"Entry {i}: Invalid filename")
                    continue

                # Validate tags
                if not isinstance(tags, list):
                    validation_results['invalid_entries'] += 1
                    validation_results['issues'].append(f"Entry {i}: Tags not a list")
                    continue

                if len(tags) == 0:
                    validation_results['empty_tag_lists'] += 1

                # Validate individual tags
                for j, tag_entry in enumerate(tags):
                    if not isinstance(tag_entry, tuple) or len(tag_entry) != 2:
                        validation_results['issues'].append(f"Entry {i}, tag {j}: Invalid tag structure")
                        continue

                    tag, weight = tag_entry
                    if not isinstance(tag, str):
                        validation_results['issues'].append(f"Entry {i}, tag {j}: Tag not string")

                    if not isinstance(weight, (int, float)):
                        validation_results['invalid_weights'] += 1
                        validation_results['issues'].append(f"Entry {i}, tag {j}: Invalid weight type")
                    elif not (-10.0 <= weight <= 10.0):  # Reasonable weight range
                        validation_results['invalid_weights'] += 1
                        validation_results['issues'].append(f"Entry {i}, tag {j}: Weight out of range: {weight}")

                # Validate goodness score
                if not isinstance(goodness, (int, float)) or goodness not in [0.0, 1.0]:
                    validation_results['invalid_goodness_scores'] += 1
                    validation_results['issues'].append(f"Entry {i}: Invalid goodness score: {goodness}")

                validation_results['valid_entries'] += 1

            except Exception as e:
                validation_results['invalid_entries'] += 1
                validation_results['issues'].append(f"Entry {i}: Exception during validation: {e}")

        # Calculate parsing accuracy
        validation_results['parsing_accuracy'] = (
            validation_results['valid_entries'] / validation_results['total_entries'] * 100
        )

        logger.info(f"Data integrity validation complete: {validation_results['parsing_accuracy']:.2f}% accuracy")
        return validation_results

    def generate_comprehensive_statistics(self) -> Dict[str, Any]:
        """
        Generate comprehensive dataset statistics.

        Returns:
            Dictionary containing all statistics
        """
        logger.info("Generating comprehensive statistics...")

        # Goodness score distribution
        goodness_distribution = Counter()
        tags_per_prompt = []
        weights_per_prompt = []

        for filename, tags, goodness in self.dataset:
            goodness_distribution[goodness] += 1
            tags_per_prompt.append(len(tags))
            weights_per_prompt.extend([weight for _, weight in tags])

        # Calculate prompt-level statistics
        prompt_stats = {
            'avg_tags_per_prompt': np.mean(tags_per_prompt),
            'std_tags_per_prompt': np.std(tags_per_prompt),
            'min_tags_per_prompt': np.min(tags_per_prompt),
            'max_tags_per_prompt': np.max(tags_per_prompt),
            'median_tags_per_prompt': np.median(tags_per_prompt)
        }

        # Weight distribution
        weight_distribution = {
            'total_weights': len(weights_per_prompt),
            'mean_weight': np.mean(weights_per_prompt),
            'std_weight': np.std(weights_per_prompt),
            'min_weight': np.min(weights_per_prompt),
            'max_weight': np.max(weights_per_prompt),
            'weight_percentiles': {
                '1%': np.percentile(weights_per_prompt, 1),
                '5%': np.percentile(weights_per_prompt, 5),
                '25%': np.percentile(weights_per_prompt, 25),
                '50%': np.percentile(weights_per_prompt, 50),
                '75%': np.percentile(weights_per_prompt, 75),
                '95%': np.percentile(weights_per_prompt, 95),
                '99%': np.percentile(weights_per_prompt, 99)
            }
        }

        # Identify outliers
        outlier_weights = [w for w in weights_per_prompt if abs(w) > 3.0]

        statistics = {
            'dataset_size': len(self.dataset),
            'goodness_distribution': dict(goodness_distribution),
            'prompt_statistics': prompt_stats,
            'weight_distribution': weight_distribution,
            'outlier_weights': {
                'count': len(outlier_weights),
                'examples': outlier_weights[:20] if outlier_weights else []
            }
        }

        logger.info("Comprehensive statistics generation complete")
        return statistics

    def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """
        Run complete data quality analysis.

        Returns:
            Dictionary containing all analysis results
        """
        logger.info("Starting comprehensive data quality analysis...")

        if not self.load_dataset():
            raise ValueError("Failed to load dataset")

        # Run all analyses
        self.analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'dataset_path': self.dataset_path,
            'tag_distribution': self.analyze_tag_distribution(),
            'lora_analysis': self.identify_lora_tags(),
            'artist_analysis': self.identify_artist_tags(),
            'technical_parameters': self.analyze_technical_parameters(),
            'data_integrity': self.validate_data_integrity(),
            'comprehensive_stats': self.generate_comprehensive_statistics()
        }

        logger.info("Comprehensive analysis complete!")
        return self.analysis_results

    def create_visualizations(self, output_dir: str) -> None:
        """
        Create comprehensive visualizations of the analysis results.

        Args:
            output_dir: Directory to save visualization files
        """
        logger.info(f"Creating visualizations in {output_dir}")
        os.makedirs(output_dir, exist_ok=True)

        # Set style
        plt.style.use('default')
        sns.set_palette("husl")

        # Create comprehensive dashboard
        fig, axes = plt.subplots(3, 3, figsize=(20, 15))
        fig.suptitle('Comprehensive Data Quality Analysis Dashboard', fontsize=16, fontweight='bold')

        # 1. Goodness score distribution
        goodness_dist = self.analysis_results['comprehensive_stats']['goodness_distribution']
        axes[0, 0].bar(goodness_dist.keys(), goodness_dist.values(), color=['red', 'green'])
        axes[0, 0].set_title('Goodness Score Distribution')
        axes[0, 0].set_xlabel('Goodness Score')
        axes[0, 0].set_ylabel('Count')

        # 2. Tags per prompt distribution
        tag_stats = self.analysis_results['comprehensive_stats']['prompt_statistics']
        axes[0, 1].hist([len(tags) for _, tags, _ in self.dataset], bins=50, alpha=0.7, color='blue')
        axes[0, 1].axvline(tag_stats['avg_tags_per_prompt'], color='red', linestyle='--',
                          label=f"Mean: {tag_stats['avg_tags_per_prompt']:.1f}")
        axes[0, 1].set_title('Tags per Prompt Distribution')
        axes[0, 1].set_xlabel('Number of Tags')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].legend()

        # 3. Weight distribution
        all_weights = []
        for _, tags, _ in self.dataset:
            all_weights.extend([weight for _, weight in tags])
        axes[0, 2].hist(all_weights, bins=100, alpha=0.7, color='purple')
        axes[0, 2].set_title('Weight Distribution')
        axes[0, 2].set_xlabel('Weight Value')
        axes[0, 2].set_ylabel('Frequency')
        axes[0, 2].set_xlim(-3, 3)  # Focus on main range

        # 4. Top tags
        top_tags = self.analysis_results['tag_distribution']['top_tags'][:15]
        tag_names = [tag for tag, count in top_tags]
        tag_counts = [count for tag, count in top_tags]
        axes[1, 0].barh(range(len(tag_names)), tag_counts, color='orange')
        axes[1, 0].set_yticks(range(len(tag_names)))
        axes[1, 0].set_yticklabels(tag_names, fontsize=8)
        axes[1, 0].set_title('Top 15 Most Common Tags')
        axes[1, 0].set_xlabel('Frequency')

        # 5. LoRA model distribution
        lora_models = self.analysis_results['lora_analysis']['top_lora_models']
        if lora_models:
            lora_names = list(lora_models.keys())[:10]
            lora_counts = [lora_models[name] for name in lora_names]
            axes[1, 1].barh(range(len(lora_names)), lora_counts, color='cyan')
            axes[1, 1].set_yticks(range(len(lora_names)))
            axes[1, 1].set_yticklabels(lora_names, fontsize=8)
            axes[1, 1].set_title('Top 10 LoRA Models')
            axes[1, 1].set_xlabel('Usage Count')
        else:
            axes[1, 1].text(0.5, 0.5, 'No LoRA models found', ha='center', va='center')
            axes[1, 1].set_title('LoRA Models')

        # 6. Technical parameters
        tech_params = self.analysis_results['technical_parameters']['param_distribution']
        if tech_params:
            param_names = list(tech_params.keys())
            param_counts = list(tech_params.values())
            axes[1, 2].bar(param_names, param_counts, color='green')
            axes[1, 2].set_title('Technical Parameters Usage')
            axes[1, 2].set_xlabel('Parameter Type')
            axes[1, 2].set_ylabel('Count')
            axes[1, 2].tick_params(axis='x', rotation=45)
        else:
            axes[1, 2].text(0.5, 0.5, 'No technical parameters found', ha='center', va='center')
            axes[1, 2].set_title('Technical Parameters')

        # 7. Data integrity metrics
        integrity = self.analysis_results['data_integrity']
        metrics = ['Valid Entries', 'Invalid Entries', 'Empty Tag Lists', 'Invalid Weights']
        values = [integrity['valid_entries'], integrity['invalid_entries'],
                 integrity['empty_tag_lists'], integrity['invalid_weights']]
        colors = ['green', 'red', 'orange', 'yellow']
        axes[2, 0].bar(metrics, values, color=colors)
        axes[2, 0].set_title('Data Integrity Metrics')
        axes[2, 0].set_ylabel('Count')
        axes[2, 0].tick_params(axis='x', rotation=45)

        # 8. Weight outliers
        outliers = self.analysis_results['comprehensive_stats']['outlier_weights']
        if outliers['examples']:
            axes[2, 1].hist(outliers['examples'], bins=20, alpha=0.7, color='red')
            axes[2, 1].set_title(f'Weight Outliers (|w| > 3.0)\nCount: {outliers["count"]}')
            axes[2, 1].set_xlabel('Weight Value')
            axes[2, 1].set_ylabel('Frequency')
        else:
            axes[2, 1].text(0.5, 0.5, 'No weight outliers found', ha='center', va='center')
            axes[2, 1].set_title('Weight Outliers')

        # 9. Parsing accuracy summary
        accuracy = integrity['parsing_accuracy']
        axes[2, 2].pie([accuracy, 100-accuracy], labels=['Valid', 'Invalid'],
                      colors=['green', 'red'], autopct='%1.1f%%')
        axes[2, 2].set_title(f'Parsing Accuracy\n{accuracy:.2f}%')

        plt.tight_layout()

        # Save dashboard
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dashboard_path = os.path.join(output_dir, f'data_quality_dashboard_{timestamp}.png')
        plt.savefig(dashboard_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Dashboard saved to {dashboard_path}")

    def save_analysis_results(self, output_dir: str) -> str:
        """
        Save analysis results to JSON file.

        Args:
            output_dir: Directory to save results

        Returns:
            Path to saved results file
        """
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_path = os.path.join(output_dir, f'data_quality_analysis_{timestamp}.json')

        with open(results_path, 'w') as f:
            json.dump(self.analysis_results, f, indent=2, default=str)

        logger.info(f"Analysis results saved to {results_path}")
        return results_path

    def generate_summary_report(self, output_dir: str) -> str:
        """
        Generate a human-readable summary report.

        Args:
            output_dir: Directory to save report

        Returns:
            Path to saved report file
        """
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(output_dir, f'data_quality_report_{timestamp}.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("COMPREHENSIVE DATA QUALITY ANALYSIS REPORT\n")
            f.write("=" * 80 + "\n\n")

            f.write(f"Analysis Date: {self.analysis_results['timestamp']}\n")
            f.write(f"Dataset: {self.analysis_results['dataset_path']}\n\n")

            # Dataset Overview
            stats = self.analysis_results['comprehensive_stats']
            f.write("DATASET OVERVIEW\n")
            f.write("-" * 40 + "\n")
            f.write(f"Total Entries: {stats['dataset_size']:,}\n")
            f.write(f"Good Quality: {stats['goodness_distribution'].get(1.0, 0):,} ({stats['goodness_distribution'].get(1.0, 0)/stats['dataset_size']*100:.1f}%)\n")
            f.write(f"Normal Quality: {stats['goodness_distribution'].get(0.0, 0):,} ({stats['goodness_distribution'].get(0.0, 0)/stats['dataset_size']*100:.1f}%)\n\n")

            # Tag Statistics
            tag_dist = self.analysis_results['tag_distribution']
            f.write("TAG STATISTICS\n")
            f.write("-" * 40 + "\n")
            f.write(f"Total Tags: {tag_dist['total_tags']:,}\n")
            f.write(f"Unique Tags: {tag_dist['unique_tags']:,}\n")
            f.write(f"Average Tags per Prompt: {tag_dist['avg_tags_per_prompt']:.1f}\n")
            f.write(f"Weight Range: {tag_dist['weight_stats']['min']:.3f} to {tag_dist['weight_stats']['max']:.3f}\n")
            f.write(f"Average Weight: {tag_dist['weight_stats']['mean']:.3f}\n\n")

            # Top Tags
            f.write("TOP 10 MOST COMMON TAGS\n")
            f.write("-" * 40 + "\n")
            for i, (tag, count) in enumerate(tag_dist['top_tags'][:10], 1):
                f.write(f"{i:2d}. {tag}: {count:,} ({count/stats['dataset_size']*100:.1f}%)\n")
            f.write("\n")

            # LoRA Analysis
            lora_analysis = self.analysis_results['lora_analysis']
            f.write("LORA MODEL ANALYSIS\n")
            f.write("-" * 40 + "\n")
            f.write(f"Total LoRA Instances: {lora_analysis['total_lora_instances']:,}\n")
            f.write(f"Unique LoRA Models: {lora_analysis['unique_lora_models']:,}\n")
            f.write(f"Standardization Candidates: {len(lora_analysis['standardization_candidates'])}\n\n")

            if lora_analysis['top_lora_models']:
                f.write("TOP 10 LORA MODELS\n")
                f.write("-" * 40 + "\n")
                for i, (model, count) in enumerate(list(lora_analysis['top_lora_models'].items())[:10], 1):
                    f.write(f"{i:2d}. {model}: {count:,}\n")
                f.write("\n")

            # Data Integrity
            integrity = self.analysis_results['data_integrity']
            f.write("DATA INTEGRITY ASSESSMENT\n")
            f.write("-" * 40 + "\n")
            f.write(f"Parsing Accuracy: {integrity['parsing_accuracy']:.2f}%\n")
            f.write(f"Valid Entries: {integrity['valid_entries']:,}\n")
            f.write(f"Invalid Entries: {integrity['invalid_entries']:,}\n")
            f.write(f"Empty Tag Lists: {integrity['empty_tag_lists']:,}\n")
            f.write(f"Invalid Weights: {integrity['invalid_weights']:,}\n\n")

            # Quality Assessment
            f.write("QUALITY ASSESSMENT\n")
            f.write("-" * 40 + "\n")
            if integrity['parsing_accuracy'] >= 98.0:
                f.write("✅ EXCELLENT: Parsing accuracy exceeds 98% requirement\n")
            elif integrity['parsing_accuracy'] >= 95.0:
                f.write("✅ GOOD: Parsing accuracy above 95%\n")
            else:
                f.write("⚠️  NEEDS IMPROVEMENT: Parsing accuracy below 95%\n")

            if integrity['invalid_weights'] / stats['dataset_size'] < 0.01:
                f.write("✅ EXCELLENT: Weight validation errors < 1%\n")
            else:
                f.write("⚠️  NEEDS ATTENTION: Weight validation errors > 1%\n")

            f.write("\n")

            # Recommendations
            f.write("RECOMMENDATIONS\n")
            f.write("-" * 40 + "\n")

            if len(lora_analysis['standardization_candidates']) > 0:
                f.write("• Consider standardizing LoRA tag formats\n")

            if integrity['invalid_weights'] > 0:
                f.write("• Review and fix invalid weight values\n")

            if integrity['empty_tag_lists'] > 0:
                f.write("• Investigate entries with empty tag lists\n")

            outliers = stats['outlier_weights']['count']
            if outliers > 0:
                f.write(f"• Review {outliers} weight outliers (|w| > 3.0)\n")

            f.write("\n" + "=" * 80 + "\n")

        logger.info(f"Summary report saved to {report_path}")
        return report_path


def main():
    """Main execution function with CLI interface."""
    import argparse

    parser = argparse.ArgumentParser(description='Comprehensive Data Quality Analysis for Structured Prompt Dataset')
    parser.add_argument('--dataset', default='production_dataset.pkl',
                       help='Path to structured prompt dataset pickle file')
    parser.add_argument('--output-dir', default='data_quality_analysis',
                       help='Output directory for results')
    parser.add_argument('--no-viz', action='store_true',
                       help='Skip visualization generation')
    parser.add_argument('--quick', action='store_true',
                       help='Run quick analysis (skip detailed visualizations)')

    args = parser.parse_args()

    try:
        # Initialize analyzer
        analyzer = DataQualityAnalyzer(args.dataset)

        # Run comprehensive analysis
        logger.info("Starting comprehensive data quality analysis...")
        results = analyzer.run_comprehensive_analysis()

        # Create output directory with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"{args.output_dir}_{timestamp}"

        # Save results
        results_path = analyzer.save_analysis_results(output_dir)
        report_path = analyzer.generate_summary_report(output_dir)

        # Generate visualizations
        if not args.no_viz:
            analyzer.create_visualizations(output_dir)

        # Print summary
        print("\n" + "="*80)
        print("DATA QUALITY ANALYSIS COMPLETE")
        print("="*80)
        print(f"Dataset: {args.dataset}")
        print(f"Total Entries: {results['comprehensive_stats']['dataset_size']:,}")
        print(f"Parsing Accuracy: {results['data_integrity']['parsing_accuracy']:.2f}%")
        print(f"Unique Tags: {results['tag_distribution']['unique_tags']:,}")
        print(f"LoRA Models: {results['lora_analysis']['unique_lora_models']:,}")
        print(f"\nResults saved to: {output_dir}")
        print(f"Summary report: {report_path}")
        if not args.no_viz:
            print(f"Visualizations: {output_dir}/data_quality_dashboard_*.png")

        # Quality assessment
        accuracy = results['data_integrity']['parsing_accuracy']
        if accuracy >= 98.0:
            print("\n✅ DATASET QUALITY: EXCELLENT (Ready for production)")
        elif accuracy >= 95.0:
            print("\n✅ DATASET QUALITY: GOOD (Minor improvements recommended)")
        else:
            print("\n⚠️  DATASET QUALITY: NEEDS IMPROVEMENT")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise


if __name__ == "__main__":
    main()

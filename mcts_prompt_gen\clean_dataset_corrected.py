#!/usr/bin/env python3
"""
CORRECTED Dataset Cleaning Script

This script fixes the critical bug in the original cleaning script where
base filenames were used as unique keys, incorrectly treating different
images from different date folders as duplicates.

CORRECTED LOGIC:
- Use date_folder + base_filename as unique key
- Only images within the same date folder can be duplicates
- 2024-05-05/ComfyUI_00001_.png and 2025-01-31-01/ComfyUI_00001_.png are DIFFERENT

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import logging
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import List, Tuple, Dict, Set, Any
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'corrected_dataset_cleaning_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CorrectedDatasetCleaner:
    """CORRECTED dataset cleaner with proper unique key logic."""
    
    def __init__(self, input_path: str = "production_dataset.pkl", output_path: str = "corrected_cleaned_dataset.pkl"):
        """Initialize the cleaner."""
        self.input_path = input_path
        self.output_path = output_path
        self.dataset = None
        
    def load_dataset(self) -> bool:
        """Load the dataset from pickle file."""
        try:
            logger.info(f"Loading dataset from {self.input_path}...")
            with open(self.input_path, 'rb') as f:
                self.dataset = dill.load(f)
            logger.info(f"Successfully loaded {len(self.dataset)} entries")
            return True
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            return False
    
    def analyze_duplicates(self) -> Dict[str, Any]:
        """CORRECTED: Analyze duplicate patterns using proper unique keys."""
        logger.info("Analyzing duplicate patterns with CORRECTED logic...")
        
        # CORRECTED: Use date_folder + base_filename as unique key
        duplicate_groups = defaultdict(list)
        base_level_images = []
        subfolder_images = []
        
        for i, (filename, tags, goodness_score) in enumerate(self.dataset):
            path_parts = Path(filename).parts
            base_filename = Path(filename).name
            
            # Find date folder to create proper unique key
            date_folder = None
            for part in path_parts:
                if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                    date_folder = part
                    break
            
            # CORRECTED: Create proper unique key: date_folder + base_filename
            if date_folder:
                unique_key = f"{date_folder}/{base_filename}"
            else:
                unique_key = f"no_date/{base_filename}"  # Fallback for files without date folders
            
            # Group by CORRECTED unique key
            duplicate_groups[unique_key].append({
                'index': i,
                'full_path': filename,
                'goodness_score': goodness_score,
                'tags_count': len(tags)
            })
            
            # Analyze path structure for base-level vs subfolder
            if date_folder and date_folder in path_parts:
                date_index = path_parts.index(date_folder)
                remaining_parts = path_parts[date_index+1:]
                
                # Check if it's a base level image
                if len(remaining_parts) == 1 and remaining_parts[0].startswith('ComfyUI_'):
                    base_level_images.append(i)
                else:
                    subfolder_images.append(i)
        
        # Find actual duplicates (same unique key with multiple entries)
        actual_duplicates = {key: entries for key, entries in duplicate_groups.items() if len(entries) > 1}
        
        analysis = {
            'total_entries': len(self.dataset),
            'unique_keys': len(duplicate_groups),
            'duplicate_groups': actual_duplicates,
            'duplicate_count': len(actual_duplicates),
            'base_level_count': len(base_level_images),
            'subfolder_count': len(subfolder_images),
            'base_level_indices': set(base_level_images),
            'subfolder_indices': set(subfolder_images)
        }
        
        logger.info(f"CORRECTED ANALYSIS:")
        logger.info(f"  Total entries: {analysis['total_entries']:,}")
        logger.info(f"  Unique keys (date_folder/filename): {analysis['unique_keys']:,}")
        logger.info(f"  Duplicate groups: {analysis['duplicate_count']:,}")
        logger.info(f"  Base-level images: {analysis['base_level_count']:,}")
        logger.info(f"  Subfolder images: {analysis['subfolder_count']:,}")
        
        return analysis
    
    def analyze_relevance(self) -> Dict[str, Any]:
        """Analyze tag relevance (1girl/2girls filter)."""
        logger.info("Analyzing tag relevance...")
        
        relevant_indices = []
        irrelevant_indices = []
        
        for i, (filename, tags, goodness_score) in enumerate(self.dataset):
            # Extract tag names (ignore weights)
            tag_names = [tag.lower() for tag, weight in tags]
            
            # Check for relevance
            is_relevant = any(tag in tag_names for tag in ['1girl', '2girls'])
            
            if is_relevant:
                relevant_indices.append(i)
            else:
                irrelevant_indices.append(i)
        
        analysis = {
            'relevant_indices': set(relevant_indices),
            'irrelevant_indices': set(irrelevant_indices),
            'relevant_count': len(relevant_indices),
            'irrelevant_count': len(irrelevant_indices)
        }
        
        logger.info(f"Relevant entries: {analysis['relevant_count']:,}")
        logger.info(f"Irrelevant entries: {analysis['irrelevant_count']:,}")
        
        return analysis
    
    def generate_cleaning_plan(self, duplicate_analysis: Dict, relevance_analysis: Dict) -> Set[int]:
        """Generate indices of entries to remove."""
        logger.info("Generating CORRECTED cleaning plan...")
        
        indices_to_remove = set()
        
        # Step 1: Handle duplicates - keep base-level, remove subfolder
        for unique_key, duplicate_entries in duplicate_analysis['duplicate_groups'].items():
            base_level_entries = []
            subfolder_entries = []
            
            for entry in duplicate_entries:
                if entry['index'] in duplicate_analysis['base_level_indices']:
                    base_level_entries.append(entry)
                else:
                    subfolder_entries.append(entry)
            
            # Keep base-level, remove subfolder
            if base_level_entries:
                # Keep the first base-level entry, remove the rest
                for entry in base_level_entries[1:]:
                    indices_to_remove.add(entry['index'])
                for entry in subfolder_entries:
                    indices_to_remove.add(entry['index'])
            else:
                # No base-level entries, keep the first subfolder entry
                for entry in subfolder_entries[1:]:
                    indices_to_remove.add(entry['index'])
        
        # Step 2: Remove irrelevant entries (but don't double-count)
        irrelevant_to_remove = relevance_analysis['irrelevant_indices'] - indices_to_remove
        indices_to_remove.update(irrelevant_to_remove)
        
        logger.info(f"Total entries to remove: {len(indices_to_remove):,}")
        
        return indices_to_remove
    
    def create_cleaned_dataset(self, indices_to_remove: Set[int]) -> List[Tuple]:
        """Create the cleaned dataset by removing specified indices."""
        logger.info("Creating cleaned dataset...")
        
        cleaned_dataset = []
        
        for i, entry in enumerate(self.dataset):
            if i not in indices_to_remove:
                cleaned_dataset.append(entry)
        
        logger.info(f"Cleaned dataset size: {len(cleaned_dataset):,}")
        
        return cleaned_dataset
    
    def save_cleaned_dataset(self, cleaned_dataset: List[Tuple]) -> bool:
        """Save the cleaned dataset to file."""
        try:
            # Create backup if output file exists
            if os.path.exists(self.output_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{self.output_path}.backup_{timestamp}"
                os.rename(self.output_path, backup_path)
                logger.info(f"Created backup: {backup_path}")
            
            # Save cleaned dataset
            logger.info(f"Saving cleaned dataset to {self.output_path}...")
            with open(self.output_path, 'wb') as f:
                dill.dump(cleaned_dataset, f)
            
            logger.info(f"Successfully saved {len(cleaned_dataset):,} entries")
            return True
            
        except Exception as e:
            logger.error(f"Error saving cleaned dataset: {e}")
            return False
    
    def print_cleaning_summary(self, duplicate_analysis: Dict, relevance_analysis: Dict, indices_to_remove: Set[int], final_size: int):
        """Print a comprehensive cleaning summary."""
        logger.info("\n" + "="*80)
        logger.info("CORRECTED DATASET CLEANING SUMMARY")
        logger.info("="*80)
        
        original_size = duplicate_analysis['total_entries']
        removed_count = len(indices_to_remove)
        reduction_percent = (removed_count / original_size) * 100
        
        logger.info(f"\nORIGINAL DATASET:")
        logger.info(f"  Total entries: {original_size:,}")
        logger.info(f"  Unique keys (date_folder/filename): {duplicate_analysis['unique_keys']:,}")
        logger.info(f"  Duplicate groups: {duplicate_analysis['duplicate_count']:,}")
        logger.info(f"  Base-level images: {duplicate_analysis['base_level_count']:,}")
        logger.info(f"  Subfolder images: {duplicate_analysis['subfolder_count']:,}")
        
        logger.info(f"\nRELEVANCE ANALYSIS:")
        logger.info(f"  Relevant entries (1girl/2girls): {relevance_analysis['relevant_count']:,}")
        logger.info(f"  Irrelevant entries: {relevance_analysis['irrelevant_count']:,}")
        
        logger.info(f"\nCORRECTED CLEANING RESULTS:")
        logger.info(f"  Entries removed: {removed_count:,}")
        logger.info(f"  Final dataset size: {final_size:,}")
        logger.info(f"  Reduction: {reduction_percent:.1f}%")
        
        # Calculate breakdown of removals
        duplicate_removals = 0
        for unique_key, duplicate_entries in duplicate_analysis['duplicate_groups'].items():
            duplicate_removals += len(duplicate_entries) - 1  # Keep one, remove the rest
        
        irrelevant_removals = len(relevance_analysis['irrelevant_indices'])
        overlap = duplicate_removals + irrelevant_removals - removed_count
        
        logger.info(f"\nREMOVAL BREAKDOWN:")
        logger.info(f"  Duplicate removals: {duplicate_removals:,}")
        logger.info(f"  Irrelevant removals: {irrelevant_removals:,}")
        logger.info(f"  Overlap (duplicates that were also irrelevant): {overlap:,}")
        
        # Show improvement over incorrect method
        incorrect_unique_count = 16151  # From previous incorrect analysis
        correct_unique_count = duplicate_analysis['unique_keys']
        improvement = correct_unique_count - incorrect_unique_count
        
        logger.info(f"\nCORRECTION IMPACT:")
        logger.info(f"  Incorrect method unique count: {incorrect_unique_count:,}")
        logger.info(f"  Corrected method unique count: {correct_unique_count:,}")
        logger.info(f"  Additional unique images recovered: {improvement:,}")
    
    def clean_dataset(self) -> bool:
        """Run the complete CORRECTED dataset cleaning process."""
        logger.info("Starting CORRECTED dataset cleaning process...")
        
        # Load dataset
        if not self.load_dataset():
            return False
        
        # Analyze duplicates and relevance with CORRECTED logic
        duplicate_analysis = self.analyze_duplicates()
        relevance_analysis = self.analyze_relevance()
        
        # Generate cleaning plan
        indices_to_remove = self.generate_cleaning_plan(duplicate_analysis, relevance_analysis)
        
        # Create cleaned dataset
        cleaned_dataset = self.create_cleaned_dataset(indices_to_remove)
        
        # Save cleaned dataset
        if not self.save_cleaned_dataset(cleaned_dataset):
            return False
        
        # Print summary
        self.print_cleaning_summary(duplicate_analysis, relevance_analysis, indices_to_remove, len(cleaned_dataset))
        
        logger.info("\nCORRECTED dataset cleaning completed successfully!")
        return True


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="CORRECTED dataset cleaning with proper unique key logic")
    parser.add_argument("--input", default="production_dataset.pkl", help="Input dataset pickle file")
    parser.add_argument("--output", default="corrected_cleaned_dataset.pkl", help="Output cleaned dataset pickle file")
    
    args = parser.parse_args()
    
    cleaner = CorrectedDatasetCleaner(args.input, args.output)
    success = cleaner.clean_dataset()
    
    if success:
        logger.info("CORRECTED dataset cleaning completed successfully!")
        return 0
    else:
        logger.error("CORRECTED dataset cleaning failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())

import argparse
import os
import torch
from transformers import AutoTokenizer

from models import ResNetModel, LightGBMModel, LLMWrapper
from mcts import MCTS
from environment import PromptEnvironment
from data import load_data, RewardModel
from train import train_resnet, train_lightgbm
from evaluate import evaluate_model, compare_models

def main():
    parser = argparse.ArgumentParser(description="AlphaZero-style Prompt Generation")
    parser.add_argument("--mode", type=str, required=True, choices=["train", "evaluate", "generate"],
                        help="Operation mode")
    parser.add_argument("--data_path", type=str, help="Path to prompt dataset")
    parser.add_argument("--model_type", type=str, default="resnet", choices=["resnet", "lightgbm", "llm"], 
                        help="Type of model to train/evaluate/use")
    parser.add_argument("--use_mcts", action="store_true", help="Use MCTS for generation")
    parser.add_argument("--epochs", type=int, default=10, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=32, help="Batch size for training")
    parser.add_argument("--lr", type=float, default=0.001, help="Learning rate")
    parser.add_argument("--max_length", type=int, default=50, help="Maximum prompt length")
    parser.add_argument("--num_prompts", type=int, default=10, help="Number of prompts to generate/evaluate")
    parser.add_argument("--temperature", type=float, default=0.8, help="Sampling temperature")
    parser.add_argument("--model_dir", type=str, default="./output", help="Directory for model files")
    parser.add_argument("--output_file", type=str, default="results.json", help="Output file for results")
    parser.add_argument("--llm_model", type=str, default="gpt2", help="Pretrained LLM model name")
    parser.add_argument("--initial_prompt", type=str, default="", help="Initial prompt text for generation")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.model_dir, exist_ok=True)
    
    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained(args.llm_model)
    
    # Initialize reward model
    reward_model = RewardModel()
    
    # Initialize environment
    env = PromptEnvironment(tokenizer, reward_model, args.max_length)
    
    # Device configuration
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    if args.mode == "train":
        if not args.data_path:
            raise ValueError("--data_path is required for training mode")
            
        # Load data
        train_loader, val_loader = load_data(
            args.data_path, tokenizer, args.batch_size, args.max_length
        )
        
        if args.model_type == "resnet":
            input_dim = args.max_length * tokenizer.vocab_size
            model = ResNetModel(input_dim)
            model = train_resnet(model, train_loader, val_loader, args.epochs, args.lr, device)
            torch.save(model.state_dict(), os.path.join(args.model_dir, "resnet_model.pt"))
            print(f"ResNet model saved to {os.path.join(args.model_dir, 'resnet_model.pt')}")
        
        elif args.model_type == "lightgbm":
            model = train_lightgbm(train_loader, val_loader)
            # Save LightGBM model
            model_path = os.path.join(args.model_dir, "lightgbm_model")
            os.makedirs(model_path, exist_ok=True)
            model.policy_model.save_model(os.path.join(model_path, "policy_model.txt"))
            model.value_model.save_model(os.path.join(model_path, "value_model.txt"))
            print(f"LightGBM model saved to {model_path}")
        
        elif args.model_type == "llm":
            print("No training needed for pretrained LLM")
            model = LLMWrapper(args.llm_model, device)
    
    elif args.mode == "evaluate":
        # Load models
        models = {}
        
        if args.model_type == "resnet":
            resnet_path = os.path.join(args.model_dir, "resnet_model.pt")
            if os.path.exists(resnet_path):
                input_dim = args.max_length * tokenizer.vocab_size
                model = ResNetModel(input_dim).to(device)
                model.load_state_dict(torch.load(resnet_path, map_location=device))
                model.eval()
                models["ResNet"] = model
            else:
                raise FileNotFoundError(f"ResNet model not found at {resnet_path}")
        
        elif args.model_type == "lightgbm":
            lightgbm_path = os.path.join(args.model_dir, "lightgbm_model")
            if os.path.exists(lightgbm_path):
                model = LightGBMModel()
                # Load LightGBM model
                import lightgbm as lgb
                model.policy_model = lgb.Booster(model_file=os.path.join(lightgbm_path, "policy_model.txt"))
                model.value_model = lgb.Booster(model_file=os.path.join(lightgbm_path, "value_model.txt"))
                models["LightGBM"] = model
            else:
                raise FileNotFoundError(f"LightGBM model not found at {lightgbm_path}")
        
        elif args.model_type == "llm":
            model = LLMWrapper(args.llm_model, device)
            models["LLM"] = model
        
        # Evaluate models
        if args.use_mcts:
            print(f"Evaluating {args.model_type} with MCTS")
            prompts, rewards, avg_reward = evaluate_model(
                models[args.model_type], env, args.num_prompts, use_mcts=True, temperature=args.temperature
            )
        else:
            print(f"Evaluating {args.model_type} without MCTS")
            prompts, rewards, avg_reward = evaluate_model(
                models[args.model_type], env, args.num_prompts, use_mcts=False, temperature=args.temperature
            )
        
        # Save results
        results = {
            "model_type": args.model_type,
            "use_mcts": args.use_mcts,
            "temperature": args.temperature,
            "average_reward": avg_reward,
            "prompts": prompts,
            "rewards": rewards
        }
        
        import json
        with open(args.output_file, 'w') as f:
            json.dump(results, f, indent=4)
        
        print(f"Evaluation results saved to {args.output_file}")
    
    elif args.mode == "generate":
        # Load model
        if args.model_type == "resnet":
            resnet_path = os.path.join(args.model_dir, "resnet_model.pt")
            if os.path.exists(resnet_path):
                input_dim = args.max_length * tokenizer.vocab_size
                model = ResNetModel(input_dim).to(device)
                model.load_state_dict(torch.load(resnet_path, map_location=device))
                model.eval()
            else:
                raise FileNotFoundError(f"ResNet model not found at {resnet_path}")
        
        elif args.model_type == "lightgbm":
            lightgbm_path = os.path.join(args.model_dir, "lightgbm_model")
            if os.path.exists(lightgbm_path):
                model = LightGBMModel()
                # Load LightGBM model
                import lightgbm as lgb
                model.policy_model = lgb.Booster(model_file=os.path.join(lightgbm_path, "policy_model.txt"))
                model.value_model = lgb.Booster(model_file=os.path.join(lightgbm_path, "value_model.txt"))
            else:
                raise FileNotFoundError(f"LightGBM model not found at {lightgbm_path}")
        
        elif args.model_type == "llm":
            model = LLMWrapper(args.llm_model, device)
        
        # Generate prompts
        print(f"Generating {args.num_prompts} prompts using {args.model_type}")
        
        prompts = []
        for i in range(args.num_prompts):
            if args.use_mcts:
                mcts = MCTS(model, num_simulations=100)
                prompt = env.generate_prompt(model, mcts=mcts, initial_prompt=args.initial_prompt, temperature=args.temperature)
            else:
                prompt = env.generate_prompt(model, initial_prompt=args.initial_prompt, temperature=args.temperature)
            
            prompts.append(prompt)
            print(f"Prompt {i+1}: {prompt}")
            print(f"Reward: {reward_model.evaluate(prompt)}")
            print("-" * 50)
        
        # Save generated prompts
        results = {
            "model_type": args.model_type,
            "use_mcts": args.use_mcts,
            "temperature": args.temperature,
            "initial_prompt": args.initial_prompt,
            "prompts": prompts
        }
        
        import json
        with open(args.output_file, 'w') as f:
            json.dump(results, f, indent=4)
        
        print(f"Generated prompts saved to {args.output_file}")

if __name__ == "__main__":
    main()

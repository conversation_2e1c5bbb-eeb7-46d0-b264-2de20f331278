#!/usr/bin/env python3
"""
Demo script for enhanced prompt extraction capabilities.

This script demonstrates the enhanced prompt extraction on real images from the dataset,
showing the difference between basic and enhanced extraction.
"""

import os
import sys
import dill
from PIL import Image
from pathlib import Path

# Add src directory to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)
from utils import image_info


def demo_enhanced_extraction(pkl_file: str = "promptlabels.pkl", num_samples: int = 10):
    """
    Demonstrate enhanced extraction on real dataset samples.
    
    Args:
        pkl_file: Path to the promptlabels.pkl file
        num_samples: Number of samples to demonstrate
    """
    print("Enhanced Prompt Extraction Demo")
    print("="*60)
    
    if not os.path.exists(pkl_file):
        print(f"❌ Dataset file not found: {pkl_file}")
        print("Please run the data cleaning script first:")
        print("  python run_data_cleaning.py --run")
        return False
    
    # Load dataset
    with open(pkl_file, 'rb') as f:
        data = dill.load(f)
    
    print(f"Loaded dataset with {len(data)} entries")
    print(f"Demonstrating enhanced extraction on {num_samples} samples...\n")
    
    enhanced_count = 0
    total_processed = 0
    
    for i, (filename, original_prompt, quality) in enumerate(data[:num_samples]):
        print(f"Sample {i+1}/{num_samples}")
        print("-" * 40)
        print(f"File: {Path(filename).name}")
        print(f"Quality: {quality}")
        
        if not os.path.exists(filename):
            print(f"⚠️  File not found: {filename}")
            print()
            continue
        
        try:
            with Image.open(filename) as img:
                # Basic extraction
                basic_info = image_info(img, filename, enhanced_prompts=False)
                basic_prompt = basic_info.get('prompt', '')
                
                # Enhanced extraction
                enhanced_info = image_info(img, filename, enhanced_prompts=True)
                enhanced_prompt = enhanced_info.get('prompt', '')
                workflow_params = enhanced_info.get('workflow_params', {})
                
                print(f"Basic prompt: {basic_prompt[:100]}{'...' if len(basic_prompt) > 100 else ''}")
                print(f"Enhanced prompt: {enhanced_prompt[:100]}{'...' if len(enhanced_prompt) > 100 else ''}")
                
                if workflow_params:
                    enhanced_count += 1
                    print("✓ Workflow parameters extracted:")
                    for key, value in workflow_params.items():
                        if key not in ['positive_prompt', 'negative_prompt']:  # Skip long text
                            print(f"  {key}: {value}")
                    
                    # Show enhancement details
                    enhancement_flags = []
                    if '--cfg' in enhanced_prompt:
                        enhancement_flags.append('CFG')
                    if '--steps' in enhanced_prompt:
                        enhancement_flags.append('Steps')
                    if '--size' in enhanced_prompt:
                        enhancement_flags.append('Size')
                    if '--lora' in enhanced_prompt:
                        enhancement_flags.append('LoRA')
                    if '--sampler' in enhanced_prompt:
                        enhancement_flags.append('Sampler')
                    
                    if enhancement_flags:
                        print(f"  Enhanced with: {', '.join(enhancement_flags)}")
                else:
                    print("ℹ️  No workflow parameters found")
                
                total_processed += 1
                
        except Exception as e:
            print(f"❌ Error processing image: {e}")
        
        print()
    
    # Summary
    print("="*60)
    print("DEMO SUMMARY")
    print("="*60)
    print(f"Total samples processed: {total_processed}")
    print(f"Enhanced extractions: {enhanced_count}")
    if total_processed > 0:
        success_rate = enhanced_count / total_processed * 100
        print(f"Enhancement success rate: {success_rate:.1f}%")
    
    return True


def compare_extraction_methods():
    """Compare basic vs enhanced extraction on a few examples."""
    print("\nExtraction Method Comparison")
    print("="*60)
    
    # Load a few samples
    with open("promptlabels.pkl", 'rb') as f:
        data = dill.load(f)
    
    # Find samples with different qualities
    good_samples = [(f, p, q) for f, p, q in data if q == 'good'][:3]
    normal_samples = [(f, p, q) for f, p, q in data if q == 'normal'][:3]
    
    print("Comparing Good vs Normal Quality Prompts:")
    print("-" * 40)
    
    for category, samples in [("GOOD", good_samples), ("NORMAL", normal_samples)]:
        print(f"\n{category} Quality Samples:")
        
        for i, (filename, _, quality) in enumerate(samples):
            if not os.path.exists(filename):
                continue
                
            try:
                with Image.open(filename) as img:
                    enhanced_info = image_info(img, filename, enhanced_prompts=True)
                    enhanced_prompt = enhanced_info.get('prompt', '')
                    workflow_params = enhanced_info.get('workflow_params', {})
                    
                    print(f"\n  Sample {i+1}: {Path(filename).name}")
                    
                    # Show key parameters
                    if workflow_params:
                        key_params = []
                        if 'cfg' in workflow_params:
                            key_params.append(f"CFG:{workflow_params['cfg']}")
                        if 'steps' in workflow_params:
                            key_params.append(f"Steps:{workflow_params['steps']}")
                        if 'width' in workflow_params and 'height' in workflow_params:
                            key_params.append(f"Size:{workflow_params['width']}x{workflow_params['height']}")
                        if 'loras' in workflow_params:
                            key_params.append(f"LoRAs:{len(workflow_params['loras'])}")
                        
                        if key_params:
                            print(f"    Parameters: {', '.join(key_params)}")
                        else:
                            print(f"    Parameters: None extracted")
                    else:
                        print(f"    Parameters: No workflow data")
                    
                    # Show prompt preview
                    prompt_preview = enhanced_prompt[:80] + "..." if len(enhanced_prompt) > 80 else enhanced_prompt
                    print(f"    Prompt: {prompt_preview}")
                    
            except Exception as e:
                print(f"    Error: {e}")


def analyze_parameter_distribution():
    """Analyze the distribution of extracted parameters across the dataset."""
    print("\nParameter Distribution Analysis")
    print("="*60)
    
    # Load dataset
    with open("promptlabels.pkl", 'rb') as f:
        data = dill.load(f)
    
    # Sample for analysis (to avoid processing all 66k images)
    import random
    random.seed(42)
    sample_data = random.sample(data, min(500, len(data)))
    
    print(f"Analyzing parameter distribution on {len(sample_data)} samples...")
    
    param_stats = {
        'has_cfg': 0,
        'has_steps': 0,
        'has_size': 0,
        'has_lora': 0,
        'has_sampler': 0,
        'cfg_values': [],
        'steps_values': [],
        'lora_counts': [],
        'aspect_ratios': []
    }
    
    processed = 0
    
    for filename, _, quality in sample_data:
        if not os.path.exists(filename):
            continue
            
        try:
            with Image.open(filename) as img:
                info = image_info(img, filename, enhanced_prompts=True)
                workflow_params = info.get('workflow_params', {})
                
                if workflow_params:
                    if 'cfg' in workflow_params:
                        param_stats['has_cfg'] += 1
                        param_stats['cfg_values'].append(workflow_params['cfg'])
                    
                    if 'steps' in workflow_params:
                        param_stats['has_steps'] += 1
                        param_stats['steps_values'].append(workflow_params['steps'])
                    
                    if 'width' in workflow_params and 'height' in workflow_params:
                        param_stats['has_size'] += 1
                        aspect_ratio = workflow_params['width'] / workflow_params['height']
                        param_stats['aspect_ratios'].append(aspect_ratio)
                    
                    if 'loras' in workflow_params:
                        param_stats['has_lora'] += 1
                        param_stats['lora_counts'].append(len(workflow_params['loras']))
                    
                    if 'sampler_name' in workflow_params:
                        param_stats['has_sampler'] += 1
                
                processed += 1
                
        except Exception:
            continue
    
    # Print statistics
    print(f"\nProcessed {processed} images successfully")
    print(f"Parameter presence rates:")
    print(f"  CFG: {param_stats['has_cfg']}/{processed} ({param_stats['has_cfg']/processed*100:.1f}%)")
    print(f"  Steps: {param_stats['has_steps']}/{processed} ({param_stats['has_steps']/processed*100:.1f}%)")
    print(f"  Size: {param_stats['has_size']}/{processed} ({param_stats['has_size']/processed*100:.1f}%)")
    print(f"  LoRA: {param_stats['has_lora']}/{processed} ({param_stats['has_lora']/processed*100:.1f}%)")
    print(f"  Sampler: {param_stats['has_sampler']}/{processed} ({param_stats['has_sampler']/processed*100:.1f}%)")
    
    # Parameter value statistics
    if param_stats['cfg_values']:
        cfg_avg = sum(param_stats['cfg_values']) / len(param_stats['cfg_values'])
        print(f"\nCFG values: avg={cfg_avg:.1f}, range={min(param_stats['cfg_values'])}-{max(param_stats['cfg_values'])}")
    
    if param_stats['steps_values']:
        steps_avg = sum(param_stats['steps_values']) / len(param_stats['steps_values'])
        print(f"Steps values: avg={steps_avg:.1f}, range={min(param_stats['steps_values'])}-{max(param_stats['steps_values'])}")
    
    if param_stats['lora_counts']:
        lora_avg = sum(param_stats['lora_counts']) / len(param_stats['lora_counts'])
        print(f"LoRA count: avg={lora_avg:.1f}, max={max(param_stats['lora_counts'])}")


def main():
    """Main demo function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Demo enhanced prompt extraction")
    parser.add_argument('--samples', type=int, default=10, help='Number of samples to demo')
    parser.add_argument('--compare', action='store_true', help='Compare good vs normal quality')
    parser.add_argument('--analyze', action='store_true', help='Analyze parameter distribution')
    parser.add_argument('--data', default='promptlabels.pkl', help='Dataset file')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.data):
        print(f"❌ Dataset file not found: {args.data}")
        print("Please run the data cleaning script first:")
        print("  python run_data_cleaning.py --run")
        return False
    
    # Run demo
    success = demo_enhanced_extraction(args.data, args.samples)
    
    if args.compare and success:
        compare_extraction_methods()
    
    if args.analyze and success:
        analyze_parameter_distribution()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

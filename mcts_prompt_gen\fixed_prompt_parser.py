#!/usr/bin/env python3
"""
Fixed Prompt Parser - Addresses Critical Parsing Issues

This module fixes the critical parsing errors identified in the comprehensive analysis:
1. Incorrect LoRA classification (artist tags vs actual LoRA models)
2. Text encoding/decoding issues (literal \n, Unicode escapes)
3. Inconsistent tag normalization (underscore/space handling)
4. Complex bracket weight parsing failures
5. Escape character handling

Author: AI Assistant
Date: 2025-06-24
"""

import re
import json
import logging
from typing import List, Tuple, Dict, Any, Optional
from collections import defaultdict, Counter
import unicodedata

logger = logging.getLogger(__name__)


class FixedPromptParser:
    """Fixed prompt parser addressing critical parsing issues."""
    
    def __init__(self):
        """Initialize the fixed parser with corrected patterns and logic."""
        
        # Fixed weight syntax patterns - handle nested brackets properly
        self.weight_patterns = {
            # Explicit weights: (tag:1.2)
            'explicit': re.compile(r'\(([^:)]+):([0-9.]+)\)'),
            
            # Curly braces - handle nested properly
            'curly_triple': re.compile(r'\{\{\{([^}]+)\}\}\}'),  # {{{tag}}} = 1.3
            'curly_double': re.compile(r'\{\{([^}]+)\}\}'),      # {{tag}} = 1.2  
            'curly_single': re.compile(r'\{([^}]+)\}'),          # {tag} = 1.1
            
            # Square brackets - handle nested properly
            'square_double': re.compile(r'\[\[([^\]]+)\]\]'),    # [[tag]] = 0.8
            'square_single': re.compile(r'\[([^\]]+)\]'),        # [tag] = 0.9
            
            # Complex nested expressions like (tag1)[tag2] or [tag1, tag2](tag3)
            'complex_nested': re.compile(r'(\([^)]+\))(\[[^\]]+\])|(\[[^\]]+\])(\([^)]+\))')
        }
        
        # Fixed technical parameter patterns - only match actual ComfyUI parameters
        self.tech_param_patterns = {
            'cfg': re.compile(r'--cfg\s+([0-9.]+)'),
            'steps': re.compile(r'--steps\s+([0-9]+)'),
            'size': re.compile(r'--size\s+([0-9]+)x([0-9]+)'),
            'sampler': re.compile(r'--sampler\s+([a-zA-Z_]+)'),
            'scheduler': re.compile(r'--scheduler\s+([a-zA-Z_]+)'),
            'seed': re.compile(r'--seed\s+([0-9]+)'),
            # CRITICAL FIX: Only match actual LoRA file parameters, not artist tags
            # FIXED: Support negative weights like -1.0
            'lora': re.compile(r'--lora\s+([a-zA-Z0-9_.-]+\.safetensors):(-?[0-9.]+)')
        }
        
        # Tag splitting patterns - handle multiple delimiters
        self.split_patterns = [
            re.compile(r'[,，；;]\s*'),  # Comma, full-width comma, semicolon
            re.compile(r'\n+'),          # Newlines (actual newlines, not literal \n)
            re.compile(r'\s{2,}')        # Multiple spaces
        ]
        
        # Statistics tracking
        self.parsing_stats = defaultdict(int)
        self.error_log = []
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text to fix encoding/decoding issues.
        
        Args:
            text: Raw text that may contain encoding issues
            
        Returns:
            Properly decoded and normalized text
        """
        if not isinstance(text, str):
            return str(text)
        
        try:
            # Fix literal \n strings to actual newlines
            text = text.replace('\\n', '\n')
            
            # Fix Unicode escape sequences like \u00d7
            text = text.encode('utf-8').decode('unicode_escape')
            
            # Normalize Unicode characters
            text = unicodedata.normalize('NFKC', text)
            
            # Fix common encoding artifacts
            text = text.replace('\\(', '(').replace('\\)', ')')
            text = text.replace('\\[', '[').replace('\\]', ']')
            text = text.replace('\\{', '{').replace('\\}', '}')
            
            # Only keep backslashes that are actually escaping brackets in content
            # Remove unnecessary backslashes
            text = re.sub(r'\\(?![()[\]{}])', '', text)
            
            self.parsing_stats['text_preprocessing_success'] += 1
            return text
            
        except Exception as e:
            logger.warning(f"Text preprocessing failed: {e}")
            self.parsing_stats['text_preprocessing_errors'] += 1
            return text
    
    def normalize_tag(self, tag: str) -> str:
        """
        Normalize tag with consistent underscore/space handling.
        
        Args:
            tag: Raw tag string
            
        Returns:
            Normalized tag string
        """
        if not isinstance(tag, str):
            return str(tag)
        
        # Step 1: Clean the tag
        tag = tag.strip()
        if not tag:
            return ""
        
        # Step 2: Replace underscores with spaces temporarily for processing
        tag = tag.replace('_', ' ')
        
        # Step 3: Normalize whitespace
        tag = re.sub(r'\s+', ' ', tag).strip()
        
        # Step 4: Convert back to underscores for final format
        tag = tag.replace(' ', '_')
        
        # Step 5: Remove any remaining problematic characters
        tag = re.sub(r'[^\w\-:.]', '_', tag)
        
        # Step 6: Clean up multiple underscores
        tag = re.sub(r'_+', '_', tag).strip('_')
        
        return tag.lower()
    
    def parse_complex_brackets(self, text: str) -> Tuple[List[Tuple[str, float]], str]:
        """
        Parse complex bracket expressions like (tag1)[tag2] or [tag1, tag2](tag3).
        
        Args:
            text: Text containing complex bracket expressions
            
        Returns:
            Tuple of (parsed tags, remaining text)
        """
        tags_with_weights = []
        processed_text = text
        
        # Handle complex nested expressions
        for match in self.weight_patterns['complex_nested'].finditer(text):
            full_match = match.group(0)
            
            # Parse each part of the complex expression
            parts = re.findall(r'\(([^)]+)\)|\[([^\]]+)\]', full_match)
            
            for pos_part, neg_part in parts:
                if pos_part:  # Positive bracket (tag) or (tag:weight)
                    if ':' in pos_part:
                        tag, weight_str = pos_part.rsplit(':', 1)
                        try:
                            weight = float(weight_str)
                            tags_with_weights.append((self.normalize_tag(tag), weight))
                        except ValueError:
                            tags_with_weights.append((self.normalize_tag(pos_part), 1.0))
                    else:
                        tags_with_weights.append((self.normalize_tag(pos_part), 1.0))
                
                if neg_part:  # Negative bracket [tag]
                    # Split by comma if multiple tags in brackets
                    sub_tags = [t.strip() for t in neg_part.split(',') if t.strip()]
                    for sub_tag in sub_tags:
                        tags_with_weights.append((self.normalize_tag(sub_tag), 0.9))
            
            # Remove the processed part
            processed_text = processed_text.replace(full_match, '', 1)
        
        return tags_with_weights, processed_text
    
    def parse_weight_syntax(self, text: str) -> Tuple[List[Tuple[str, float]], str]:
        """
        Parse all weight syntax patterns from text.
        
        Args:
            text: Text containing weight syntax
            
        Returns:
            Tuple of (parsed tags with weights, remaining text)
        """
        tags_with_weights = []
        processed_text = text
        
        # Step 1: Handle complex nested expressions first
        complex_tags, processed_text = self.parse_complex_brackets(processed_text)
        tags_with_weights.extend(complex_tags)
        
        # Step 2: Handle explicit weights: (tag:1.2)
        for match in self.weight_patterns['explicit'].finditer(processed_text):
            tag = match.group(1).strip()
            try:
                weight = float(match.group(2))
                tags_with_weights.append((self.normalize_tag(tag), weight))
                processed_text = processed_text.replace(match.group(0), '', 1)
            except ValueError:
                tags_with_weights.append((self.normalize_tag(tag), 1.0))
                processed_text = processed_text.replace(match.group(0), '', 1)
        
        # Step 3: Handle nested curly braces (process from most nested to least)
        for pattern_name in ['curly_triple', 'curly_double', 'curly_single']:
            pattern = self.weight_patterns[pattern_name]
            weight_map = {'curly_triple': 1.3, 'curly_double': 1.2, 'curly_single': 1.1}
            
            for match in pattern.finditer(processed_text):
                tag = match.group(1).strip()
                # Handle multiple tags within single brackets
                sub_tags = [t.strip() for t in tag.split(',') if t.strip()]
                for sub_tag in sub_tags:
                    tags_with_weights.append((self.normalize_tag(sub_tag), weight_map[pattern_name]))
                processed_text = processed_text.replace(match.group(0), '', 1)
        
        # Step 4: Handle square brackets (process from most nested to least)
        for pattern_name in ['square_double', 'square_single']:
            pattern = self.weight_patterns[pattern_name]
            weight_map = {'square_double': 0.8, 'square_single': 0.9}
            
            for match in pattern.finditer(processed_text):
                tag = match.group(1).strip()
                # Handle multiple tags within single brackets
                sub_tags = [t.strip() for t in tag.split(',') if t.strip()]
                for sub_tag in sub_tags:
                    tags_with_weights.append((self.normalize_tag(sub_tag), weight_map[pattern_name]))
                processed_text = processed_text.replace(match.group(0), '', 1)
        
        return tags_with_weights, processed_text
    
    def extract_technical_parameters(self, text: str) -> Tuple[Dict[str, Any], str]:
        """
        Extract ONLY actual ComfyUI technical parameters, not artist style tags.
        
        Args:
            text: Text that may contain technical parameters
            
        Returns:
            Tuple of (technical parameters dict, remaining text)
        """
        tech_params = {}
        cleaned_text = text
        
        # Extract each type of technical parameter
        for param_name, pattern in self.tech_param_patterns.items():
            matches = pattern.findall(text)
            
            if param_name == 'size' and matches:
                # Handle size specially (width x height)
                width, height = matches[0]
                tech_params['width'] = int(width)
                tech_params['height'] = int(height)
                tech_params['aspect_ratio'] = int(width) / int(height)
                tech_params['megapixels'] = (int(width) * int(height)) / 1000000
                
            elif param_name == 'lora' and matches:
                # CRITICAL FIX: Only extract actual LoRA file parameters
                loras = []
                for filename, weight_str in matches:
                    try:
                        weight = float(weight_str)
                        loras.append((filename, weight))
                    except ValueError:
                        loras.append((filename, 1.0))
                tech_params['loras'] = loras
                
            elif matches:
                # Handle single-value parameters
                try:
                    if param_name in ['cfg', 'seed']:
                        tech_params[param_name] = float(matches[0])
                    elif param_name == 'steps':
                        tech_params[param_name] = int(matches[0])
                    else:
                        tech_params[param_name] = matches[0]
                except (ValueError, TypeError):
                    tech_params[param_name] = matches[0]
            
            # Remove matched parameters from text
            cleaned_text = pattern.sub('', cleaned_text)
        
        return tech_params, cleaned_text

    def split_into_tags(self, text: str) -> List[str]:
        """
        Split text into individual tags using multiple delimiters.

        Args:
            text: Text to split into tags

        Returns:
            List of individual tag strings
        """
        if not text or not text.strip():
            return []

        # Apply all splitting patterns
        parts = [text]
        for pattern in self.split_patterns:
            new_parts = []
            for part in parts:
                new_parts.extend(pattern.split(part))
            parts = new_parts

        # Clean and filter tags
        tags = []
        for part in parts:
            part = part.strip()
            if part and len(part) > 0:
                tags.append(part)

        return tags

    def parse_prompt_with_fixed_logic(self, prompt: str) -> List[Tuple[str, float]]:
        """
        Parse prompt with all fixes applied.

        Args:
            prompt: Raw prompt string

        Returns:
            List of (tag, weight) tuples
        """
        try:
            if not isinstance(prompt, str) or not prompt.strip():
                return []

            # Step 1: Preprocess text to fix encoding issues
            processed_text = self.preprocess_text(prompt)

            # Step 2: Extract technical parameters (ONLY actual ComfyUI params)
            tech_params, text_without_params = self.extract_technical_parameters(processed_text)

            # Step 3: Parse weight syntax from remaining text
            weighted_tags, remaining_text = self.parse_weight_syntax(text_without_params)

            # Step 4: Split remaining text into individual tags
            raw_tags = self.split_into_tags(remaining_text)

            # Step 5: Add unweighted tags with default weight
            for tag in raw_tags:
                normalized_tag = self.normalize_tag(tag)
                if normalized_tag and len(normalized_tag) > 0:
                    weighted_tags.append((normalized_tag, 1.0))

            # Step 6: Convert technical parameters to special tags
            tech_tags = []
            for param_name, param_value in tech_params.items():
                if param_name == 'loras':
                    # Handle LoRA list - ONLY actual LoRA files - PRESERVE ORIGINAL FILENAMES
                    for lora_filename, lora_weight in param_value:
                        clean_name = lora_filename.replace('.safetensors', '').replace('.ckpt', '')
                        # CRITICAL: Do NOT normalize LoRA filenames - preserve them exactly
                        tech_tags.append((f'lora_{clean_name}', lora_weight))
                elif param_name in ['cfg', 'steps', 'width', 'height', 'seed']:
                    # Numeric parameters
                    tag_name = f'{param_name}_value' if param_name in ['cfg', 'steps'] else param_name
                    tech_tags.append((tag_name, float(param_value)))
                elif param_name in ['sampler_name', 'scheduler', 'model']:
                    # String parameters
                    tech_tags.append((f'{param_name}_{self.normalize_tag(str(param_value))}', 1.0))

            # Step 7: Combine all tags
            all_tags = weighted_tags + tech_tags

            # Step 8: Deduplicate and validate
            final_tags = self.deduplicate_and_validate_tags(all_tags)

            self.parsing_stats['successful_parses'] += 1
            return final_tags

        except Exception as e:
            error_msg = f"Error parsing prompt '{prompt[:50]}...': {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            self.parsing_stats['parsing_errors'] += 1
            return [('parsing_error', 1.0)]

    def deduplicate_and_validate_tags(self, tags: List[Tuple[str, float]]) -> List[Tuple[str, float]]:
        """
        Deduplicate tags and validate weights.

        Args:
            tags: List of (tag, weight) tuples

        Returns:
            Cleaned and validated list of (tag, weight) tuples
        """
        tag_weights = defaultdict(list)

        # Group weights by tag
        for tag, weight in tags:
            # Validate tag
            if not isinstance(tag, str) or not tag.strip():
                continue

            # Validate weight
            try:
                weight = float(weight)
                # Clamp extreme weights to reasonable range
                if weight > 10.0:
                    weight = 2.0
                elif weight < -10.0:
                    weight = 0.1
            except (ValueError, TypeError):
                weight = 1.0

            tag_weights[tag].append(weight)

        # Combine weights (use average for multiple occurrences)
        final_tags = []
        for tag, weights in tag_weights.items():
            if len(weights) == 1:
                final_tags.append((tag, weights[0]))
            else:
                # Use average weight for duplicates
                avg_weight = sum(weights) / len(weights)
                final_tags.append((tag, round(avg_weight, 3)))

        return final_tags

    def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive parsing statistics."""
        total_attempts = (self.parsing_stats['successful_parses'] +
                         self.parsing_stats['parsing_errors'])

        return {
            'total_parsing_attempts': total_attempts,
            'successful_parses': self.parsing_stats['successful_parses'],
            'parsing_errors': self.parsing_stats['parsing_errors'],
            'text_preprocessing_success': self.parsing_stats['text_preprocessing_success'],
            'text_preprocessing_errors': self.parsing_stats['text_preprocessing_errors'],
            'parsing_accuracy': (self.parsing_stats['successful_parses'] / total_attempts * 100) if total_attempts > 0 else 0,
            'error_log_count': len(self.error_log),
            'recent_errors': self.error_log[-10:] if self.error_log else []
        }


def parse_prompt_with_weights_fixed(prompt_string: str) -> List[Tuple[str, float]]:
    """
    Fixed version of parse_prompt_with_weights function.

    This function replaces the problematic original version with proper:
    - LoRA extraction (only actual --lora parameters)
    - Text preprocessing (Unicode decoding, newline handling)
    - Tag normalization (consistent underscore/space handling)
    - Complex bracket parsing (nested expressions)

    Args:
        prompt_string: Enhanced prompt string

    Returns:
        List of (tag, weight) tuples
    """
    parser = FixedPromptParser()
    return parser.parse_prompt_with_fixed_logic(prompt_string)


def test_fixed_parser():
    """Test the fixed parser with problematic examples from the analysis."""
    parser = FixedPromptParser()

    test_cases = [
        # Test 1: Artist tags should NOT be classified as LoRA
        "1girl, (ciloranko:0.7), masterpiece",

        # Test 2: Actual LoRA should be extracted correctly
        "1girl, masterpiece --lora character.safetensors:0.8",

        # Test 3: Complex bracket expressions
        "(artist:mika pikazo)[artist:ciloranko]",

        # Test 4: Text encoding issues
        "1girl\\n\\nblue hair\\u00d7detailed",

        # Test 5: Tag normalization
        "floral_print, floral print, very_aesthetic",

        # Test 6: Multiple tags in brackets
        "{masterpiece, best quality}, [low quality, blurry]"
    ]

    print("Testing Fixed Parser:")
    print("=" * 60)

    for i, test_prompt in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_prompt}")
        result = parser.parse_prompt_with_fixed_logic(test_prompt)
        print(f"Result: {result}")

    print("\nParsing Statistics:")
    stats = parser.get_parsing_statistics()
    for key, value in stats.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    test_fixed_parser()

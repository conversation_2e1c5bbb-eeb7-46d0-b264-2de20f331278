#!/usr/bin/env python3
"""
Test Prompt Parsing and Visualize Statistics

This script tests the new prompt parsing functionality and visualizes statistics
on tags and weights to ensure they are correctly parsed and extracted.

Features:
- Test prompt parsing with various weight formats
- Validate LoRA extraction accuracy
- Generate comprehensive statistics and visualizations
- Verify data integrity of the new format
"""

import os
import sys
import dill
import json
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from collections import Counter, defaultdict
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import parse_prompt_with_weights


def test_prompt_parsing():
    """Test the prompt parsing function with various formats."""
    print("=" * 60)
    print("TESTING PROMPT PARSING FUNCTIONALITY")
    print("=" * 60)
    
    test_cases = [
        # Basic tags
        ("masterpiece, best quality, 1girl", [("masterpiece", 1.0), ("best quality", 1.0), ("1girl", 1.0)]),
        
        # Weighted tags
        ("{masterpiece}, [low quality], (detailed:1.2)", [("masterpiece", 1.1), ("low quality", 0.9), ("detailed", 1.2)]),
        
        # LoRA tags
        ("--lora character.safetensors:0.8, --lora style.safetensors:1.0", [("character.safetensors", 0.8), ("style.safetensors", 1.0)]),
        
        # Command line parameters
        ("--cfg 7.5, --steps 30, --width 1024", [("cfg", 7.5), ("steps", 30.0), ("width", 1024.0)]),
        
        # Mixed format
        ("1girl, {masterpiece}, --lora test.safetensors:0.9, --cfg 7.5, (detailed:1.3)", 
         [("1girl", 1.0), ("masterpiece", 1.1), ("test.safetensors", 0.9), ("cfg", 7.5), ("detailed", 1.3)]),
        
        # Complex real-world example
        ("1girl, masterpiece, best quality, {detailed face}, [blurry], --lora anime_style.safetensors:0.8, --lora character_girl.safetensors:1.0, --cfg 7.5, --steps 35, (beautiful eyes:1.2)",
         [("1girl", 1.0), ("masterpiece", 1.0), ("best quality", 1.0), ("detailed face", 1.1), ("blurry", 0.9), 
          ("anime_style.safetensors", 0.8), ("character_girl.safetensors", 1.0), ("cfg", 7.5), ("steps", 35.0), ("beautiful eyes", 1.2)])
    ]
    
    passed = 0
    failed = 0
    
    for i, (input_prompt, expected_output) in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        print(f"Input: {input_prompt}")
        
        actual_output = parse_prompt_with_weights(input_prompt)
        print(f"Expected: {expected_output}")
        print(f"Actual:   {actual_output}")
        
        if actual_output == expected_output:
            print("✅ PASSED")
            passed += 1
        else:
            print("❌ FAILED")
            failed += 1
    
    print(f"\n" + "=" * 60)
    print(f"PROMPT PARSING TEST RESULTS: {passed}/{passed + failed} passed")
    print("=" * 60)
    
    return passed == len(test_cases)


def analyze_dataset_statistics(dataset_file):
    """Analyze and visualize statistics from the dataset."""
    print("\n" + "=" * 60)
    print("DATASET STATISTICS ANALYSIS")
    print("=" * 60)
    
    # Load dataset
    try:
        with open(dataset_file, 'rb') as f:
            data = dill.load(f)
        print(f"Loaded dataset with {len(data)} entries")
    except Exception as e:
        print(f"Error loading dataset: {e}")
        return False
    
    # Analyze data structure
    print(f"\nData Structure Analysis:")
    if len(data) > 0:
        sample_entry = data[0]
        print(f"  Entry format: {type(sample_entry)}")
        print(f"  Entry length: {len(sample_entry)}")
        if len(sample_entry) >= 3:
            filename, prompt, goodness = sample_entry
            print(f"  Filename type: {type(filename)}")
            print(f"  Prompt type: {type(prompt)}")
            print(f"  Goodness type: {type(goodness)}")
            
            if isinstance(prompt, list) and len(prompt) > 0:
                print(f"  Prompt format: NEW FORMAT - List of {len(prompt)} (tag, weight) tuples")
                print(f"  Sample tags: {prompt[:3]}")
            else:
                print(f"  Prompt format: OLD FORMAT - String prompt")
                print(f"  Sample prompt: {str(prompt)[:100]}...")
    
    # Extract all tags and weights
    all_tags = []
    all_weights = []
    lora_tags = []
    parameter_tags = []
    regular_tags = []
    goodness_scores = []

    # Determine if this is old format (string prompts) or new format (list of tuples)
    old_format = isinstance(data[0][1], str) if len(data) > 0 else True

    for filename, prompt, goodness in data:
        # Handle goodness score (might be string or float)
        if isinstance(goodness, str):
            try:
                goodness_scores.append(float(goodness))
            except ValueError:
                goodness_scores.append(0.0)  # Default for invalid scores
        else:
            goodness_scores.append(goodness)

        # Handle prompt format
        if isinstance(prompt, list):
            # New format: list of (tag, weight) tuples
            for tag, weight in prompt:
                all_tags.append(tag)
                all_weights.append(weight)

                if tag.endswith('.safetensors'):
                    lora_tags.append((tag, weight))
                elif tag in ['cfg', 'steps', 'width', 'height', 'seed', 'sampler_name', 'scheduler']:
                    parameter_tags.append((tag, weight))
                else:
                    regular_tags.append((tag, weight))
        elif isinstance(prompt, str):
            # Old format: string prompt - parse it
            parsed_tags = parse_prompt_with_weights(prompt)
            for tag, weight in parsed_tags:
                all_tags.append(tag)
                all_weights.append(weight)

                if tag.endswith('.safetensors'):
                    lora_tags.append((tag, weight))
                elif tag in ['cfg', 'steps', 'width', 'height', 'seed', 'sampler_name', 'scheduler']:
                    parameter_tags.append((tag, weight))
                else:
                    regular_tags.append((tag, weight))
    
    # Generate statistics
    print(f"\nTag Statistics:")
    print(f"  Total tag instances: {len(all_tags)}")
    print(f"  Unique tags: {len(set(all_tags))}")
    print(f"  LoRA tags: {len(lora_tags)}")
    print(f"  Parameter tags: {len(parameter_tags)}")
    print(f"  Regular tags: {len(regular_tags)}")
    
    # Weight distribution
    weight_counter = Counter(all_weights)
    print(f"\nWeight Distribution:")
    print(f"  Unique weights: {len(weight_counter)}")
    print(f"  Most common weights:")
    for weight, count in weight_counter.most_common(10):
        print(f"    {weight}: {count}")
    
    # LoRA analysis
    if lora_tags:
        lora_names = [tag for tag, weight in lora_tags]
        lora_weights = [weight for tag, weight in lora_tags]
        lora_counter = Counter(lora_names)
        
        print(f"\nLoRA Analysis:")
        print(f"  Total LoRA instances: {len(lora_tags)}")
        print(f"  Unique LoRA models: {len(lora_counter)}")
        print(f"  Top 10 LoRAs:")
        for lora, count in lora_counter.most_common(10):
            print(f"    {lora}: {count}")
        
        print(f"  LoRA weight range: {min(lora_weights):.2f} - {max(lora_weights):.2f}")
        print(f"  Average LoRA weight: {sum(lora_weights)/len(lora_weights):.2f}")
    
    # Goodness score analysis
    print(f"\nGoodness Score Analysis:")
    print(f"  Score range: {min(goodness_scores):.2f} - {max(goodness_scores):.2f}")
    print(f"  Average score: {sum(goodness_scores)/len(goodness_scores):.2f}")
    print(f"  Score distribution:")
    goodness_counter = Counter(goodness_scores)
    for score in sorted(goodness_counter.keys()):
        print(f"    {score}: {goodness_counter[score]}")
    
    # Create visualizations
    create_visualizations(all_tags, all_weights, lora_tags, goodness_scores)
    
    return True


def create_visualizations(all_tags, all_weights, lora_tags, goodness_scores):
    """Create visualizations for the dataset statistics."""
    print(f"\nCreating visualizations...")
    
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create a figure with subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Dataset Statistics Visualization', fontsize=16)
    
    # 1. Weight distribution
    weight_counter = Counter(all_weights)
    weights, counts = zip(*weight_counter.most_common(20))
    axes[0, 0].bar(range(len(weights)), counts)
    axes[0, 0].set_title('Top 20 Weight Values Distribution')
    axes[0, 0].set_xlabel('Weight Value')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_xticks(range(len(weights)))
    axes[0, 0].set_xticklabels([f'{w:.1f}' for w in weights], rotation=45)
    
    # 2. LoRA weight distribution
    if lora_tags:
        lora_weights = [weight for tag, weight in lora_tags]
        axes[0, 1].hist(lora_weights, bins=20, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('LoRA Weight Distribution')
        axes[0, 1].set_xlabel('LoRA Weight')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].axvline(sum(lora_weights)/len(lora_weights), color='red', linestyle='--', label='Mean')
        axes[0, 1].legend()
    else:
        axes[0, 1].text(0.5, 0.5, 'No LoRA tags found', ha='center', va='center', transform=axes[0, 1].transAxes)
        axes[0, 1].set_title('LoRA Weight Distribution')
    
    # 3. Goodness score distribution
    axes[1, 0].hist(goodness_scores, bins=20, alpha=0.7, edgecolor='black')
    axes[1, 0].set_title('Goodness Score Distribution')
    axes[1, 0].set_xlabel('Goodness Score')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].axvline(sum(goodness_scores)/len(goodness_scores), color='red', linestyle='--', label='Mean')
    axes[1, 0].legend()
    
    # 4. Top tags frequency
    tag_counter = Counter(all_tags)
    top_tags, top_counts = zip(*tag_counter.most_common(15))
    axes[1, 1].barh(range(len(top_tags)), top_counts)
    axes[1, 1].set_title('Top 15 Most Frequent Tags')
    axes[1, 1].set_xlabel('Frequency')
    axes[1, 1].set_ylabel('Tags')
    axes[1, 1].set_yticks(range(len(top_tags)))
    axes[1, 1].set_yticklabels([tag[:20] + '...' if len(tag) > 20 else tag for tag in top_tags])
    
    plt.tight_layout()
    
    # Save the plot
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    plot_file = f'dataset_statistics_{timestamp}.png'
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"Visualizations saved to: {plot_file}")
    
    # Show the plot
    plt.show()


def main():
    """Main function."""
    print("Test Prompt Parsing and Visualize Statistics")
    print("=" * 60)
    
    # Test prompt parsing
    parsing_success = test_prompt_parsing()
    
    if not parsing_success:
        print("❌ Prompt parsing tests failed. Please fix the issues before proceeding.")
        return 1
    
    # Analyze dataset if it exists
    dataset_files = ['promptlabels_v2.pkl', 'promptlabels.pkl']
    dataset_found = False
    
    for dataset_file in dataset_files:
        if os.path.exists(dataset_file):
            print(f"\nFound dataset: {dataset_file}")
            analysis_success = analyze_dataset_statistics(dataset_file)
            if analysis_success:
                dataset_found = True
                break
    
    if not dataset_found:
        print(f"\nNo dataset files found. Looking for: {dataset_files}")
        print("Please run the dataset regeneration script first.")
        return 1
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
    print("✅ Prompt parsing is working correctly")
    print("✅ Dataset statistics generated")
    print("✅ Visualizations created")
    print("=" * 60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

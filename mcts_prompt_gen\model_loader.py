#!/usr/bin/env python3
"""
Model Loader for Web Interface

This module provides utilities to load and use trained models for the web interface.
It handles the integration between the web interface and the existing trained models.

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import logging
import pickle
import dill
import numpy as np
from typing import List, Tuple, Dict, Any, Optional

# Import existing models
try:
    from goodness_predictor_models import LightGBMGoodnessPredictorModel, PromptFeatureExtractor
except ImportError:
    # Fallback imports
    import lightgbm as lgb
    from sklearn.feature_extraction.text import TfidfVectorizer

logger = logging.getLogger(__name__)

class ModelWrapper:
    """
    Wrapper class for trained models to provide consistent interface.
    """
    
    def __init__(self, model_path: str, model_type: str):
        """
        Initialize model wrapper.
        
        Args:
            model_path: Path to the trained model file
            model_type: Type of model ('lightgbm_ratio', 'lightgbm_binary', etc.)
        """
        self.model_path = model_path
        self.model_type = model_type
        self.model = None
        self.feature_extractor = None
        self.is_loaded = False
        
        self._load_model()
    
    def _load_model(self):
        """Load the model from disk."""
        try:
            logger.info(f"Loading model from: {self.model_path}")
            
            with open(self.model_path, 'rb') as f:
                model_data = dill.load(f)
            
            # Handle different model formats
            if isinstance(model_data, dict):
                # Model data is a dictionary with model and feature extractor
                self.model = model_data.get('model')
                self.feature_extractor = model_data.get('feature_extractor')
                logger.info(f"Loaded model and feature extractor from dict for {self.model_type}")
            else:
                # Model data is the model itself
                self.model = model_data
                # Try to find feature extractor in the same directory
                fe_path = self.model_path.replace('_model.pkl', '_feature_extractor.pkl')
                if os.path.exists(fe_path):
                    with open(fe_path, 'rb') as f:
                        self.feature_extractor = dill.load(f)
                        logger.info(f"Loaded separate feature extractor for {self.model_type}")
                else:
                    logger.warning(f"No feature extractor found for {self.model_type}")
            
            self.is_loaded = True
            logger.info(f"Successfully loaded {self.model_type} model")
            
        except Exception as e:
            logger.error(f"Failed to load model {self.model_path}: {e}")
            self.is_loaded = False
    
    def predict(self, prompt_text: str) -> float:
        """
        Predict goodness score for a prompt.
        
        Args:
            prompt_text: Raw prompt text
            
        Returns:
            Predicted goodness score
        """
        if not self.is_loaded:
            return 0.5
        
        try:
            # If we have a feature extractor, use it
            if self.feature_extractor:
                # Parse prompt to tags first
                from corrected_prompt_parser import parse_prompt_with_weights_corrected
                parsed_tags = parse_prompt_with_weights_corrected(prompt_text)

                # Convert to expected format: (filename, [(tag, weight)], goodness)
                # Use dummy values for filename and goodness since we only need features
                prompt_data = [("dummy.png", parsed_tags, 0.0)]

                # Transform using the feature extractor
                features = self.feature_extractor.transform(prompt_data)

                # Make prediction
                if hasattr(self.model, 'predict'):
                    prediction = self.model.predict(features)[0]
                else:
                    # LightGBM model
                    prediction = self.model.predict(features)[0]

                return float(prediction)

            else:
                # Fallback: use model directly if it has a predict method
                if hasattr(self.model, 'predict'):
                    return float(self.model.predict([prompt_text])[0])
                else:
                    logger.warning(f"No feature extractor found for {self.model_type}")
                    return 0.5

        except Exception as e:
            logger.error(f"Prediction error for {self.model_type}: {e}")
            return 0.5
    
    def predict_from_tags(self, tags: List[Tuple[str, float]]) -> float:
        """
        Predict goodness score from parsed tags.
        
        Args:
            tags: List of (tag, weight) tuples
            
        Returns:
            Predicted goodness score
        """
        if not self.is_loaded:
            return 0.5
        
        try:
            # Use tags directly with feature extractor
            if self.feature_extractor:
                # Convert to expected format: (filename, [(tag, weight)], goodness)
                prompt_data = [("dummy.png", tags, 0.0)]

                # Transform using the feature extractor
                features = self.feature_extractor.transform(prompt_data)

                # Make prediction
                if hasattr(self.model, 'predict'):
                    prediction = self.model.predict(features)[0]
                else:
                    # LightGBM model
                    prediction = self.model.predict(features)[0]

                return float(prediction)
            else:
                # Fallback: convert tags back to prompt string
                prompt_text = self._tags_to_prompt(tags)
                return self.predict(prompt_text)

        except Exception as e:
            logger.error(f"Tag prediction error for {self.model_type}: {e}")
            return 0.5
    
    def _tags_to_prompt(self, tags: List[Tuple[str, float]]) -> str:
        """Convert tags back to prompt string."""
        tag_strings = []
        for tag, weight in tags:
            if weight == 1.0:
                tag_strings.append(tag)
            else:
                tag_strings.append(f"({tag}:{weight:.1f})")
        return ', '.join(tag_strings)

class ModelManager:
    """
    Manager class for loading and managing multiple models.
    """
    
    def __init__(self, model_dir: str = None):
        """
        Initialize model manager.
        
        Args:
            model_dir: Directory containing trained models
        """
        self.model_dir = model_dir or self._find_latest_model_dir()
        self.models = {}
        self._load_all_models()
    
    def _find_latest_model_dir(self) -> str:
        """Find the latest model training directory."""
        base_dirs = [
            'corrected_ratio_training_20250624_190517',
            'goodness_model_training_20250624_154556',
            'ratio_based_training_20250624_161114'
        ]
        
        for dir_name in base_dirs:
            full_path = os.path.join(os.path.dirname(__file__), dir_name)
            if os.path.exists(full_path):
                logger.info(f"Using model directory: {full_path}")
                return full_path
        
        raise FileNotFoundError("No trained model directory found")
    
    def _load_all_models(self):
        """Load all available models from the directory."""
        model_files = {
            'lightgbm_ratio': 'lightgbm_ratio_model.pkl',
            'lightgbm_binary': 'lightgbm_binary_model.pkl',
            'resnet_ratio': 'resnet_ratio_model.pkl',
            'resnet_binary': 'resnet_binary_model.pkl'
        }
        
        for model_name, filename in model_files.items():
            model_path = os.path.join(self.model_dir, filename)
            if os.path.exists(model_path):
                try:
                    wrapper = ModelWrapper(model_path, model_name)
                    if wrapper.is_loaded:
                        self.models[model_name] = wrapper
                        logger.info(f"Loaded model: {model_name}")
                except Exception as e:
                    logger.error(f"Failed to load {model_name}: {e}")
    
    def get_model(self, model_name: str) -> Optional[ModelWrapper]:
        """Get a specific model by name."""
        return self.models.get(model_name)
    
    def get_all_models(self) -> Dict[str, ModelWrapper]:
        """Get all loaded models."""
        return self.models
    
    def predict(self, prompt_text: str, model_names: List[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Get predictions from multiple models.
        
        Args:
            prompt_text: Raw prompt text
            model_names: List of model names to use (default: all)
            
        Returns:
            Dictionary with predictions from each model
        """
        if model_names is None:
            model_names = list(self.models.keys())
        
        predictions = {}
        
        for model_name in model_names:
            if model_name in self.models:
                model = self.models[model_name]
                try:
                    score = model.predict(prompt_text)
                    predictions[model_name] = {
                        'score': score,
                        'confidence': 0.95,  # Placeholder
                        'model_type': model_name.split('_')[0]
                    }
                except Exception as e:
                    logger.error(f"Prediction error for {model_name}: {e}")
                    predictions[model_name] = {
                        'score': 0.5,
                        'confidence': 0.0,
                        'error': str(e)
                    }
        
        return predictions
    
    def get_best_score(self, predictions: Dict[str, Dict[str, Any]]) -> float:
        """Get the best score from predictions (prioritize ratio-based LightGBM)."""
        if 'lightgbm_ratio' in predictions:
            return predictions['lightgbm_ratio']['score']
        elif 'lightgbm_binary' in predictions:
            return predictions['lightgbm_binary']['score']
        elif predictions:
            return list(predictions.values())[0]['score']
        return 0.5

# Convenience function for easy import
def create_model_manager(model_dir: str = None) -> ModelManager:
    """Create and return a model manager instance."""
    return ModelManager(model_dir)

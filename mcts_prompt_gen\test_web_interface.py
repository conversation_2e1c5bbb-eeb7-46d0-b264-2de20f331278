#!/usr/bin/env python3
"""
Test script for the web interface components.

This script tests the model loading, prediction, and optimization functionality
before running the full web interface.

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import logging
from typing import List, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_loading():
    """Test model loading functionality."""
    print("="*60)
    print("TESTING MODEL LOADING")
    print("="*60)
    
    try:
        from model_loader import ModelManager
        
        # Initialize model manager
        model_manager = ModelManager()
        
        print(f"Loaded {len(model_manager.models)} models:")
        for model_name, model in model_manager.models.items():
            print(f"  - {model_name}: {'✓' if model.is_loaded else '✗'}")
        
        return model_manager
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return None

def test_prompt_parsing():
    """Test prompt parsing functionality."""
    print("\n" + "="*60)
    print("TESTING PROMPT PARSING")
    print("="*60)
    
    try:
        from corrected_prompt_parser import parse_prompt_with_weights_corrected
        
        test_prompts = [
            "1girl, masterpiece, best quality",
            "(1girl:1.2), {masterpiece}, [low quality:-0.5]",
            "1girl, --cfg 7.5 --steps 30 --lora test.safetensors:0.8",
            "very aesthetic, amazing quality, absurdres, 1girl"
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\nTest {i}: {prompt}")
            try:
                parsed = parse_prompt_with_weights_corrected(prompt)
                print(f"  Parsed: {len(parsed)} tags")
                for tag, weight in parsed[:5]:  # Show first 5 tags
                    print(f"    - {tag}: {weight}")
                if len(parsed) > 5:
                    print(f"    ... and {len(parsed) - 5} more")
            except Exception as e:
                print(f"  ❌ Parsing failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Prompt parsing test failed: {e}")
        return False

def test_predictions(model_manager):
    """Test prediction functionality."""
    print("\n" + "="*60)
    print("TESTING PREDICTIONS")
    print("="*60)
    
    if not model_manager:
        print("❌ No model manager available")
        return False
    
    test_prompts = [
        "1girl, masterpiece, best quality, ultra detailed",
        "1girl, low quality, blurry",
        "very aesthetic, amazing quality, absurdres, 1girl, detailed face",
        "simple prompt"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\nTest {i}: {prompt}")
        try:
            predictions = model_manager.predict(prompt)
            print(f"  Predictions from {len(predictions)} models:")
            for model_name, pred in predictions.items():
                score = pred.get('score', 0.5)
                print(f"    - {model_name}: {score:.3f}")
        except Exception as e:
            print(f"  ❌ Prediction failed: {e}")
    
    return True

def test_optimization():
    """Test optimization functionality."""
    print("\n" + "="*60)
    print("TESTING OPTIMIZATION")
    print("="*60)
    
    try:
        from web_interface_backend import PromptGoodnessPredictor
        
        # Initialize predictor
        predictor = PromptGoodnessPredictor()
        
        test_prompt = "1girl, simple background"
        print(f"Optimizing: {test_prompt}")
        
        # Test quick optimization
        result = predictor.optimize_prompt(test_prompt, time_limit=5)
        
        if 'error' in result:
            print(f"❌ Optimization failed: {result['error']}")
            return False
        
        print(f"Original score: {result.get('original_score', 'N/A'):.3f}")
        print(f"Found {len(result.get('suggestions', []))} suggestions:")
        
        for i, suggestion in enumerate(result.get('suggestions', [])[:3], 1):
            print(f"  {i}. {suggestion.get('action', 'N/A')}")
            print(f"     Improvement: +{suggestion.get('improvement', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Optimization test failed: {e}")
        return False

def test_web_backend():
    """Test web backend initialization."""
    print("\n" + "="*60)
    print("TESTING WEB BACKEND")
    print("="*60)
    
    try:
        from web_interface_backend import initialize_predictor
        
        # Test predictor initialization
        initialize_predictor()
        print("✓ Web backend initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Web backend test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 STARTING WEB INTERFACE TESTS")
    print("="*60)
    
    results = {}
    
    # Test 1: Model Loading
    model_manager = test_model_loading()
    results['model_loading'] = model_manager is not None
    
    # Test 2: Prompt Parsing
    results['prompt_parsing'] = test_prompt_parsing()
    
    # Test 3: Predictions
    results['predictions'] = test_predictions(model_manager)
    
    # Test 4: Optimization
    results['optimization'] = test_optimization()
    
    # Test 5: Web Backend
    results['web_backend'] = test_web_backend()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✓ PASS" if passed_test else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if passed_test:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Web interface is ready to run.")
        print("\nTo start the web interface:")
        print("  python web_interface_backend.py")
        print("  Then open http://localhost:5000 in your browser")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

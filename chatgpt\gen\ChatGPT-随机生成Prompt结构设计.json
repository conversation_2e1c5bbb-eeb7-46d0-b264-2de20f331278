{"metadata": {"user": {"name": "Yef flower", "email": "<EMAIL>"}, "dates": {"created": "6/21/2025 15:45", "updated": "6/21/2025 15:49", "exported": "6/23/2025 2:45"}, "powered_by": "ChatGPT Exporter (https://www.chatgptexporter.com)"}, "messages": [{"role": "Prompt", "say": "comfygen.json\n\n\nFile\n\n\ncomfygen.py\n\n\nPython\n\n\nnovelai_gen_rc.py\n\n\nPython\n\n\n我在写一个简易的随机生成danbooru tag格式prompt的代码，然后用comfyui生成对应图片。现在的代码(comfygen.py)非常简陋，只实现了最基础的生成，我想参考之前的另一份代码(novelai_gen_rc.py)，里面有一段更复杂的生成角色和衣服的函数。\n首先，应该设计一个更合理的结构，来存储这些随机的分支。目前的代码有3处不合理的地方：\n\n\nlora应该有权重，现在只能随机权重，但是可能有些权重有不同的分布，比如lora1适合0.8到1，而lora2可能要求必须权重在0.2到0.7之间才效果好，甚至有些特殊lora比如outline, 它的效果如果是-1则会消除轮廓线，配合flat color的tag反而效果更好。\n\n现有part之间有重合的关系。比如现在的角色yixuan，她有一套默认衣服，是黄色夹克，但是也可以换其他衣服（黑色西装等等）。如果有新的角色，新角色的默认衣服不是黄色夹克，那么新角色的衣服tag在随机的时候，不应该再给yixuan的这套衣服分配概率。也就是应该有不同part之间的联动，但又保留一些随机性。\n\n同一个part内可能多个tag都应该选。现有代码一定是每个part内部恰好选一个出来，但是这样很僵硬，实际上比如衣服，有些衣服是一整套，有些衣服则应该是上衣和下装两个子类别的组合，再比如style，也可能有多种风格一起出现，且不同权重。还有background，既要有简单背景之类的概括tag，也要有indoors outdoors之类的场景tag。\n上面这些都表明现有结构过于简单，难以表达这些复杂的关系。应该用一个更合理的数据结构来存储。 其次，这些关系不应该全靠手工编写，而是应该从我之前收集的10000张图片与prompt数据集里计算一些。 因此这个新的数据结构还需要能方便计算和拟合。 这是指两种意义上的计算，类似于AlphaZero的模型设计，一个是policy即生成多种多样prompt的概率，一个是value即这个模型应该能预判一个prompt的最终质量（比如用户是否喜欢，可以来自已有图片的标注初始化等等）。比如如果只考虑style, 可以认为不同style是相互独立的，而且可以相互叠加，只是有些组合会不好看或者相互矛盾。这就可以当成一个高维向量，每一维代表一种style，而模型应该根据用户反馈，拟合并动态调整一个高维分布。 但是角色特征，表情，衣服等等会更复杂，因为那些内容不能看成是相互独立的，或者说比style更容易相互矛盾。 \n最后，数据结构也应该考虑额外的采样参数，比如步数，cfg，长和宽，采样方式（虽然可以默认全用euler_a）。我设想的是把这个结构封装为一个class Data， 然后再把一个具体的prompt封装为class Prompt，然后可以prompt.queue()来调用api加入comfyui的队列，而它内部变量存储着各种生成信息，它的构造函数能根据Data随机采样一个合理的prompt，它的各种工具函数可以方便调整; 而Data应该能根据输入一堆prompt学一个更好的分布用来随机生成。 \n请分析上述需求，然后给出完整的设计，最后写出完整的python代码\n\n\nThought for 19 seconds\n\n\nReading documents\n\n\nThe user's creating a random prompt generator for Danbooru tags in ComfyUI and is unhappy with its simplicity. They highlighted three issues:\n\n\nDifferent Lora weights need range-specific handling, including negative values.\n\nClothing items should be conditionally picked based on the chosen character, avoiding mismatched outfits.\n\nSome parts require multiple tags, not just one – like outfits with sets or backgrounds with multiple styles.\nTo improve, I’ll focus on adding more complexity and flexibility to their data structure.\n\n\nThe user wants to enhance their generator by computing distributions from a dataset of 10,000+ images and prompts. They want two components like AlphaZero: a policy for generating diverse prompts and a value system to assess quality. The design requires a new Data class to model these relationships and a Prompt class to represent generated prompts. The system would use distributions, weighting, and possibly JSON/YAML for file handling. I'll need to outline classes, implement methods, and integrate with ComfyUI without external resources.\n\n\nDone"}, {"role": "Response", "say": "下面先把整体的设计要点梳理清楚，再给出一份可以直接运行的 Python 示例代码。代码在保持与你现有 ComfyUI 工作流兼容的同时，引入了更灵活的数据结构与采样逻辑，方便以后继续扩展（包括统计学习、价值网络、用户反馈微调等）。\n\n\n\n1 设计思路\n\n\n1.1 核心类与整体流程\n\n类\n作用\n关键字段\nLoraRule\n描述一条 LoRA 的权重分布（可负权）\npath, mean, sigma, low, high, allow_negative\nTagOption\n一个可被采样的选项（角色 / 衣服 / 风格 …）\nname, tags:list[str], loras:list[LoraRule], weight:float, children:list[str]\nPart\n一类内容（character / clothing …）\nname, multi_select:bool, options:dict[str,TagOption], count_dist\nData\n整体先验分布（可通过 fit 学习）\nparts:dict[str,Part], param_dists, fit(), sample_prompt()\nPrompt\n一次具体的采样结果\ntags, loras, sampler_args, to_workflow(), queue()\n\n流程示意：\n\n\nData.fit(dataset)  ──►  更新各 Part / TagOption 概率\n                           ▲\n                           │\nPrompt(Data)  ──►  sample_prompt()  ──►  Prompt.queue()  ──►  ComfyUI\n\n\n1.2 支持三种关键需求\n\n\nLoRA 权重分布\n\n\n每个 LoraRule 独立存储权重采样函数，可定义均值、方差、截断区间，甚至自定义 lambda（支持负权）。\n\n跨 Part 依赖\n\n\nTagOption.children 里列出受此选项影响的其他 Part 名称。\n\n采样时先选定 character，然后把 character 的默认衣服写进 clothing 的候选池里，并降低与其它角色专属衣服的概率。\n\nPart 内多选 & 子类别\n\n\nPart.multi_select=True 时，用 count_dist() 决定一次要选多少个选项；TagOption 还可以指向子 Part（例如 clothing/top, clothing/bottom）。\n\n\n1.3 统计学习接口\n\n\nData.fit(list[prompt_str])：\n\n\n解析已存在的 Danbooru tag prompt，统计词频 & 共现频率。\n\n用 Add-α 平滑更新各 TagOption.weight；如果检测到强耦合（互信息阈值），自动在两 Part 之间建依赖边。\n\n未来扩展：再加一层 ValueNetwork（可用 LightGBM / 任意 NN），用用户评分 / 收藏数回传梯度。\n\n\n1.4 采样参数（steps, cfg, H×W …）\n\n\nData.param_dists 里放 scipy.stats 或自定义采样 callables；现示例用截断正态。\n\n这些参数随 Prompt.sample_prompt() 一起采样并进入 prepare_workflow()（直接调用你现有的模板逻辑）。\n\n\n\n2 Python 示例代码\n\n完整单文件，保存为 smart_comfygen.py 即可运行；依赖仅 pydantic（便于校验）和 numpy，其余沿用你已有的 comfygen.py 中的工具函数。\n其中 DATA_YAML 段写了一份极简 YAML，演示 yixuan 与新角色 ririka 的衣服联动、LoRA 权重范围等规则；实际可拆成独立 .yaml 或 .json 文件由美术/策划维护。\n训练接口 Data.fit() 目前是占位实现（计算词频并归一化）。你只需把 dataset/*.txt 换成你的 1 万条 prompt 文件即可自动更新权重。\n\n# smart_comfygen.py\nfrom __future__ import annotations\nimport json, random, os, sys, datetime, math\nfrom pathlib import Path\nfrom typing import List, Dict, Callable, Optional\nimport numpy as np\nfrom pydantic import BaseModel, Field, validator\n\n# ---------------------------- 基础数据结构 ----------------------------\n\nclass LoraRule(BaseModel):\n    path: str\n    mean: float = 1.0\n    sigma: float = 0.25\n    low: float = 0.05\n    high: float = 1.5\n    allow_negative: bool = False\n\n    def sample(self) -> str:\n        w = np.random.normal(self.mean, self.sigma)\n        if not self.allow_negative:\n            w = abs(w)\n        w = np.clip(w, self.low, self.high)\n        return f\"<lora:{self.path}:{w:.2f}>\"\n\nclass TagOption(BaseModel):\n    name: str\n    tags: List[str] = []\n    loras: List[LoraRule] = []\n    weight: float = 1.0               # 用于加权采样 / 统计学习更新\n    children: List[str] = []          # 受此 option 影响的 Part 名称\n\n    def expand(self) -> (List[str], List[str]):\n        \"\"\"返回 (loras_txt, tags_txt)\"\"\"\n        lora_txt = [lr.sample() for lr in self.loras]\n        return lora_txt, self.tags\n\nclass Part(BaseModel):\n    name: str\n    multi_select: bool = False\n    options: Dict[str, TagOption]\n    # count_dist 决定一次多选选多少个：可自定义 Callable，也可给 (low, high, p) 三元组\n    count_dist: Optional[Callable[[], int]] = None  \n\n    def sample(self, exclude: List[str] = None) -> List[TagOption]:\n        exclude = exclude or []\n        opts = list(self.options.values())\n        # 按 weight 构造概率\n        weights = np.array([o.weight for o in opts], dtype=float)\n        weights /= weights.sum()\n        if not self.multi_select:\n            choice = random.choices(opts, weights=weights)[0]\n            return [choice]\n        # multi_select\n        n = 1\n        if self.count_dist:\n            n = max(1, self.count_dist())\n        choice = np.random.choice(opts, size=n, replace=False, p=weights)\n        return list(choice)\n\n# ---------------------------- Data & Prompt ----------------------------\n\nclass Data(BaseModel):\n    parts: Dict[str, Part]\n    param_dists: Dict[str, Callable[[], float]] = {}\n\n    # ------------------------------------------------------------------\n    # 生成 Prompt\n    # ------------------------------------------------------------------\n    def sample_prompt(self) -> \"Prompt\":\n        chosen: Dict[str, List[TagOption]] = {}\n        loras, tags = [], []\n        # 先抽 character，便于处理 cross-part 依赖\n        char_part = self.parts[\"character\"].sample()[0]\n        chosen[\"character\"] = [char_part]\n        l, t = char_part.expand()\n        loras += l; tags += t + [\"1girl\", char_part.name]\n\n        # 根据 children 注入默认衣服 / 依赖选项\n        for child_name in char_part.children:\n            if child_name in self.parts:\n                # 角色默认衣服放到选项池顶并设置高权重\n                for opt in self.parts[child_name].options.values():\n                    if opt.name == f\"{char_part.name}_default\":\n                        opt.weight = 10.0\n\n        # 其余 Part 正常采样\n        for pname, part in self.parts.items():\n            if pname == \"character\":\n                continue\n            sampled = part.sample()\n            chosen[pname] = sampled\n            for opt in sampled:\n                l, t = opt.expand()\n                loras += l\n                tags += t\n\n        # 生成参数\n        sampler_args = {k: dist() for k, dist in self.param_dists.items()}\n\n        return Prompt(tags=tags, loras=loras, sampler_args=sampler_args)\n\n    # ------------------------------------------------------------------\n    # 适配统计学习 —— 简化版：基于词频调整 option.weight\n    # ------------------------------------------------------------------\n    def fit(self, prompt_texts: List[str], alpha: float = 0.1):\n        \"\"\"\n        prompt_texts: 已有的 Danbooru tag prompt 列表\n        alpha: Add-alpha 平滑\n        \"\"\"\n        # 简单词频统计\n        freq: Dict[str, int] = {}\n        for p in prompt_texts:\n            for tag in [t.strip() for t in p.split(',')]:\n                if tag:\n                    freq[tag] = freq.get(tag, 0) + 1\n        # 更新权重（示例：log(freq)）\n        for part in self.parts.values():\n            for opt in part.options.values():\n                cnt = sum(freq.get(t, 0) for t in opt.tags)\n                opt.weight = math.log(cnt + alpha)\n\nclass Prompt(BaseModel):\n    tags: List[str]\n    loras: List[str]\n    sampler_args: Dict[str, float]\n\n    # ---------------- ComfyUI 工作流封装 -----------------\n    def _prepare_workflow(self, output_dir: str, model_name: str) -> dict:\n        \"\"\"复用原 comfygen.py 的模板逻辑\"\"\"  # :contentReference[oaicite:0]{index=0}\n        script_directory = Path(__file__).parent\n        template = json.load((script_directory / \"res\" / \"comfygen.json\").open())  # :contentReference[oaicite:1]{index=1}\n        memos = {\"sampler\":\"12\", \"model\":\"4\", \"prompt\":\"6\", \"neg_prompt\":\"7\",\n                 \"resolution\":\"30\", \"lora\":\"41\", \"output\":\"9\"}\n        # 基本替换\n        template[memos[\"model\"]][\"inputs\"][\"ckpt_name\"] = model_name\n        template[memos[\"resolution\"]][\"inputs\"][\"width\"]  = 1024\n        template[memos[\"resolution\"]][\"inputs\"][\"height\"] = 1024\n        template[memos[\"prompt\"]][\"inputs\"][\"text\"] = ', '.join(self.tags)\n        template[memos[\"output\"]][\"inputs\"][\"filename_prefix\"] = output_dir\n        template[memos[\"lora\"]][\"inputs\"][\"text\"] = ','.join(self.loras)\n        # 采样参数\n        sampler = template[memos[\"sampler\"]][\"inputs\"]\n        sampler[\"seed\"] = random.randint(0, 18446744073709551614)\n        sampler[\"steps\"] = int(self.sampler_args[\"steps\"])\n        sampler[\"cfg\"]   = round(self.sampler_args[\"cfg\"], 1)\n        return template\n\n    def queue(self, output_dir: Optional[str] = None,\n              model: str = \"shiitakeMix_v20.safetensors\",\n              api: str = \"http://127.0.0.1:8188/prompt\"):\n        output_dir = output_dir or datetime.datetime.now().strftime(\"%Y-%m-%d/ComfyUI\")\n        wf = self._prepare_workflow(output_dir, model)\n        from urllib import request\n        req =  request.Request(api, data=json.dumps({\"prompt\": wf}).encode('utf-8'))\n        request.urlopen(req)\n\n# ---------------------------- 默认 YAML 规则 ----------------------------\nDATA_YAML = \"\"\"\ncharacter:\n  multi_select: false\n  options:\n    yixuan:\n      tags: [\"white hair\", \"yellow eyes\", \"mole\", \"bangs\"]\n      loras:\n        - {path: \"ch\\\\Yi_Xuan_IL_v2.safetensors\", mean: 1.0, sigma: 0.1}\n      children: [\"clothing\"]\n    ririka:\n      tags: [\"pink hair\", \"green eyes\"]\n      loras:\n        - {path: \"ch\\\\Ririka_XL_v1.safetensors\", mean: 0.9, sigma: 0.1}\n      children: [\"clothing\"]\nclothing:\n  multi_select: true\n  count_dist: [1, 2]      # 简写：随机选 1-2 套\n  options:\n    yixuan_default:\n      tags: [\"yellow jacket\", \"black gloves\"]\n    ririka_default:\n      tags: [\"school uniform\", \"red ribbon\"]\n    suits:\n      tags: [\"black suits\", \"tie\", \"necktie\"]\nstyle:\n  multi_select: true\n  count_dist: [1, 3]\n  options:\n    watercolor:\n      tags: [\"watercolor (medium)\"]\n      loras:\n        - {path: \"sty\\\\ILwatercolor.safetensors\", mean: 1.0, sigma: 0.15}\n    cute:\n      tags: [\"Cute_niji_style\"]\n      loras:\n        - {path: \"sty\\\\Niji_cute_style_illustrious.safetensors\", mean: 1.0, sigma: 0.1}\nquality:\n  multi_select: false\n  options:\n    high:\n      tags: [\"masterpiece\", \"best quality\", \"absurdres\"]\n\"\"\"\n\nimport yaml\ndef parse_yaml(yaml_str: str) -> Data:\n    raw = yaml.safe_load(yaml_str)\n    parts_dict = {}\n    for pname, conf in raw.items():\n        opts = {}\n        for oname, od in conf[\"options\"].items():\n            lrs = [LoraRule(**lr) for lr in od.get(\"loras\", [])]\n            opts[oname] = TagOption(name=oname, tags=od.get(\"tags\", []),\n                                    loras=lrs, children=od.get(\"children\", []))\n        # count_dist 解析\n        cd_raw = conf.get(\"count_dist\", None)\n        cd_fn = None\n        if isinstance(cd_raw, list) and len(cd_raw)==2:\n            low, high = cd_raw\n            cd_fn = lambda low=low, high=high: random.randint(low, high)\n        parts_dict[pname] = Part(name=pname,\n                                 multi_select=conf.get(\"multi_select\", False),\n                                 options=opts,\n                                 count_dist=cd_fn)\n    # 采样参数分布\n    param = {\"steps\": lambda: np.clip(np.random.normal(37, 4), 30, 45),\n             \"cfg\":   lambda: np.clip(np.random.normal(5.0, 1.0), 3.0, 8.0)}\n    return Data(parts=parts_dict, param_dists=param)\n\n# ---------------------------- 命令行入口 ----------------------------\ndef main():\n    import argparse\n    parser = argparse.ArgumentParser()\n    parser.add_argument(\"-n\", \"--count\", type=int, default=2,\n                        help=\"生成图片数量\")\n    args = parser.parse_args()\n\n    data = parse_yaml(DATA_YAML)\n    # 可选：用历史数据拟合\n    dataset_dir = Path(\"dataset\")\n    if dataset_dir.exists():\n        texts = [p.read_text(encoding='utf-8') for p in dataset_dir.glob(\"*.txt\")]\n        data.fit(texts)\n\n    for _ in range(args.count):\n        prompt = data.sample_prompt()\n        prompt.queue()\n        print(\"Queued:\", ', '.join(prompt.loras), \"|\", ', '.join(prompt.tags))\n\nif __name__ == \"__main__\":\n    main()\n\n\n\n3 如何继续迭代\n\n\n把 YAML 抽到文件\n\n\n每位角色 / 场景设计师都能独立维护 YAML；smart_comfygen.py 启动时自动读取并合并。\n\n统计学习升级\n\n\n当前 fit() 只是词频 → 权重，你可换成互信息、PMI、Graph Neural Network 等，更好捕捉 衣服–角色、表情–姿势 关联。\n\nValue Network\n\n\n记录用户点赞 / 收藏 / 手工评分，训练一个 QualityPredictor，推断 prompt 质量；MCTS 里用它作 value，再用本文 Data 作 policy，就得到 AlphaZero 风格自我改进循环。\n\nGUI 调参\n\n\n把 DATA_YAML 动态加载到一个 Streamlit / Gradio 面板，让美术实时观察采样效果并手动微调权重 / 约束。\n\n\n这样，既解决了 LoRA 权重分布、跨 Part 依赖、多标签组合 的灵活表达，又为 统计学习与质量评估 预留了接口，后续只需填充 fit() 与 ValueNetwork 的实现即可。祝你生成愉快!"}], "title": "随机生成Prompt结构设计"}
from __future__ import annotations
import os, sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import duckdb
from datetime import datetime
import numpy as np
import dill
from telethon import types
import os
from typing import Dict, Sequence, Optional
import pandas as pd


from db import StickerDB
from model import CaptionModel

cache_messages_file = 'cache_messages.pkl'
msgs = dill.load(open(cache_messages_file, 'rb'))


from collections import defaultdict
pack_cnt = defaultdict(int)
last_use = defaultdict(lambda: datetime.min)
sticker_packs = {}
for chat_id in msgs:
    for message_id in msgs[chat_id]:
        if message_id == -1:
            continue
        message = msgs[chat_id][message_id]
        if 'document' not in message:
            continue
        document = message['document']
        if not hasattr(document, 'attributes') or not any(isinstance(attr, types.DocumentAttributeSticker) for attr in document.attributes):
            continue
        stickerset = None
        for attr in document.attributes:
            if isinstance(attr, types.DocumentAttributeSticker):
                stickerset = attr.stickerset
                break
        if stickerset is None or isinstance(stickerset, types.InputStickerSetEmpty):# or stickerset.id in sticker_packs:
            continue
        sticker_packs[stickerset.id] = stickerset
        pack_cnt[stickerset.id] += 1
        last_use[stickerset.id] = max(last_use[stickerset.id], message['date'])
        # print('found sticker pack:', stickerset.id, stickerset.access_hash)

cache_packs_file = 'cache_packs.pkl'
known_packs = dill.load(open(cache_packs_file, 'rb'))

# dict_keys(['id', 'access_hash', 'cnt', 'title', 'short_name', 'stickers'])
uf = {}
us = set()
cnt = 0
for set_id in known_packs:
    for sticker in known_packs[set_id]["stickers"]:
        cnt += 1
        uf[sticker.id] = set_id
        us.add((sticker.access_hash))

print('found', len(uf), 'unique stickers in', cnt, 'stickers', 'and', len(us), 'unique access hashes in known packs', len(sticker_packs), 'sticker packs')



dim_map = {
    'cap_en': 3072,
    'cap_zh': 3072,
}
DB = StickerDB(name='stickers', dim_map=dim_map)

caption_model = CaptionModel()
# sort by count first, then by last use date. The more recently used packs will be processed first.
packs = sorted(sticker_packs.items(), key=lambda x: (pack_cnt[x[0]], last_use[x[0]]), reverse=True)


from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from itertools import chain
MAX_WORKERS = 16

def get_pack_stickers(packs):
    pack_id, stickerset = packs
    ret = []
    pack = known_packs.get(pack_id, {})
    print(f"{pack_cnt[pack_id]} Pack ID: {pack_id}, Count: {pack.get('cnt', 0)}, Title: {pack.get('title', 'Unknown')}, Short Name: {pack.get('short_name', 'Unknown')}")
    short_name = pack.get('short_name', 'unknown')
    if not os.path.exists(f"stickers/{short_name}"):
        print(f"not found pack {short_name}, skipping")
        return []
    for sidx, sticker in enumerate(pack["stickers"]):
        sid = sticker.id
        dbs = DB.get_sticker(sid)
        if dbs and all([dbs.get(s) for s in ['caption_en', 'caption_zh']]):
            continue
        elif not dbs:
            DB.insert_sticker(
                sid,
                sticker.access_hash,
                sticker.file_reference,
                {
                    # 'emoji': sticker.emoticon or '',
                    'pack_name': short_name,
                    'mime_type': sticker.mime_type or '',
                    'created_at': sticker.date.strftime('%Y-%m-%d %H:%M:%S'),
                }
            )
        print('now processing sticker:', sid, 'from pack', pack_id, sidx + 1, '/', len(pack["stickers"]))
        ext = 'webp'
        for attr in sticker.attributes:
            if isinstance(attr, types.DocumentAttributeFilename):
                ext = attr.file_name.split('.')[-1]
        filename = f"stickers/{short_name}/{sid}.{ext}"
        if ext == "webm":
            # extract first frame as webp
            ext = 'webp'
            tmp = filename[:-4] + '_0.webp'
            if not os.path.exists(tmp):
                os.system(f"ffmpeg -i {filename} -vframes 1 -q:v 2 {tmp}")
                filename = tmp
        if ext != 'webp':
            print(f"not webp sticker {sid}: {ext}, skipping")
            continue
        if not os.path.exists(filename):
            print(f"not found sticker {filename}, skipping")
            continue
        ret.append((pack_id, sid, filename))
    return ret

with ThreadPoolExecutor(max_workers=MAX_WORKERS) as pool:
    all_stickers = list(chain.from_iterable(pool.map(get_pack_stickers, packs[:50])))
print(f"共找到 {len(all_stickers)} 个sticker需要处理")

def process_sticker(args):
    pack_id, sid, filename = args
    try:
        captions = caption_model.caption(filename)
        print(f"Caption for sticker {sid}:")
        values = {}
        for k, v in [('caption_en', captions['p']), ('caption_zh', captions['c']), ('ocr', captions['t'])]:
            if v and len(v.strip()) > 0:
                values[k] = v.strip()
                print(f"  {k}: {v.strip()}")
        if len(values) > 0:
            print(f"Updating sticker {sid} with captions: {values}")
            DB.update_sticker(sid, values)
        else:
            print(f"No captions found for sticker {filename}, skipping")
            return "Empty"
    except Exception as e:
        print(f"Error processing sticker {sid}: {e}")
        return f"Error: {e}"
    return "OK"

if len(all_stickers) != 0:
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as pool:
        results = list(pool.map(process_sticker, all_stickers))

    stat = defaultdict(int)
    for res in results:
        stat[res] += 1
    print("处理结果统计：")
    for k, v in stat.items():
        print(f"{k}: {v}")


# now compute embeddings for captions
missing_en = DB.conn.cursor().execute('''
    SELECT id, caption_en FROM stickers
    WHERE caption_en IS NOT NULL AND id NOT IN (SELECT id FROM vectors_cap_en)
''').fetchall()
missing_zh = DB.conn.cursor().execute('''
    SELECT id, caption_zh FROM stickers
    WHERE caption_zh IS NOT NULL AND id NOT IN (SELECT id FROM vectors_cap_zh)
''').fetchall()

all_captions = [(sid, caption, 'cap_en') for sid, caption in missing_en] + [(sid, caption, 'cap_zh') for sid, caption in missing_zh]
print(f"共需处理英文caption embedding: {len(missing_en)} 条，中文: {len(missing_zh)} 条")
if len(all_captions) == 0:
    print("没有需要处理的caption，退出")
    exit(0)

block_size = 100
blocks = [(i, min(i + block_size, len(all_captions))) for i in range(0, len(all_captions), block_size)]
def process_caption_block(block):
    start, end = block
    texts = [caption for sid, caption, kind in all_captions[start:end] if caption and caption.strip()]
    embed_text_batch = caption_model.embed_text_batch(texts)
    ret = []
    for i, (sid, caption, kind) in enumerate(all_captions[start:end]):
        vec = embed_text_batch[i]
        if vec is None or len(vec) != dim_map[kind]:
            ret.append("Error")
        else:
            DB.insert_vector(sid, kind, vec)
            ret.append("OK")
    return ret

with ThreadPoolExecutor(max_workers=MAX_WORKERS) as pool:
    results = list(chain.from_iterable(pool.map(process_caption_block, blocks)))

print(f"共处理 {len(results)} 个caption embedding")
stat = defaultdict(int)
for res in results:
    stat[res] += 1
print("处理结果统计：")
for k, v in stat.items():
    print(f"{k}: {v}")

# exit(0)

# for pack_id, stickerset in packs[:3]:  # limit to top n packs
#     pack = known_packs.get(pack_id, {})
#     print(f"{pack_cnt[pack_id]} Pack ID: {pack_id}, Count: {pack.get('cnt', 0)}, Title: {pack.get('title', 'Unknown')}, Short Name: {pack.get('short_name', 'Unknown')}")
#     short_name = pack.get('short_name', 'unknown')
#     if not os.path.exists(f"stickers/{short_name}"):
#         print(f"not found pack {short_name}, skipping")
#         continue
#     for sidx, sticker in enumerate(known_packs[pack_id]["stickers"]):
#         sid = sticker.id
#         print('now processing sticker:', sid, 'from pack', pack_id, sidx + 1, '/', len(known_packs[pack_id]["stickers"]))
#         dbs = DB.get_sticker(sid)
#         if not dbs:
#             DB.insert_sticker(
#                 sid,
#                 sticker.access_hash,
#                 sticker.file_reference,
#                 {
#                     # 'emoji': sticker.emoticon or '',
#                     'pack_name': short_name,
#                     'mime_type': sticker.mime_type or '',
#                     'created_at': sticker.date.strftime('%Y-%m-%d %H:%M:%S'),
#                 }
#             )
#             dbs = DB.get_sticker(sid)
#         # check if all captions are already set
#         if all([dbs.get(s) for s in ['caption_en', 'caption_zh']]):
#             print(f"already have captions for sticker {sid}, skipping")
#             continue

#         ext = 'webp'
#         for attr in sticker.attributes:
#             if isinstance(attr, types.DocumentAttributeFilename):
#                 ext = attr.file_name.split('.')[-1]
#         if ext != 'webp':
#             print(f"not webp sticker {sid}: {ext}, skipping")
#             continue
#         filename = f"stickers/{short_name}/{sid}.{ext}"
#         if not os.path.exists(filename):
#             print(f"not found sticker {filename}, skipping")
#             continue
#         img = open(filename, 'rb').read()
#         captions = caption_model.caption(filename)
#         print(f"Caption for sticker {sid}:")
#         for k, v in captions.items():
#             print(f"  {k}: {v}")
#         values = {}
#         for k, v in [('caption_en', captions['p']), ('caption_zh', captions['c']), ('ocr', captions['t'])]:
#             if v and len(v.strip()) > 0:
#                 values[k] = v.strip()
#         if len(values) > 0:
#             print(f"Updating sticker {sid} with captions: {values}")
#             DB.update_sticker(sid, values)
#         else:
#             print(f"No captions found for sticker {filename}, skipping")
#             continue

# # ========== 多线程并发处理sticker caption ===========
# import threading
# import queue
# import time

# THREAD_NUM = 8  # 推荐并发数8~10

# def process_sticker_batch(sticker_batch, db: StickerDB, caption_model: CaptionModel, thread_id=0):
#     """
#     每个线程处理一批sticker，独立cursor，避免DuckDB线程安全问题。
#     """
#     local_cursor = db.conn.cursor()
#     for item in sticker_batch:
#         sid, short_name, ext, filename, sticker = item
#         try:
#             dbs = db.get_sticker(sid)
#             if all([dbs.get(s) for s in ['caption_en', 'caption_zh']]):
#                 print(f"[T{thread_id}] already have captions for sticker {sid}, skipping")
#                 continue
#             if not os.path.exists(filename):
#                 print(f"[T{thread_id}] not found sticker {filename}, skipping")
#                 continue
#             captions = caption_model.caption(filename)
#             print(f"[T{thread_id}] Caption for sticker {sid}:")
#             for k, v in captions.items():
#                 print(f"  {k}: {v}")
#             values = {}
#             for k, v in [('caption_en', captions['p']), ('caption_zh', captions['c']), ('ocr', captions['t'])]:
#                 if v and len(v.strip()) > 0:
#                     values[k] = v.strip()
#             if len(values) > 0:
#                 print(f"[T{thread_id}] Updating sticker {sid} with captions: {values}")
#                 # 用独立cursor执行SQL
#                 sql = f"INSERT OR REPLACE INTO stickers (id, {','.join(values.keys())}) VALUES (?, {','.join('?' for _ in values)})"
#                 local_cursor.execute(sql, [sid] + list(values.values()))
#                 db._sync_fts(sid, values)
#             else:
#                 print(f"[T{thread_id}] No captions found for sticker {filename}, skipping")
#         except Exception as e:
#             print(f"[T{thread_id}] Error processing sticker {sid}: {e}")
#     local_cursor.close()

# # 收集所有待处理sticker
# all_stickers = []
# for pack_id, stickerset in packs[:3]:
#     pack = known_packs.get(pack_id, {})
#     short_name = pack.get('short_name', 'unknown')
#     if not os.path.exists(f"stickers/{short_name}"):
#         continue
#     for sidx, sticker in enumerate(known_packs[pack_id]["stickers"]):
#         sid = sticker.id
#         ext = 'webp'
#         for attr in sticker.attributes:
#             if isinstance(attr, types.DocumentAttributeFilename):
#                 ext = attr.file_name.split('.')[-1]
#         if ext != 'webp':
#             continue
#         filename = f"stickers/{short_name}/{sid}.{ext}"
#         all_stickers.append((sid, short_name, ext, filename, sticker))

# # 分批分配到各线程
# batches = [[] for _ in range(THREAD_NUM)]
# for idx, item in enumerate(all_stickers):
#     batches[idx % THREAD_NUM].append(item)

# threads = []
# for i in range(THREAD_NUM):
#     t = threading.Thread(target=process_sticker_batch, args=(batches[i], DB, caption_model, i))
#     threads.append(t)
#     t.start()

# for t in threads:
#     t.join()

# print("多线程caption处理全部完成！")
# # ========== 并发处理结束 ===========

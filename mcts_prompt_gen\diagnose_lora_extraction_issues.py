#!/usr/bin/env python3
"""
Diagnostic script to analyze LoRA extraction issues in different ComfyUI workflow formats.
This script will examine the specific failing test cases and identify workflow format differences.
"""

import os
import sys
import json
from pathlib import Path
from PIL import Image

# Add src directory to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

try:
    from utils import (
        image_info,
        extract_comfyui_workflow_params,
        convert_api_workflow_to_standard,
        traverse_workflow_graph,
        extract_connected_lora_nodes
    )
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    print(f"Src path: {src_path}")
    print(f"Files in src: {os.listdir(src_path) if os.path.exists(src_path) else 'src directory not found'}")
    sys.exit(1)

def analyze_failing_image(image_path: str, expected_loras: list):
    """
    Analyze a specific failing image to understand workflow format and LoRA extraction issues.
    
    Args:
        image_path: Path to the failing image
        expected_loras: List of expected LoRA extractions in format ["filename:weight", ...]
    """
    print(f"\n{'='*80}")
    print(f"ANALYZING: {image_path}")
    print(f"EXPECTED LoRAs: {expected_loras}")
    print(f"{'='*80}")
    
    if not os.path.exists(image_path):
        print(f"❌ ERROR: Image file does not exist: {image_path}")
        return
    
    try:
        with Image.open(image_path) as img:
            print(f"✅ Image loaded successfully")
            print(f"   Size: {img.size}")
            print(f"   Mode: {img.mode}")
            print(f"   Format: {img.format}")
            
            # Step 1: Extract basic image info
            print(f"\n{'-'*60}")
            print("STEP 1: BASIC IMAGE INFO EXTRACTION")
            print(f"{'-'*60}")
            
            info = image_info(img, image_path, enhanced_prompts=True)
            print(f"Extracted info keys: {list(info.keys())}")
            
            if 'prompt' in info:
                prompt = info['prompt']
                print(f"Prompt length: {len(prompt)}")
                print(f"Prompt preview: {prompt[:200]}...")
            else:
                print("❌ No prompt found in image info")
            
            # Step 2: Raw metadata analysis
            print(f"\n{'-'*60}")
            print("STEP 2: RAW METADATA ANALYSIS")
            print(f"{'-'*60}")
            
            print(f"PNG info keys: {list(img.info.keys()) if hasattr(img, 'info') else 'None'}")
            
            # Look for workflow data
            workflow_found = False
            workflow_json = None
            
            # Method 1: Direct workflow key
            if hasattr(img, 'info') and 'workflow' in img.info:
                print("✅ Found 'workflow' in image.info")
                workflow_data = img.info['workflow']
                print(f"   Workflow data type: {type(workflow_data)}")
                print(f"   Workflow data length: {len(str(workflow_data))}")
                
                if isinstance(workflow_data, str):
                    workflow_json = workflow_data
                elif isinstance(workflow_data, dict):
                    workflow_json = json.dumps(workflow_data)
                workflow_found = True
            
            # Method 2: Check other potential workflow keys
            workflow_keys = ['Workflow', 'ComfyUI_workflow', 'workflow_api', 'api']
            for key in workflow_keys:
                if hasattr(img, 'info') and key in img.info:
                    print(f"✅ Found '{key}' in image.info")
                    workflow_data = img.info[key]
                    print(f"   {key} data type: {type(workflow_data)}")
                    print(f"   {key} data length: {len(str(workflow_data))}")
                    
                    if not workflow_found:  # Use first found workflow
                        if isinstance(workflow_data, str):
                            workflow_json = workflow_data
                        elif isinstance(workflow_data, dict):
                            workflow_json = json.dumps(workflow_data)
                        workflow_found = True
            
            if not workflow_found:
                print("❌ No workflow data found in any standard keys")
                return
            
            # Step 3: Workflow format analysis
            print(f"\n{'-'*60}")
            print("STEP 3: WORKFLOW FORMAT ANALYSIS")
            print(f"{'-'*60}")
            
            try:
                workflow = json.loads(workflow_json)
                print(f"✅ Workflow JSON parsed successfully")
                print(f"   Workflow type: {type(workflow)}")
                print(f"   Workflow keys: {list(workflow.keys()) if isinstance(workflow, dict) else 'Not a dict'}")
                
                # Detect workflow format
                if isinstance(workflow, dict):
                    if 'nodes' in workflow and isinstance(workflow['nodes'], list):
                        print("✅ DETECTED: ComfyUI API format (has 'nodes' list)")
                        format_type = "API"
                        
                        # Analyze nodes structure
                        nodes = workflow['nodes']
                        print(f"   Number of nodes: {len(nodes)}")
                        
                        # Look for LoRA nodes in API format
                        lora_nodes = []
                        for node in nodes:
                            if isinstance(node, dict) and node.get('type') in ['LoraTagLoader', 'LoraLoader']:
                                lora_nodes.append(node)
                        
                        print(f"   LoRA nodes found: {len(lora_nodes)}")
                        for i, node in enumerate(lora_nodes):
                            print(f"     Node {i+1}: {node.get('type', 'Unknown')} (ID: {node.get('id', 'Unknown')})")
                            if 'inputs' in node:
                                inputs = node['inputs']
                                print(f"       Inputs: {list(inputs.keys()) if isinstance(inputs, dict) else inputs}")
                    
                    elif any(key.isdigit() for key in workflow.keys()):
                        print("✅ DETECTED: ComfyUI Standard format (numeric node IDs)")
                        format_type = "Standard"
                        
                        # Analyze nodes structure
                        print(f"   Number of nodes: {len(workflow)}")
                        
                        # Look for LoRA nodes in standard format
                        lora_nodes = []
                        for node_id, node in workflow.items():
                            if isinstance(node, dict) and node.get('class_type') in ['LoraTagLoader', 'LoraLoader']:
                                lora_nodes.append((node_id, node))
                        
                        print(f"   LoRA nodes found: {len(lora_nodes)}")
                        for node_id, node in lora_nodes:
                            print(f"     Node {node_id}: {node.get('class_type', 'Unknown')}")
                            if 'inputs' in node:
                                inputs = node['inputs']
                                print(f"       Inputs: {list(inputs.keys()) if isinstance(inputs, dict) else inputs}")
                    
                    else:
                        print("❌ UNKNOWN workflow format")
                        format_type = "Unknown"
                        print(f"   Available keys: {list(workflow.keys())}")
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse workflow JSON: {e}")
                return
            
            # Step 4: Current extraction attempt
            print(f"\n{'-'*60}")
            print("STEP 4: CURRENT EXTRACTION ATTEMPT")
            print(f"{'-'*60}")
            
            try:
                params = extract_comfyui_workflow_params(workflow_json)
                extracted_loras = params.get('loras', [])
                print(f"Current extraction result: {extracted_loras}")
                print(f"Expected: {expected_loras}")
                
                # Compare results
                if set(extracted_loras) == set(expected_loras):
                    print("✅ EXTRACTION SUCCESSFUL - matches expected results")
                else:
                    print("❌ EXTRACTION FAILED - does not match expected results")
                    missing = set(expected_loras) - set(extracted_loras)
                    extra = set(extracted_loras) - set(expected_loras)
                    if missing:
                        print(f"   Missing LoRAs: {missing}")
                    if extra:
                        print(f"   Extra LoRAs: {extra}")
                
            except Exception as e:
                print(f"❌ Extraction failed with error: {e}")
            
            # Step 5: Detailed workflow analysis
            print(f"\n{'-'*60}")
            print("STEP 5: DETAILED WORKFLOW ANALYSIS")
            print(f"{'-'*60}")
            
            try:
                # Convert to standard format
                standard_workflow = convert_api_workflow_to_standard(workflow)
                print(f"✅ Converted to standard format")
                print(f"   Standard workflow nodes: {len(standard_workflow)}")
                
                # Traverse graph
                connected_nodes = traverse_workflow_graph(standard_workflow)
                print(f"✅ Graph traversal completed")
                print(f"   Connected nodes: {len(connected_nodes)}")
                print(f"   Connected node IDs: {sorted(connected_nodes)}")
                
                # Extract connected LoRAs
                connected_loras = extract_connected_lora_nodes(standard_workflow, connected_nodes)
                print(f"✅ Connected LoRA extraction completed")
                print(f"   Connected LoRAs: {connected_loras}")
                
            except Exception as e:
                print(f"❌ Detailed analysis failed: {e}")
                import traceback
                traceback.print_exc()
    
    except Exception as e:
        print(f"❌ Failed to analyze image: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function to analyze all failing test cases."""
    print("LoRA EXTRACTION DIAGNOSTIC TOOL")
    print("="*80)
    
    # Define failing test cases with expected results
    failing_cases = [
        {
            "path": r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png",
            "expected": ["luce2_Noob75XL.safetensors:0.6"]
        },
        {
            "path": r"F:\SD-webui\ComfyUI\output\2025-04-22\ComfyUI_00212_.png", 
            "expected": ["prts_sn59.safetensors:0.8"]
        },
        {
            "path": r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png",
            "expected": [
                "a31_style_koni-000010.safetensors:0.8",
                "outline_xl_kohaku_delta_spv5x.safetensors:-1.0", 
                "Hoseki_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9"
            ]
        }
    ]
    
    # Analyze each failing case
    for i, case in enumerate(failing_cases, 1):
        print(f"\n\nTEST CASE {i}/{len(failing_cases)}")
        analyze_failing_image(case["path"], case["expected"])
    
    print(f"\n\n{'='*80}")
    print("DIAGNOSTIC ANALYSIS COMPLETE")
    print(f"{'='*80}")


if __name__ == "__main__":
    main()

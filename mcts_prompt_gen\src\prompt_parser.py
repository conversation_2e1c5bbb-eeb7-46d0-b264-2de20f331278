#!/usr/bin/env python3
"""
Advanced Prompt Parser for Structured Tag Extraction

This module provides comprehensive parsing of enhanced prompts into structured
(tag, weight) pairs, handling complex weight syntax, technical parameters,
and iterative cleaning processes.

Author: AI Assistant
Date: 2025-06-23
"""

import re
import json
from typing import List, Tuple, Dict, Any, Optional, Set
from collections import Counter, defaultdict
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PromptParser:
    """Advanced prompt parser with iterative cleaning and validation."""
    
    def __init__(self):
        self.tag_frequency = Counter()
        self.weight_stats = defaultdict(list)
        self.parsing_errors = []
        self.cleaning_iterations = 0
        
        # Compile regex patterns for efficiency
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for tag parsing."""
        # Technical parameter patterns
        self.tech_param_patterns = {
            'cfg': re.compile(r'--cfg\s+([0-9.]+)'),
            'steps': re.compile(r'--steps\s+(\d+)'),
            'size': re.compile(r'--size\s+(\d+)x(\d+)'),
            'sampler': re.compile(r'--sampler\s+([a-zA-Z_]+)'),
            'scheduler': re.compile(r'--scheduler\s+([a-zA-Z_]+)'),
            'model': re.compile(r'--model\s+([^\s]+)'),
            'lora': re.compile(r'--lora\s+([^\s:]+)(?::([0-9.]+))?'),
            'seed': re.compile(r'--seed\s+(\d+)'),
            'denoise': re.compile(r'--denoise\s+([0-9.]+)')
        }
        
        # Weight syntax patterns
        self.weight_patterns = {
            'explicit': re.compile(r'\(([^:)]+):([0-9.]+)\)'),  # (tag:1.2)
            'curly_single': re.compile(r'\{([^}]+)\}'),         # {tag}
            'curly_double': re.compile(r'\{\{([^}]+)\}\}'),     # {{tag}}
            'curly_triple': re.compile(r'\{\{\{([^}]+)\}\}\}'), # {{{tag}}}
            'square_single': re.compile(r'\[([^\]]+)\]'),       # [tag]
            'square_double': re.compile(r'\[\[([^\]]+)\]\]'),   # [[tag]]
            'nested_complex': re.compile(r'\(([^:)]+):([0-9.]+)\)|\{+([^}]+)\}+|\[+([^\]]+)\]+')
        }
        
        # Tag splitting patterns
        self.split_patterns = [
            re.compile(r'[,，；;]\s*'),  # Comma, full-width comma, semicolon (both types)
            re.compile(r'\n+'),        # Newlines
            re.compile(r'\s{2,}')      # Multiple spaces
        ]
        
        # Tag validation patterns
        self.invalid_tag_patterns = [
            re.compile(r'^--\w+'),     # Technical parameters (should be handled separately)
            re.compile(r'^\s*$'),      # Empty or whitespace only
            re.compile(r'^[0-9.]+$'),  # Numbers only
            re.compile(r'[<>{}[\]()]{3,}')  # Excessive brackets
        ]
    
    def extract_technical_parameters(self, prompt: str) -> Tuple[Dict[str, Any], str]:
        """
        Extract technical parameters from prompt and return cleaned prompt.
        
        Args:
            prompt: Enhanced prompt string with technical parameters
            
        Returns:
            Tuple of (technical_params_dict, cleaned_prompt_string)
        """
        tech_params = {}
        cleaned_prompt = prompt
        
        # Extract each type of technical parameter
        for param_name, pattern in self.tech_param_patterns.items():
            matches = pattern.findall(cleaned_prompt)
            
            if param_name == 'size' and matches:
                # Handle size specially (width x height)
                width, height = matches[0]
                tech_params['width'] = int(width)
                tech_params['height'] = int(height)
                tech_params['aspect_ratio'] = int(width) / int(height)
                tech_params['megapixels'] = (int(width) * int(height)) / 1000000
                
            elif param_name == 'lora' and matches:
                # Handle LoRAs (can be multiple)
                loras = []
                for match in matches:
                    filename = match[0]
                    weight = float(match[1]) if match[1] else 1.0
                    loras.append((filename, weight))
                tech_params['loras'] = loras
                
            elif matches:
                # Handle single-value parameters
                value = matches[0]
                if param_name in ['cfg', 'denoise']:
                    tech_params[param_name] = float(value)
                elif param_name == 'steps':
                    tech_params[param_name] = int(value)
                else:
                    tech_params[param_name] = value
        
        # Remove technical parameters from prompt
        for pattern in self.tech_param_patterns.values():
            cleaned_prompt = pattern.sub('', cleaned_prompt)
        
        # Clean up extra whitespace
        cleaned_prompt = re.sub(r'\s+', ' ', cleaned_prompt).strip()
        
        return tech_params, cleaned_prompt
    
    def parse_weight_syntax(self, text: str) -> List[Tuple[str, float]]:
        """
        Parse weight syntax from text and return (tag, weight) pairs.
        
        Args:
            text: Text containing weight syntax
            
        Returns:
            List of (tag, weight) tuples
        """
        tags_with_weights = []
        processed_text = text
        
        # Handle explicit weights: (tag:1.2)
        for match in self.weight_patterns['explicit'].finditer(text):
            tag = match.group(1).strip()
            weight = float(match.group(2))
            tags_with_weights.append((tag, weight))
            processed_text = processed_text.replace(match.group(0), '', 1)
        
        # Handle nested curly braces: {{{tag}}} = 1.3, {{tag}} = 1.2, {tag} = 1.1
        for match in self.weight_patterns['curly_triple'].finditer(processed_text):
            tag = match.group(1).strip()
            tags_with_weights.append((tag, 1.3))
            processed_text = processed_text.replace(match.group(0), '', 1)
        
        for match in self.weight_patterns['curly_double'].finditer(processed_text):
            tag = match.group(1).strip()
            tags_with_weights.append((tag, 1.2))
            processed_text = processed_text.replace(match.group(0), '', 1)
        
        for match in self.weight_patterns['curly_single'].finditer(processed_text):
            tag = match.group(1).strip()
            tags_with_weights.append((tag, 1.1))
            processed_text = processed_text.replace(match.group(0), '', 1)
        
        # Handle square brackets: [[tag]] = 0.8, [tag] = 0.9
        for match in self.weight_patterns['square_double'].finditer(processed_text):
            tag = match.group(1).strip()
            tags_with_weights.append((tag, 0.8))
            processed_text = processed_text.replace(match.group(0), '', 1)
        
        for match in self.weight_patterns['square_single'].finditer(processed_text):
            tag = match.group(1).strip()
            tags_with_weights.append((tag, 0.9))
            processed_text = processed_text.replace(match.group(0), '', 1)
        
        return tags_with_weights, processed_text
    
    def split_into_tags(self, text: str) -> List[str]:
        """
        Split text into individual tags using multiple delimiters.
        
        Args:
            text: Text to split into tags
            
        Returns:
            List of individual tag strings
        """
        # Start with the original text
        current_text = text
        
        # Apply each splitting pattern
        for pattern in self.split_patterns:
            # Split by pattern and rejoin with a standard separator
            parts = pattern.split(current_text)
            current_text = '|||'.join(parts)  # Use unique separator
        
        # Split by our unique separator and clean up
        raw_tags = current_text.split('|||')
        
        # Clean and filter tags
        cleaned_tags = []
        for tag in raw_tags:
            tag = tag.strip()
            if tag and not any(pattern.match(tag) for pattern in self.invalid_tag_patterns):
                cleaned_tags.append(tag)
        
        return cleaned_tags
    
    def normalize_tag(self, tag: str) -> str:
        """
        Normalize tag variations to standard forms.
        
        Args:
            tag: Raw tag string
            
        Returns:
            Normalized tag string
        """
        # Convert to lowercase for normalization
        normalized = tag.lower().strip()
        
        # Common normalizations
        normalizations = {
            '1 girl': '1girl',
            '1 boy': '1boy',
            'master piece': 'masterpiece',
            'best quality': 'best_quality',
            'high quality': 'high_quality',
            'very detailed': 'very_detailed',
            'extremely detailed': 'extremely_detailed',
            'ultra detailed': 'ultra_detailed',
            'detailed face': 'detailed_face',
            'detailed eyes': 'detailed_eyes',
            'beautiful eyes': 'beautiful_eyes',
            'long hair': 'long_hair',
            'short hair': 'short_hair',
            'blue eyes': 'blue_eyes',
            'brown eyes': 'brown_eyes',
            'green eyes': 'green_eyes',
            'red hair': 'red_hair',
            'blue hair': 'blue_hair',
            'brown hair': 'brown_hair',
            'black hair': 'black_hair',
            'white hair': 'white_hair',
            'blonde hair': 'blonde_hair'
        }
        
        # Apply normalizations
        for old_form, new_form in normalizations.items():
            if normalized == old_form:
                return new_form
        
        # Remove extra spaces and replace spaces with underscores for multi-word tags
        normalized = re.sub(r'\s+', '_', normalized)
        
        # Remove special characters that might cause issues
        normalized = re.sub(r'[^\w\-_]', '', normalized)
        
        return normalized if normalized else tag  # Return original if normalization failed
    
    def validate_tag(self, tag: str, weight: float) -> Tuple[bool, str]:
        """
        Validate a tag and weight pair.
        
        Args:
            tag: Tag string to validate
            weight: Weight value to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Tag length validation
        if len(tag) < 1:
            return False, "Tag is empty"
        if len(tag) > 100:
            return False, f"Tag too long ({len(tag)} characters)"
        
        # Weight validation for text tags
        if not tag.startswith(('cfg_', 'sampling_', 'width', 'height', 'lora_', 'sampler')):
            if weight < 0.1 or weight > 2.0:
                return False, f"Weight {weight} outside valid range (0.1-2.0)"
        
        # Character validation
        if not re.match(r'^[\w\-_\s]+$', tag):
            return False, f"Tag contains invalid characters: {tag}"
        
        return True, ""
    
    def parse_enhanced_prompt(self, prompt: str) -> Dict[str, Any]:
        """
        Parse enhanced prompt into structured format.
        
        Args:
            prompt: Enhanced prompt string with technical parameters
            
        Returns:
            Structured prompt dictionary
        """
        try:
            # Step 1: Extract technical parameters
            tech_params, text_prompt = self.extract_technical_parameters(prompt)
            
            # Step 2: Parse weight syntax from remaining text
            weighted_tags, remaining_text = self.parse_weight_syntax(text_prompt)
            
            # Step 3: Split remaining text into individual tags
            raw_tags = self.split_into_tags(remaining_text)
            
            # Step 4: Add unweighted tags with default weight
            for tag in raw_tags:
                if tag.strip():
                    normalized_tag = self.normalize_tag(tag)
                    if normalized_tag:
                        weighted_tags.append((normalized_tag, 1.0))
            
            # Step 5: Convert technical parameters to special tags
            tech_tags = []
            for param, value in tech_params.items():
                if param == 'loras':
                    for lora_name, lora_weight in value:
                        # Clean LoRA filename
                        clean_name = lora_name.replace('.safetensors', '').replace('.ckpt', '')
                        tech_tags.append((f'lora_{clean_name}', lora_weight))
                elif param in ['width', 'height', 'aspect_ratio', 'megapixels']:
                    tech_tags.append((param, float(value)))
                elif param in ['cfg', 'steps']:
                    param_name = 'cfg_scale' if param == 'cfg' else 'sampling_steps'
                    tech_tags.append((param_name, float(value)))
                else:
                    # For string parameters, use weight 1.0 to avoid type issues
                    tech_tags.append((param, 1.0))
            
            # Step 6: Combine all tags and validate
            all_tags = weighted_tags + tech_tags
            validated_tags = []
            
            for tag, weight in all_tags:
                is_valid, error_msg = self.validate_tag(tag, weight)
                if is_valid:
                    validated_tags.append((tag, weight))
                    self.tag_frequency[tag] += 1
                    self.weight_stats[tag].append(weight)
                else:
                    self.parsing_errors.append(f"Invalid tag '{tag}': {error_msg}")
            
            # Step 7: Create structured prompt
            structured_prompt = {
                'raw_text': prompt,
                'text_prompt': text_prompt,
                'tags': validated_tags,
                'technical_params': tech_params,
                'tag_count': len(validated_tags),
                'avg_weight': sum(w for _, w in validated_tags) / len(validated_tags) if validated_tags else 1.0,
                'weight_variance': self._calculate_weight_variance(validated_tags)
            }
            
            return structured_prompt
            
        except Exception as e:
            logger.error(f"Error parsing prompt '{prompt[:50]}...': {e}")
            self.parsing_errors.append(f"Parsing error: {e}")
            
            # Return minimal structure on error
            return {
                'raw_text': prompt,
                'text_prompt': prompt,
                'tags': [('parsing_error', 1.0)],
                'technical_params': {},
                'tag_count': 1,
                'avg_weight': 1.0,
                'weight_variance': 0.0
            }
    
    def _calculate_weight_variance(self, tags: List[Tuple[str, float]]) -> float:
        """Calculate variance of weights in tag list."""
        if len(tags) < 2:
            return 0.0
        
        weights = [w for _, w in tags]
        mean_weight = sum(weights) / len(weights)
        variance = sum((w - mean_weight) ** 2 for w in weights) / len(weights)
        return variance
    
    def reconstruct_prompt(self, structured_prompt: Dict[str, Any]) -> str:
        """
        Reconstruct prompt string from structured format.
        
        Args:
            structured_prompt: Structured prompt dictionary
            
        Returns:
            Reconstructed prompt string
        """
        parts = []
        
        # Add text tags with weights
        text_tags = []
        tech_tags = []
        
        for tag, weight in structured_prompt['tags']:
            if tag.startswith(('cfg_', 'sampling_', 'width', 'height', 'lora_', 'sampler')):
                tech_tags.append((tag, weight))
            else:
                text_tags.append((tag, weight))
        
        # Format text tags
        for tag, weight in text_tags:
            if weight == 1.0:
                parts.append(tag)
            elif weight > 1.0:
                if weight == 1.1:
                    parts.append(f'{{{tag}}}')
                elif weight == 1.2:
                    parts.append(f'{{{{{tag}}}}}')
                elif weight == 1.3:
                    parts.append(f'{{{{{{{tag}}}}}}}')
                else:
                    parts.append(f'({tag}:{weight})')
            else:
                if weight == 0.9:
                    parts.append(f'[{tag}]')
                elif weight == 0.8:
                    parts.append(f'[[{tag}]]')
                else:
                    parts.append(f'({tag}:{weight})')
        
        # Add technical parameters
        tech_params = structured_prompt.get('technical_params', {})
        if 'cfg' in tech_params:
            parts.append(f"--cfg {tech_params['cfg']}")
        if 'steps' in tech_params:
            parts.append(f"--steps {tech_params['steps']}")
        if 'width' in tech_params and 'height' in tech_params:
            parts.append(f"--size {tech_params['width']}x{tech_params['height']}")
        if 'loras' in tech_params:
            for lora_name, lora_weight in tech_params['loras']:
                parts.append(f"--lora {lora_name}:{lora_weight}")
        
        return ', '.join(parts)
    
    def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive parsing statistics."""
        total_tags = sum(self.tag_frequency.values())
        unique_tags = len(self.tag_frequency)
        
        # Calculate weight statistics
        weight_stats = {}
        for tag, weights in self.weight_stats.items():
            # Convert all weights to float to avoid type errors
            numeric_weights = []
            for w in weights:
                try:
                    numeric_weights.append(float(w))
                except (ValueError, TypeError):
                    # Skip non-numeric weights
                    continue

            if numeric_weights:
                weight_stats[tag] = {
                    'count': len(numeric_weights),
                    'mean': sum(numeric_weights) / len(numeric_weights),
                    'min': min(numeric_weights),
                    'max': max(numeric_weights)
                }
            else:
                weight_stats[tag] = {
                    'count': 0,
                    'mean': 0.0,
                    'min': 0.0,
                    'max': 0.0
                }
        
        return {
            'total_tags_parsed': total_tags,
            'unique_tags': unique_tags,
            'parsing_errors': len(self.parsing_errors),
            'error_rate': len(self.parsing_errors) / max(total_tags, 1),
            'most_common_tags': self.tag_frequency.most_common(20),
            'rare_tags': [(tag, count) for tag, count in self.tag_frequency.items() if count <= 3],
            'weight_statistics': weight_stats,
            'cleaning_iterations': self.cleaning_iterations
        }

    def clean_tag_iteratively(self, tag: str, context: Dict[str, Any] = None) -> str:
        """
        Clean a tag iteratively based on context and common patterns.

        Args:
            tag: Tag to clean
            context: Additional context for cleaning decisions

        Returns:
            Cleaned tag string
        """
        if not context:
            context = {}

        cleaned = tag.strip()

        # Iteration 1: Fix obvious parsing errors
        # Remove malformed brackets
        cleaned = re.sub(r'[{}[\]()]+$', '', cleaned)  # Trailing brackets
        cleaned = re.sub(r'^[{}[\]()]+', '', cleaned)  # Leading brackets

        # Fix missing separators (heuristic)
        # If tag is very long and contains multiple concepts, try to split
        if len(cleaned) > 30 and ' ' in cleaned:
            words = cleaned.split()
            if len(words) > 3:
                # This might be multiple tags merged together
                # For now, just take the first reasonable part
                cleaned = ' '.join(words[:2])

        # Iteration 2: Handle special cases
        # Artist names with commas - preserve them
        artist_indicators = ['by ', 'artist:', 'drawn by', 'art by']
        is_artist_tag = any(indicator in cleaned.lower() for indicator in artist_indicators)

        if is_artist_tag:
            # Preserve artist names as-is, just clean whitespace
            cleaned = re.sub(r'\s+', ' ', cleaned)
        else:
            # Regular tag cleaning
            # Remove parentheses that aren't part of weight syntax
            if not re.match(r'.*:[0-9.]+\)$', cleaned):
                cleaned = re.sub(r'[()]', '', cleaned)

        # Iteration 3: Normalize variations
        cleaned = self.normalize_tag(cleaned)

        # Iteration 4: Final validation
        if len(cleaned) < 1 or len(cleaned) > 100:
            return tag  # Return original if cleaning failed

        return cleaned

    def validate_tags(self, tag_list: List[Tuple[str, float]]) -> Dict[str, Any]:
        """
        Validate a list of tags and return comprehensive report.

        Args:
            tag_list: List of (tag, weight) tuples

        Returns:
            Validation report dictionary
        """
        report = {
            'total_tags': len(tag_list),
            'valid_tags': 0,
            'invalid_tags': 0,
            'validation_errors': [],
            'tag_length_stats': {'min': 0, 'max': 0, 'avg': 0},
            'weight_stats': {'min': 0, 'max': 0, 'avg': 0},
            'duplicate_tags': [],
            'rare_tags': [],
            'technical_tags': 0,
            'text_tags': 0
        }

        if not tag_list:
            return report

        # Track seen tags for duplicate detection
        seen_tags = {}
        tag_lengths = []
        weights = []

        for tag, weight in tag_list:
            tag_lengths.append(len(tag))
            weights.append(weight)

            # Check for duplicates
            if tag in seen_tags:
                report['duplicate_tags'].append((tag, seen_tags[tag], weight))
            else:
                seen_tags[tag] = weight

            # Validate tag
            is_valid, error_msg = self.validate_tag(tag, weight)
            if is_valid:
                report['valid_tags'] += 1

                # Categorize tag
                if tag.startswith(('cfg_', 'sampling_', 'width', 'height', 'lora_', 'sampler')):
                    report['technical_tags'] += 1
                else:
                    report['text_tags'] += 1
            else:
                report['invalid_tags'] += 1
                report['validation_errors'].append((tag, weight, error_msg))

        # Calculate statistics
        if tag_lengths:
            report['tag_length_stats'] = {
                'min': min(tag_lengths),
                'max': max(tag_lengths),
                'avg': sum(tag_lengths) / len(tag_lengths)
            }

        if weights:
            report['weight_stats'] = {
                'min': min(weights),
                'max': max(weights),
                'avg': sum(weights) / len(weights)
            }

        # Find rare tags (appearing only once in this list)
        tag_counts = Counter(tag for tag, _ in tag_list)
        report['rare_tags'] = [(tag, count) for tag, count in tag_counts.items() if count == 1]

        return report

    def iterative_cleaning_process(self, prompts: List[str], max_iterations: int = 4) -> List[Dict[str, Any]]:
        """
        Perform iterative cleaning process on a list of prompts.

        Args:
            prompts: List of prompt strings to clean
            max_iterations: Maximum number of cleaning iterations

        Returns:
            List of structured prompts after cleaning
        """
        logger.info(f"Starting iterative cleaning process on {len(prompts)} prompts")

        structured_prompts = []

        for iteration in range(max_iterations):
            self.cleaning_iterations = iteration + 1
            logger.info(f"Cleaning iteration {iteration + 1}/{max_iterations}")

            # Reset statistics for this iteration
            iteration_errors = len(self.parsing_errors)

            # Process prompts
            if iteration == 0:
                # First iteration: parse all prompts
                for prompt in prompts:
                    structured = self.parse_enhanced_prompt(prompt)
                    structured_prompts.append(structured)
            else:
                # Subsequent iterations: re-clean problematic tags
                for i, structured in enumerate(structured_prompts):
                    # Re-clean tags that might need improvement
                    cleaned_tags = []
                    for tag, weight in structured['tags']:
                        if tag != 'parsing_error':  # Skip error tags
                            cleaned_tag = self.clean_tag_iteratively(tag)
                            is_valid, _ = self.validate_tag(cleaned_tag, weight)
                            if is_valid:
                                cleaned_tags.append((cleaned_tag, weight))
                            else:
                                # Try to salvage the tag
                                salvaged_tag = self.normalize_tag(tag)
                                if salvaged_tag and len(salvaged_tag) > 0:
                                    cleaned_tags.append((salvaged_tag, weight))

                    structured['tags'] = cleaned_tags
                    structured['tag_count'] = len(cleaned_tags)
                    if cleaned_tags:
                        structured['avg_weight'] = sum(w for _, w in cleaned_tags) / len(cleaned_tags)
                        structured['weight_variance'] = self._calculate_weight_variance(cleaned_tags)

            # Check improvement
            new_errors = len(self.parsing_errors) - iteration_errors
            logger.info(f"Iteration {iteration + 1} completed. New errors: {new_errors}")

            # Early stopping if error rate is low enough
            total_tags = sum(len(sp['tags']) for sp in structured_prompts)
            error_rate = len(self.parsing_errors) / max(total_tags, 1)

            if error_rate < 0.05:  # Less than 5% error rate
                logger.info(f"Early stopping: error rate {error_rate:.3f} < 0.05")
                break

        logger.info(f"Cleaning process completed after {self.cleaning_iterations} iterations")
        return structured_prompts


def parse_enhanced_prompt(prompt: str) -> Dict[str, Any]:
    """
    Convenience function to parse a single enhanced prompt.

    Args:
        prompt: Enhanced prompt string

    Returns:
        Structured prompt dictionary
    """
    parser = PromptParser()
    return parser.parse_enhanced_prompt(prompt)


def validate_tags(tag_list: List[Tuple[str, float]]) -> Dict[str, Any]:
    """
    Convenience function to validate a list of tags.

    Args:
        tag_list: List of (tag, weight) tuples

    Returns:
        Validation report dictionary
    """
    parser = PromptParser()
    return parser.validate_tags(tag_list)


def clean_tag_iteratively(tag: str, context: Dict[str, Any] = None) -> str:
    """
    Convenience function to clean a single tag.

    Args:
        tag: Tag to clean
        context: Additional context for cleaning

    Returns:
        Cleaned tag string
    """
    parser = PromptParser()
    return parser.clean_tag_iteratively(tag, context)


def reconstruct_prompt(structured_prompt: Dict[str, Any]) -> str:
    """
    Convenience function to reconstruct prompt from structured format.

    Args:
        structured_prompt: Structured prompt dictionary

    Returns:
        Reconstructed prompt string
    """
    parser = PromptParser()
    return parser.reconstruct_prompt(structured_prompt)

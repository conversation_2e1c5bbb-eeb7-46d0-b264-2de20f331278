import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import lightgbm as lgb
from transformers import AutoModelForCausalLM, AutoTokenizer

class ResNetModel(nn.Module):
    def __init__(self, input_dim, hidden_dim=256, num_blocks=4):
        super(ResNetModel, self).__init__()
        self.input_layer = nn.Linear(input_dim, hidden_dim)
        self.blocks = nn.ModuleList([
            ResidualBlock(hidden_dim) for _ in range(num_blocks)
        ])
        self.policy_head = nn.Linear(hidden_dim, input_dim)  # For action probabilities
        self.value_head = nn.Linear(hidden_dim, 1)  # For state evaluation
        
    def forward(self, x):
        x = F.relu(self.input_layer(x))
        for block in self.blocks:
            x = block(x)
        policy = F.softmax(self.policy_head(x), dim=-1)
        value = torch.tanh(self.value_head(x))
        return policy, value

class ResidualBlock(nn.Module):
    def __init__(self, hidden_dim):
        super(ResidualBlock, self).__init__()
        self.layer1 = nn.Linear(hidden_dim, hidden_dim)
        self.layer2 = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x):
        residual = x
        x = F.relu(self.layer1(x))
        x = self.layer2(x)
        return F.relu(x + residual)

class LightGBMModel:
    def __init__(self, params=None):
        self.params = params or {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9
        }
        self.policy_model = None
        self.value_model = None
        
    def train(self, X, policy_targets, value_targets):
        policy_train = lgb.Dataset(X, policy_targets)
        value_train = lgb.Dataset(X, value_targets)
        
        self.policy_model = lgb.train(self.params, policy_train, num_boost_round=100)
        self.value_model = lgb.train(self.params, value_train, num_boost_round=100)
    
    def predict(self, X):
        policy = self.policy_model.predict(X)
        value = self.value_model.predict(X)
        return policy, value

class LLMWrapper:
    def __init__(self, model_name="gpt2", device="cuda" if torch.cuda.is_available() else "cpu"):
        self.device = device
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name).to(device)
        
    def generate_prompt(self, context, max_length=100):
        inputs = self.tokenizer(context, return_tensors="pt").to(self.device)
        outputs = self.model.generate(
            inputs.input_ids, 
            max_length=max_length, 
            do_sample=True, 
            top_p=0.95,
            temperature=0.8
        )
        return self.tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    def get_next_token_probs(self, context):
        inputs = self.tokenizer(context, return_tensors="pt").to(self.device)
        with torch.no_grad():
            outputs = self.model(inputs.input_ids)
            logits = outputs.logits[:, -1, :]
            probs = F.softmax(logits, dim=-1)
        return probs.cpu().numpy()
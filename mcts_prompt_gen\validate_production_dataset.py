#!/usr/bin/env python3
"""
Production Dataset Validation Script

This script provides comprehensive validation for the production dataset builder,
including regression testing, quality assurance, and performance benchmarking.

Features:
- Validates against specific test cases
- Compares results with previous extractions
- Checks LoRA filename preservation
- Validates negative weight handling
- Performance and accuracy metrics

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import logging
import pickle
import dill
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional
from pathlib import Path

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PIL import Image
from production_dataset_builder import ProductionDatasetBuilder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProductionDatasetValidator:
    """Comprehensive validator for production dataset builder."""
    
    def __init__(self):
        """Initialize the validator."""
        self.builder = ProductionDatasetBuilder(max_workers=4)  # Use fewer workers for testing
        
        # Extended test cases including edge cases
        self.comprehensive_test_cases = [
            {
                "path": r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png",
                "expected_loras": ["<lora:luce2_Noob75XL>"],
                "expected_weights": [0.6],
                "description": "Single LoRA with preserved filename"
            },
            {
                "path": r"F:\SD-webui\ComfyUI\output\2025-04-22\ComfyUI_00212_.png", 
                "expected_loras": ["<lora:prts_sn59>"],
                "expected_weights": [0.8],
                "description": "Character LoRA with underscore"
            },
            {
                "path": r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png",
                "expected_loras": [
                    "<lora:a31_style_koni-000010>",
                    "<lora:outline_xl_kohaku_delta_spv5x>", 
                    "<lora:Hoseki_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1>"
                ],
                "expected_weights": [0.8, -1.0, 0.9],
                "description": "Multiple complex LoRAs with negative weights"
            }
        ]
        
        self.validation_results = {
            'test_cases_passed': 0,
            'test_cases_failed': 0,
            'lora_filename_preservation': 0,
            'negative_weight_handling': 0,
            'parsing_accuracy': 0.0,
            'total_tags_extracted': 0,
            'total_loras_extracted': 0
        }
    
    def validate_lora_format(self, tags: List[Tuple[str, float]]) -> Dict[str, Any]:
        """
        Validate that LoRA tags use the correct <lora:filename>:weight format.
        
        Args:
            tags: List of (tag, weight) tuples
            
        Returns:
            Validation results dictionary
        """
        lora_validation = {
            'total_loras': 0,
            'correct_format': 0,
            'filename_preserved': 0,
            'negative_weights': 0,
            'weight_range_valid': 0
        }
        
        for tag, weight in tags:
            if tag.startswith('<lora:') and tag.endswith('>'):
                lora_validation['total_loras'] += 1
                
                # Check format correctness
                lora_name = tag[6:-1]  # Remove <lora: and >
                if lora_name and not lora_name.isspace():
                    lora_validation['correct_format'] += 1
                
                # Check filename preservation (no excessive normalization)
                if any(char in lora_name for char in ['-', '_', 'A-Z']):
                    lora_validation['filename_preserved'] += 1
                
                # Check negative weight handling
                if weight < 0:
                    lora_validation['negative_weights'] += 1
                
                # Check weight range validity
                if -5.0 <= weight <= 5.0:
                    lora_validation['weight_range_valid'] += 1
        
        return lora_validation
    
    def validate_single_test_case(self, test_case: Dict[str, Any]) -> bool:
        """
        Validate a single test case.
        
        Args:
            test_case: Test case dictionary
            
        Returns:
            True if test case passes, False otherwise
        """
        logger.info(f"Validating: {test_case['description']}")
        
        if not os.path.exists(test_case['path']):
            logger.warning(f"Test image not found: {test_case['path']}")
            return False
        
        try:
            # Extract tags using production builder
            tags, extraction_info = self.builder.extract_prompt_with_special_lora_format(test_case['path'])
            
            if not extraction_info['success']:
                logger.error(f"Extraction failed: {extraction_info['error']}")
                return False
            
            # Find LoRA tags
            found_loras = []
            found_weights = []
            
            for tag, weight in tags:
                if tag.startswith('<lora:') and tag.endswith('>'):
                    found_loras.append(tag)
                    found_weights.append(weight)
            
            # Validate LoRA format
            lora_validation = self.validate_lora_format(tags)
            
            logger.info(f"Expected LoRAs: {test_case['expected_loras']}")
            logger.info(f"Found LoRAs: {found_loras}")
            logger.info(f"Expected weights: {test_case['expected_weights']}")
            logger.info(f"Found weights: {found_weights}")
            
            # Check LoRA matching
            expected_set = set(test_case['expected_loras'])
            found_set = set(found_loras)
            
            loras_match = expected_set == found_set
            
            # Check weight matching (with tolerance)
            weights_match = True
            if len(found_weights) == len(test_case['expected_weights']):
                for i, (expected_weight, found_weight) in enumerate(zip(test_case['expected_weights'], found_weights)):
                    if abs(expected_weight - found_weight) > 0.01:  # 0.01 tolerance
                        weights_match = False
                        break
            else:
                weights_match = False
            
            # Overall test result
            test_passed = loras_match and weights_match
            
            if test_passed:
                logger.info("✅ Test case PASSED")
                self.validation_results['test_cases_passed'] += 1
                
                # Update specific metrics
                self.validation_results['lora_filename_preservation'] += lora_validation['filename_preserved']
                self.validation_results['negative_weight_handling'] += lora_validation['negative_weights']
                self.validation_results['total_loras_extracted'] += lora_validation['total_loras']
                
            else:
                logger.error("❌ Test case FAILED")
                self.validation_results['test_cases_failed'] += 1
                
                if not loras_match:
                    missing = expected_set - found_set
                    extra = found_set - expected_set
                    if missing:
                        logger.error(f"   Missing LoRAs: {missing}")
                    if extra:
                        logger.error(f"   Extra LoRAs: {extra}")
                
                if not weights_match:
                    logger.error(f"   Weight mismatch detected")
            
            self.validation_results['total_tags_extracted'] += len(tags)
            return test_passed
            
        except Exception as e:
            logger.error(f"Error validating test case: {e}")
            self.validation_results['test_cases_failed'] += 1
            return False
    
    def run_comprehensive_validation(self) -> bool:
        """
        Run comprehensive validation on all test cases.
        
        Returns:
            True if all tests pass, False otherwise
        """
        logger.info("Running comprehensive validation...")
        logger.info("=" * 60)
        
        all_passed = True
        
        for i, test_case in enumerate(self.comprehensive_test_cases, 1):
            logger.info(f"\nTest Case {i}/{len(self.comprehensive_test_cases)}")
            logger.info("-" * 40)
            
            test_passed = self.validate_single_test_case(test_case)
            if not test_passed:
                all_passed = False
        
        # Calculate accuracy metrics
        total_tests = len(self.comprehensive_test_cases)
        if total_tests > 0:
            self.validation_results['parsing_accuracy'] = (
                self.validation_results['test_cases_passed'] / total_tests
            ) * 100
        
        return all_passed
    
    def print_validation_report(self):
        """Print comprehensive validation report."""
        logger.info("\n" + "=" * 60)
        logger.info("PRODUCTION DATASET VALIDATION REPORT")
        logger.info("=" * 60)
        
        # Test results
        total_tests = (self.validation_results['test_cases_passed'] + 
                      self.validation_results['test_cases_failed'])
        
        logger.info(f"Test Cases Passed: {self.validation_results['test_cases_passed']}/{total_tests}")
        logger.info(f"Test Cases Failed: {self.validation_results['test_cases_failed']}/{total_tests}")
        logger.info(f"Overall Accuracy: {self.validation_results['parsing_accuracy']:.2f}%")
        
        # LoRA-specific metrics
        logger.info(f"Total LoRAs Extracted: {self.validation_results['total_loras_extracted']}")
        logger.info(f"LoRA Filename Preservation: {self.validation_results['lora_filename_preservation']}")
        logger.info(f"Negative Weight LoRAs: {self.validation_results['negative_weight_handling']}")
        logger.info(f"Total Tags Extracted: {self.validation_results['total_tags_extracted']}")
        
        # Quality assessment
        if self.validation_results['parsing_accuracy'] >= 100.0:
            logger.info("🎉 EXCELLENT: 100% test accuracy achieved!")
        elif self.validation_results['parsing_accuracy'] >= 98.0:
            logger.info("✅ GOOD: >98% accuracy requirement met")
        else:
            logger.warning("⚠️  NEEDS IMPROVEMENT: <98% accuracy")
        
        logger.info("=" * 60)
    
    def validate_production_readiness(self) -> bool:
        """
        Validate that the system is ready for production use.
        
        Returns:
            True if ready for production, False otherwise
        """
        logger.info("Validating production readiness...")
        
        # Run comprehensive validation
        validation_passed = self.run_comprehensive_validation()
        
        # Print report
        self.print_validation_report()
        
        # Check production readiness criteria
        criteria_met = (
            validation_passed and
            self.validation_results['parsing_accuracy'] >= 98.0 and
            self.validation_results['test_cases_failed'] == 0
        )
        
        if criteria_met:
            logger.info("🎉 PRODUCTION READY: All criteria met!")
            return True
        else:
            logger.error("❌ NOT PRODUCTION READY: Some criteria not met")
            return False


def main():
    """Main validation function."""
    validator = ProductionDatasetValidator()
    
    logger.info("Production Dataset Validation")
    logger.info("=" * 60)
    
    # Run validation
    is_ready = validator.validate_production_readiness()
    
    if is_ready:
        logger.info("\n✅ VALIDATION SUCCESSFUL - System ready for production!")
        return True
    else:
        logger.error("\n❌ VALIDATION FAILED - System needs fixes before production!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

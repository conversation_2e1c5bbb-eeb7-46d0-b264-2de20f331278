================================================================================
COMPREHENSIVE STRUCTURED PROMPT DATA ANALYSIS REPORT
================================================================================
Generated: 2025-06-24 02:21:28
Dataset: promptlabels_structured.pkl

📊 DATASET OVERVIEW
----------------------------------------
Total entries: 66,312
Data type: <class 'list'>
Correct format entries: 66,312 (100.0%)
Format issues: 0

🏷️  TAG ANALYSIS
----------------------------------------
Total tags: 2,234,843
Unique tags: 13,063
Average tags per prompt: 33.7
Tag count range: 0 - 131

Top 10 Most Frequent Tags:
   1. very aesthetic: 47,726 occurrences
   2. amazing quality: 47,482 occurrences
   3. 1girl: 47,072 occurrences
   4. best quality: 37,503 occurrences
   5. solo: 35,859 occurrences
   6. absurdres: 33,917 occurrences
   7. sy4: 24,591 occurrences
   8. highly detailed: 21,828 occurrences
   9. full body: 19,760 occurrences
  10. hiten: 18,061 occurrences

⚠️  Long tags found (>100 chars): 283
  • \n\nraincoat\npelvic curtain\n\n\npentacle\nslippe... (118 chars)
  • \n\nmeiji schoolgirl uniform\n\nsweater vest\nbloo... (104 chars)
  • animal costume\n\ncardigan\n\nshrug (clothing)\nga... (142 chars)

⚖️  WEIGHT ANALYSIS
----------------------------------------
Total weights: 2,234,843
Average weight: 1.066
Weight range: 0.010 - 28.000
Weights in normal range (-2.0 to 2.0): 99.3%
⚠️  Weights outside normal range: 16,247

🎨 LORA MODEL ANALYSIS
----------------------------------------
Total LoRA usage: 5,687
Unique LoRA models: 144

Top 10 Most Used LoRA Models:
   1. ciloranko: 3,301 uses
   2. artist:ciloranko: 877 uses
   3. (artist:mika pikazo)[artist:ciloranko]: 207 uses
   4. artist:fujiyama\n\n(artist:mika pikazo)[artist:ciloranko]: 160 uses
   5. floral print: 144 uses
   6. floral_print: 124 uses
   7. \n\n(artist:mika pikazo)[artist:ciloranko]: 108 uses
   8. Ciloranko: 105 uses
   9. \nartist:as109\u00d7ciloranko: 96 uses
  10. ciloranko]]: 66 uses

LoRA Weight Statistics:
  Average: 0.958
  Range: 0.010 - 1.650

⭐ GOODNESS SCORE ANALYSIS
----------------------------------------
Total scores: 66,312
Average goodness: 0.066
Score range: 0.000 - 1.000

Score Distribution:
  Score 0.0: 61,937 (93.4%)
  Score 1.0: 4,375 (6.6%)

✅ VALIDATION RESULTS
----------------------------------------
Overall Parsing Accuracy: 100.00%
Meets 98% Requirement: ✅ YES
Total Issues Found: 0

Accuracy Breakdown:
  ✅ Format Accuracy: 100.00%
  ✅ Tag Accuracy: 100.00%
  ✅ Weight Accuracy: 99.27%

💡 RECOMMENDATIONS
----------------------------------------
🎉 Dataset quality is excellent!
✅ Ready for production use
✅ Parsing accuracy meets requirements
• Investigate and fix long tags (>100 characters)
• Review weights outside normal range (-2.0 to 2.0)

================================================================================
END OF REPORT
================================================================================
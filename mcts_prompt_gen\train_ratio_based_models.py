#!/usr/bin/env python3
"""
Ratio-Based Model Training Script

This script trains goodness prediction models using improved ratio-based labels
instead of binary labels. The ratio-based approach provides more nuanced
training signals by using the proportion of good vs normal ratings for
identical prompt combinations.

Features:
- Ratio-based continuous labels (0.0 to 1.0)
- Comparison with binary labeling approach
- Enhanced evaluation metrics for continuous targets
- Comprehensive performance analysis

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional
import dill
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# Import our models
from goodness_predictor_models import LightGBMGoodnessPredictorModel
from resnet_goodness_model import ResNetGoodnessPredictorModel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RatioBasedModelTrainer:
    """
    Enhanced trainer for ratio-based goodness prediction models.
    """
    
    def __init__(self, binary_dataset_path: str, ratio_dataset_path: str, output_dir: str = "ratio_based_training"):
        """
        Initialize trainer with both binary and ratio-based datasets.
        
        Args:
            binary_dataset_path: Path to binary-labeled dataset
            ratio_dataset_path: Path to ratio-based dataset
            output_dir: Output directory for results
        """
        self.binary_dataset_path = binary_dataset_path
        self.ratio_dataset_path = ratio_dataset_path
        self.output_dir = output_dir
        self.binary_dataset = None
        self.ratio_dataset = None
        self.results = {}
        
        # Create timestamped output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = f"{output_dir}_{timestamp}"
        os.makedirs(self.run_dir, exist_ok=True)
        
        logger.info(f"Training results will be saved to: {self.run_dir}")
    
    def load_datasets(self) -> bool:
        """Load both binary and ratio-based datasets."""
        try:
            # Load binary dataset
            logger.info(f"Loading binary dataset from {self.binary_dataset_path}")
            with open(self.binary_dataset_path, 'rb') as f:
                self.binary_dataset = dill.load(f)
            logger.info(f"Binary dataset loaded: {len(self.binary_dataset)} entries")
            
            # Load ratio-based dataset
            logger.info(f"Loading ratio-based dataset from {self.ratio_dataset_path}")
            with open(self.ratio_dataset_path, 'rb') as f:
                self.ratio_dataset = dill.load(f)
            logger.info(f"Ratio-based dataset loaded: {len(self.ratio_dataset)} entries")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading datasets: {e}")
            return False
    
    def analyze_label_distributions(self):
        """Analyze and compare label distributions."""
        logger.info("Analyzing label distributions...")
        
        # Extract labels
        binary_labels = [goodness for _, _, goodness in self.binary_dataset]
        ratio_labels = [goodness for _, _, goodness in self.ratio_dataset]
        
        # Calculate statistics
        binary_stats = {
            'mean': np.mean(binary_labels),
            'std': np.std(binary_labels),
            'min': np.min(binary_labels),
            'max': np.max(binary_labels),
            'unique_count': len(set(binary_labels)),
            'unique_values': sorted(set(binary_labels))
        }
        
        ratio_stats = {
            'mean': np.mean(ratio_labels),
            'std': np.std(ratio_labels),
            'min': np.min(ratio_labels),
            'max': np.max(ratio_labels),
            'unique_count': len(set(ratio_labels)),
            'unique_values': sorted(set(ratio_labels))
        }
        
        logger.info(f"Binary labels - Mean: {binary_stats['mean']:.4f}, Std: {binary_stats['std']:.4f}, Unique: {binary_stats['unique_count']}")
        logger.info(f"Ratio labels - Mean: {ratio_stats['mean']:.4f}, Std: {ratio_stats['std']:.4f}, Unique: {ratio_stats['unique_count']}")
        
        # Create comparison visualization
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # Binary distribution
        axes[0].hist(binary_labels, bins=min(20, binary_stats['unique_count']), alpha=0.7, color='blue', edgecolor='black')
        axes[0].set_title('Binary Label Distribution')
        axes[0].set_xlabel('Goodness Score')
        axes[0].set_ylabel('Frequency')
        
        # Ratio distribution
        axes[1].hist(ratio_labels, bins=min(20, ratio_stats['unique_count']), alpha=0.7, color='green', edgecolor='black')
        axes[1].set_title('Ratio-Based Label Distribution')
        axes[1].set_xlabel('Quality Ratio')
        axes[1].set_ylabel('Frequency')
        
        plt.tight_layout()
        
        # Save visualization
        viz_path = os.path.join(self.run_dir, "label_distributions.png")
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Label distribution visualization saved to {viz_path}")
        
        return binary_stats, ratio_stats
    
    def prepare_data_splits(self, dataset: List[Tuple], test_size: float = 0.15, val_size: float = 0.15, 
                           random_state: int = 42) -> Tuple[List, List, List, List, List, List]:
        """Prepare train/validation/test splits for a dataset."""
        
        # Extract features and labels
        X = [(filename, tags, goodness) for filename, tags, goodness in dataset]
        y = [goodness for filename, tags, goodness in dataset]
        
        # First split: separate test set
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
        
        # Second split: separate train and validation
        val_size_adjusted = val_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, random_state=random_state
        )
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def train_lightgbm_model(self, X_train, y_train, X_val, y_val, model_name: str) -> LightGBMGoodnessPredictorModel:
        """Train LightGBM model."""
        logger.info(f"Training LightGBM model ({model_name})...")
        
        model = LightGBMGoodnessPredictorModel(max_features=5000, random_state=42)
        
        training_history = model.train_model(
            X_train, y_train, X_val, y_val,
            optimize_hyperparams=True,
            n_trials=30  # Reduced for faster training
        )
        
        # Save model
        model_path = os.path.join(self.run_dir, f"lightgbm_{model_name}_model.pkl")
        model.save_model(model_path)
        
        # Save training history
        history_path = os.path.join(self.run_dir, f"lightgbm_{model_name}_training_history.json")
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)
        
        logger.info(f"LightGBM ({model_name}) training complete")
        return model
    
    def train_resnet_model(self, X_train, y_train, X_val, y_val, model_name: str, epochs: int = 30) -> ResNetGoodnessPredictorModel:
        """Train ResNet model."""
        logger.info(f"Training ResNet model ({model_name})...")
        
        model = ResNetGoodnessPredictorModel(
            vocab_size=10000,
            embedding_dim=128,
            hidden_dim=256,
            num_blocks=3,
            max_length=100,
            dropout=0.1
        )
        
        training_history = model.train_model(
            X_train, y_train, X_val, y_val,
            batch_size=32,
            epochs=epochs,
            learning_rate=0.001,
            patience=10
        )
        
        # Save model
        model_path = os.path.join(self.run_dir, f"resnet_{model_name}_model.pkl")
        model.save_model(model_path)
        
        # Save training history
        history_path = os.path.join(self.run_dir, f"resnet_{model_name}_training_history.json")
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)
        
        logger.info(f"ResNet ({model_name}) training complete")
        return model
    
    def evaluate_model_enhanced(self, model, X_test, y_test, model_name: str) -> Dict[str, float]:
        """Enhanced evaluation for continuous targets."""
        logger.info(f"Evaluating {model_name}...")
        
        # Get base evaluation metrics
        base_metrics = model.evaluate(X_test, y_test)
        
        # Add enhanced metrics for continuous targets
        predictions = model.predict(X_test)
        
        # Continuous regression metrics
        mse = mean_squared_error(y_test, predictions)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, predictions)
        r2 = r2_score(y_test, predictions)
        
        # Prediction statistics
        pred_mean = np.mean(predictions)
        pred_std = np.std(predictions)
        pred_min = np.min(predictions)
        pred_max = np.max(predictions)
        
        # Target statistics
        target_mean = np.mean(y_test)
        target_std = np.std(y_test)
        target_min = np.min(y_test)
        target_max = np.max(y_test)
        
        # Correlation
        correlation = np.corrcoef(y_test, predictions)[0, 1] if len(set(y_test)) > 1 else 0.0
        
        enhanced_metrics = {
            **base_metrics,
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'correlation': correlation,
            'pred_mean': pred_mean,
            'pred_std': pred_std,
            'pred_range': pred_max - pred_min,
            'target_mean': target_mean,
            'target_std': target_std,
            'target_range': target_max - target_min,
            'pred_target_mean_diff': abs(pred_mean - target_mean),
            'pred_target_std_ratio': pred_std / target_std if target_std > 0 else 0.0
        }
        
        return enhanced_metrics
    
    def create_comparison_analysis(self, binary_results: Dict, ratio_results: Dict):
        """Create comprehensive comparison analysis."""
        logger.info("Creating comparison analysis...")
        
        # Prepare comparison data
        models = ['LightGBM', 'ResNet']
        approaches = ['Binary', 'Ratio-Based']
        
        # Key metrics for comparison
        key_metrics = ['rmse', 'mae', 'r2', 'correlation', 'ndcg']
        
        # Create comparison visualization
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Binary vs Ratio-Based Model Comparison', fontsize=16, fontweight='bold')
        
        for i, metric in enumerate(key_metrics):
            if i >= 6:  # Only plot first 6 metrics
                break
                
            row, col = i // 3, i % 3
            ax = axes[row, col]
            
            # Prepare data for this metric
            binary_values = [binary_results.get(model, {}).get(metric, 0) for model in models]
            ratio_values = [ratio_results.get(model, {}).get(metric, 0) for model in models]
            
            x = np.arange(len(models))
            width = 0.35
            
            bars1 = ax.bar(x - width/2, binary_values, width, label='Binary', color='blue', alpha=0.7)
            bars2 = ax.bar(x + width/2, ratio_values, width, label='Ratio-Based', color='green', alpha=0.7)
            
            ax.set_title(f'{metric.upper()}')
            ax.set_ylabel('Score')
            ax.set_xticks(x)
            ax.set_xticklabels(models)
            ax.legend()
            
            # Add value labels on bars
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.3f}', ha='center', va='bottom', fontsize=8)
        
        # Remove empty subplots
        for i in range(len(key_metrics), 6):
            row, col = i // 3, i % 3
            fig.delaxes(axes[row, col])
        
        plt.tight_layout()
        
        # Save comparison visualization
        comparison_path = os.path.join(self.run_dir, "binary_vs_ratio_comparison.png")
        plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Comparison visualization saved to {comparison_path}")
    
    def run_comparative_training(self) -> Dict[str, Any]:
        """Run comparative training with both binary and ratio-based labels."""
        logger.info("Starting comparative training pipeline...")
        
        # Load datasets
        if not self.load_datasets():
            raise ValueError("Failed to load datasets")
        
        # Analyze label distributions
        binary_stats, ratio_stats = self.analyze_label_distributions()
        
        # Prepare data splits for both datasets
        logger.info("Preparing data splits...")
        binary_splits = self.prepare_data_splits(self.binary_dataset)
        ratio_splits = self.prepare_data_splits(self.ratio_dataset)
        
        X_train_bin, X_val_bin, X_test_bin, y_train_bin, y_val_bin, y_test_bin = binary_splits
        X_train_rat, X_val_rat, X_test_rat, y_train_rat, y_val_rat, y_test_rat = ratio_splits
        
        logger.info(f"Binary splits - Train: {len(X_train_bin)}, Val: {len(X_val_bin)}, Test: {len(X_test_bin)}")
        logger.info(f"Ratio splits - Train: {len(X_train_rat)}, Val: {len(X_val_rat)}, Test: {len(X_test_rat)}")
        
        # Train models with both approaches
        models = {}
        
        try:
            # Binary LightGBM
            models['Binary_LightGBM'] = self.train_lightgbm_model(
                X_train_bin, y_train_bin, X_val_bin, y_val_bin, "binary"
            )
            
            # Ratio-based LightGBM
            models['Ratio_LightGBM'] = self.train_lightgbm_model(
                X_train_rat, y_train_rat, X_val_rat, y_val_rat, "ratio"
            )
            
            # Binary ResNet
            models['Binary_ResNet'] = self.train_resnet_model(
                X_train_bin, y_train_bin, X_val_bin, y_val_bin, "binary", epochs=20
            )
            
            # Ratio-based ResNet
            models['Ratio_ResNet'] = self.train_resnet_model(
                X_train_rat, y_train_rat, X_val_rat, y_val_rat, "ratio", epochs=20
            )
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
        
        # Evaluate models
        binary_results = {}
        ratio_results = {}
        
        if 'Binary_LightGBM' in models:
            binary_results['LightGBM'] = self.evaluate_model_enhanced(
                models['Binary_LightGBM'], X_test_bin, y_test_bin, "Binary LightGBM"
            )
        
        if 'Ratio_LightGBM' in models:
            ratio_results['LightGBM'] = self.evaluate_model_enhanced(
                models['Ratio_LightGBM'], X_test_rat, y_test_rat, "Ratio-Based LightGBM"
            )
        
        if 'Binary_ResNet' in models:
            binary_results['ResNet'] = self.evaluate_model_enhanced(
                models['Binary_ResNet'], X_test_bin, y_test_bin, "Binary ResNet"
            )
        
        if 'Ratio_ResNet' in models:
            ratio_results['ResNet'] = self.evaluate_model_enhanced(
                models['Ratio_ResNet'], X_test_rat, y_test_rat, "Ratio-Based ResNet"
            )
        
        # Create comparison analysis
        if binary_results and ratio_results:
            self.create_comparison_analysis(binary_results, ratio_results)
        
        # Save comprehensive results
        results = {
            'binary_stats': binary_stats,
            'ratio_stats': ratio_stats,
            'binary_results': binary_results,
            'ratio_results': ratio_results,
            'models': models,
            'run_directory': self.run_dir
        }
        
        # Save results summary
        summary_path = os.path.join(self.run_dir, "comparative_training_summary.json")
        with open(summary_path, 'w') as f:
            # Remove models from summary (not JSON serializable)
            summary_data = {k: v for k, v in results.items() if k != 'models'}
            json.dump(summary_data, f, indent=2, default=str)
        
        logger.info("Comparative training pipeline completed successfully!")
        return results


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description='Comparative Training: Binary vs Ratio-Based Labels')
    parser.add_argument('--binary-dataset', default='cleaned_dataset.pkl',
                       help='Path to binary-labeled dataset')
    parser.add_argument('--ratio-dataset', default='ratio_based_dataset.pkl',
                       help='Path to ratio-based dataset')
    parser.add_argument('--output-dir', default='ratio_based_training',
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    try:
        # Initialize trainer
        trainer = RatioBasedModelTrainer(args.binary_dataset, args.ratio_dataset, args.output_dir)
        
        # Run comparative training
        results = trainer.run_comparative_training()
        
        # Print summary
        print("\n" + "="*80)
        print("COMPARATIVE TRAINING COMPLETE")
        print("="*80)
        print(f"Results saved to: {results['run_directory']}")
        
        print("\nBinary vs Ratio-Based Performance Comparison:")
        for model_type in ['LightGBM', 'ResNet']:
            if model_type in results['binary_results'] and model_type in results['ratio_results']:
                binary_rmse = results['binary_results'][model_type].get('rmse', 0)
                ratio_rmse = results['ratio_results'][model_type].get('rmse', 0)
                improvement = ((binary_rmse - ratio_rmse) / binary_rmse * 100) if binary_rmse > 0 else 0
                
                print(f"\n{model_type}:")
                print(f"  Binary RMSE: {binary_rmse:.4f}")
                print(f"  Ratio RMSE: {ratio_rmse:.4f}")
                print(f"  Improvement: {improvement:+.1f}%")
        
        return 0
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
Test script for the data cleaning functionality.

This script provides utilities to test and validate the data cleaning script
without processing the full dataset.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from PIL import Image
import dill

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from utils import stderr_print
from data_cleaning_script import DataCleaner


def create_test_image_with_metadata(path: str, prompt: str = "test prompt", width: int = 512, height: int = 512):
    """Create a test image with embedded prompt metadata."""
    # Create a simple test image
    img = Image.new('RGB', (width, height), color='red')

    # Add metadata that the image_info function can extract
    # This simulates ComfyUI/Stable Diffusion metadata format
    if path.endswith('.png'):
        from PIL import PngImagePlugin
        pnginfo = PngImagePlugin.PngInfo()
        pnginfo.add_text("parameters", prompt)
        pnginfo.add_text("Software", "Test Generator")
        img.save(path, pnginfo=pnginfo)
    else:
        # For non-PNG files, save without metadata
        img.save(path)

    img.close()


def create_test_directory_structure(base_path: Path):
    """Create a test directory structure for validation."""
    # Create date directories
    date_dirs = ['2025-01-15', '2025-01-16', '2025-02-20']
    
    for date_dir in date_dirs:
        date_path = base_path / date_dir
        date_path.mkdir(parents=True, exist_ok=True)
        
        # Create some normal images in the date directory
        for i in range(3):
            img_path = date_path / f"normal_image_{i}.png"
            create_test_image_with_metadata(str(img_path), f"normal prompt {i} for {date_dir}")
        
        # Create sel directory with good images
        sel_path = date_path / "sel"
        sel_path.mkdir(exist_ok=True)
        
        for i in range(2):
            img_path = sel_path / f"normal_image_{i}.png"  # Same name as normal images
            create_test_image_with_metadata(str(img_path), f"good prompt {i} for {date_dir}")
        
        # Create nested sel directory
        nested_sel_path = sel_path / "sel1" / "tmp"
        nested_sel_path.mkdir(parents=True, exist_ok=True)
        
        img_path = nested_sel_path / "normal_image_2.png"  # Same name as normal image
        create_test_image_with_metadata(str(img_path), f"nested good prompt for {date_dir}")
        
        # Create some images without prompts (to test failure handling)
        no_prompt_path = date_path / "no_prompt.png"
        img = Image.new('RGB', (100, 100), color='blue')
        img.save(str(no_prompt_path))
        img.close()


def test_data_cleaner():
    """Test the DataCleaner with a temporary directory structure."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_input_dir = temp_path / "test_input"
        
        stderr_print("Creating test directory structure...")
        create_test_directory_structure(test_input_dir)
        
        # Create a custom DataCleaner with test directory
        cleaner = DataCleaner()
        cleaner.input_directories = [str(test_input_dir)]
        
        stderr_print("Running Phase 1: Collecting images...")
        images_with_quality = cleaner.collect_all_images_with_quality()
        
        stderr_print(f"Found {len(images_with_quality)} images")
        
        # Verify quality labeling logic
        good_count = sum(1 for _, quality in images_with_quality if quality == "good")
        normal_count = sum(1 for _, quality in images_with_quality if quality == "normal")
        
        stderr_print(f"Quality distribution: {good_count} good, {normal_count} normal")
        
        # Expected: 3 images per date dir (9 total normal), 3 should be marked as good (same filenames in sel)
        expected_good = 3 * 3  # 3 date dirs * 3 good images each (including nested)
        expected_normal = 3 * 4 - expected_good  # 4 images per date dir, minus the good ones
        
        stderr_print(f"Expected: {expected_good} good, {expected_normal} normal")
        
        stderr_print("Running Phase 2: Extracting prompts...")
        results = cleaner.process_images_with_prompts(images_with_quality)
        
        # Save test results
        output_path = temp_path / "test_results.pkl"
        cleaner.save_results(results, str(output_path))
        
        # Load and verify results
        with open(output_path, 'rb') as f:
            loaded_results = dill.load(f)
        
        stderr_print(f"Saved and loaded {len(loaded_results)} results")
        
        # Print sample results
        stderr_print("\nSample results:")
        for i, (filename, prompt, goodness) in enumerate(loaded_results[:5]):
            stderr_print(f"  {i+1}. {Path(filename).name} | {goodness} | {prompt[:50]}...")
        
        cleaner.print_statistics()
        
        return len(loaded_results) > 0


def validate_pickle_file(pickle_path: str):
    """Validate an existing pickle file."""
    try:
        with open(pickle_path, 'rb') as f:
            results = dill.load(f)
        
        stderr_print(f"Successfully loaded {len(results)} entries from {pickle_path}")
        
        if not results:
            stderr_print("Warning: Pickle file is empty!")
            return False
        
        # Validate structure
        first_entry = results[0]
        if not isinstance(first_entry, tuple) or len(first_entry) != 3:
            stderr_print("Error: Invalid data structure. Expected tuples of length 3.")
            return False
        
        filename, prompt, goodness = first_entry
        if not isinstance(filename, str) or not isinstance(prompt, str) or not isinstance(goodness, str):
            stderr_print("Error: Invalid data types. Expected all strings.")
            return False
        
        if goodness not in ['good', 'normal']:
            stderr_print(f"Warning: Unexpected goodness value: {goodness}")
        
        # Statistics
        good_count = sum(1 for _, _, g in results if g == 'good')
        normal_count = sum(1 for _, _, g in results if g == 'normal')
        empty_prompts = sum(1 for _, p, _ in results if not p.strip())
        
        stderr_print(f"Statistics:")
        stderr_print(f"  Total entries: {len(results)}")
        stderr_print(f"  Good quality: {good_count}")
        stderr_print(f"  Normal quality: {normal_count}")
        stderr_print(f"  Empty prompts: {empty_prompts}")
        stderr_print(f"  Success rate: {(len(results) - empty_prompts) / len(results) * 100:.1f}%")
        
        # Show sample entries
        stderr_print(f"\nFirst 3 entries:")
        for i, (filename, prompt, goodness) in enumerate(results[:3]):
            stderr_print(f"  {i+1}. File: {Path(filename).name}")
            stderr_print(f"     Quality: {goodness}")
            stderr_print(f"     Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
        
        return True
        
    except Exception as e:
        stderr_print(f"Error validating pickle file: {e}")
        return False


def main():
    """Main function for testing."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test data cleaning functionality")
    parser.add_argument('--test', action='store_true', help='Run test with temporary directory')
    parser.add_argument('--validate', type=str, help='Validate existing pickle file')
    
    args = parser.parse_args()
    
    if args.test:
        stderr_print("Running data cleaner test...")
        success = test_data_cleaner()
        if success:
            stderr_print("Test completed successfully!")
        else:
            stderr_print("Test failed!")
            sys.exit(1)
    
    elif args.validate:
        stderr_print(f"Validating pickle file: {args.validate}")
        success = validate_pickle_file(args.validate)
        if not success:
            sys.exit(1)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()

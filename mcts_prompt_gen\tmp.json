{"last_node_id": 52, "last_link_id": 245, "nodes": [{"id": 16, "type": "VAEEncode", "pos": {"0": -607.4528198242188, "1": -1090.7921142578125}, "size": {"0": 240, "1": 60}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 20}, {"name": "vae", "type": "VAE", "link": 81}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 30, "type": "Reroute", "pos": {"0": -280, "1": -560}, "size": [82, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 240}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [61, 62], "slot_index": 0}], "title": "Model", "properties": {"showOutputText": true, "horizontal": false}, "color": "#323", "bgcolor": "#535"}, {"id": 33, "type": "Reroute", "pos": {"0": -280, "1": -440}, "size": [75, 26], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 158}], "outputs": [{"name": "VAE", "type": "VAE", "links": [71, 81], "slot_index": 0}], "properties": {"showOutputText": true, "horizontal": false}, "color": "#322", "bgcolor": "#533"}, {"id": 34, "type": "Reroute", "pos": {"0": -290, "1": -1280}, "size": [90.4, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 151}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [83], "slot_index": 0}], "title": "Latent", "properties": {"showOutputText": true, "horizontal": false}, "color": "#323", "bgcolor": "#535"}, {"id": 35, "type": "Reroute", "pos": {"0": 115, "1": -1345}, "size": [90.4, 26], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 87}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [86], "slot_index": 0}], "title": "Latent", "properties": {"showOutputText": true, "horizontal": false}, "color": "#323", "bgcolor": "#535"}, {"id": 24, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": -143.25999450683594, "1": -857.949462890625}, "size": {"0": 210, "1": 234}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 62}, {"name": "positive", "type": "CONDITIONING", "link": 48}, {"name": "negative", "type": "CONDITIONING", "link": 49}, {"name": "latent_image", "type": "LATENT", "link": 171}, {"name": "seed", "type": "INT", "link": 46, "widget": {"name": "seed"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [319276359, "increment", 40, 7, "euler_ancestral", "normal", 0.5]}, {"id": 25, "type": "VAELoader", "pos": {"0": -980, "1": -160}, "size": {"0": 320, "1": 60}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["taesdxl"]}, {"id": 23, "type": "Random Number", "pos": {"0": -135.32000732421875, "1": -513.6900024414062}, "size": {"0": 210, "1": 194}, "flags": {"pinned": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "NUMBER", "type": "NUMBER", "links": null, "shape": 3}, {"name": "FLOAT", "type": "FLOAT", "links": null, "shape": 3}, {"name": "INT", "type": "INT", "links": [46, 79], "slot_index": 2, "shape": 3}], "title": "Random Seed", "properties": {"Node name for S&R": "Random Number"}, "widgets_values": ["integer", 1, 100000, 864796137997017, "randomize"]}, {"id": 15, "type": "LoadImage", "pos": {"0": -607.4528198242188, "1": -970.7920532226562}, "size": {"0": 380, "1": 340}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [20], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["ddinput.png", "image"]}, {"id": 39, "type": "PreviewImage", "pos": {"0": 340, "1": -560}, "size": {"0": 740, "1": 840}, "flags": {"pinned": true}, "order": 25, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 94}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 47, "type": "LatentUpscaleBy", "pos": {"0": -140, "1": -1020}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 172}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [171], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "LatentUpscaleBy"}, "widgets_values": ["nearest-exact", 1.5]}, {"id": 36, "type": "UpscaleModelLoader", "pos": {"0": 104, "1": -1237}, "size": {"0": 330, "1": 60}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [89], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["RealESRGAN_x4plus_anime_6B.pth"]}, {"id": 8, "type": "VAEDecode", "pos": {"0": 295, "1": -1325}, "size": {"0": 140, "1": 50}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 86}, {"name": "vae", "type": "VAE", "link": 71}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [94, 105, 210], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 51, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -649, "1": 207}, "size": {"0": 320, "1": 126}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null}, {"name": "clip", "type": "CLIP", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["sparkle.safetensors", 0.9, 1]}, {"id": 38, "type": "SaveImage", "pos": {"0": 110, "1": -998}, "size": {"0": 330, "1": 270}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 210}], "outputs": [], "properties": {}, "widgets_values": ["%date:yyyy-MM-dd%/ComfyUI"]}, {"id": 31, "type": "Reroute", "pos": {"0": -274, "1": -502}, "size": [75, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 241}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [64, 65], "slot_index": 0}], "properties": {"showOutputText": true, "horizontal": false}, "color": "#432", "bgcolor": "#653"}, {"id": 50, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -637, "1": -25}, "size": {"0": 320, "1": 126}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 215}, {"name": "clip", "type": "CLIP", "link": 216}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["ly4dim.safetensors", 0.8, 1]}, {"id": 20, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -988, "1": -348}, "size": {"0": 320, "1": 126}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 181}, {"name": "clip", "type": "CLIP", "link": 36}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["sdxl_lightning_8step_lora.safetensors", 1, 1]}, {"id": 37, "type": "ImageUpscaleWithModel", "pos": {"0": 112, "1": -1122}, "size": {"0": 330, "1": 50}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 89}, {"name": "image", "type": "IMAGE", "link": 105}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageUpscaleWithModel"}, "widgets_values": []}, {"id": 7, "type": "CLIPTextEncode", "pos": {"0": -135.32000732421875, "1": 46.309967041015625}, "size": {"0": 430, "1": 210}, "flags": {"pinned": true}, "order": 19, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 65}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [18, 49], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(low quality),(wors (low quality),(worst quality),lowres,bad anatomy,bad hands,strange hand,extra fingers,text,error,missing fingers,extra digit,fewer digits,cropped,worst quality,low quality,normal quality,jpeg artifacts,signature,watermark,username,blurry, by (embedding:bad-artist.safetensors:0.8), by (embedding:bad-artist-anime.safetensors:0.8),\n(embedding:negativeXL_D.safetensors:1),"]}, {"id": 42, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -644, "1": -355}, "size": {"0": 320, "1": 126}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 236}, {"name": "clip", "type": "CLIP", "link": 237}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [215], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [216], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["outline_xl_kohaku_delta_spv5x.safetensors", 0.4, 1]}, {"id": 52, "type": "PromptDanTagGen", "pos": {"0": 539, "1": -1173}, "size": {"0": 400, "1": 384}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "output", "type": "STRING", "links": null, "shape": 3}, {"name": "llm_output", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "PromptDanTagGen"}, "widgets_values": ["KBlueLeaf/DanTagGen-delta", "", "", "", "", "", "", "safe", "very_short", 1024, 1024, false, 1.35]}, {"id": 48, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -638, "1": -537}, "size": {"0": 320, "1": 126}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 244}, {"name": "clip", "type": "CLIP", "link": 245}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [240], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [241], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["Yanami XL kohaku zeta.safetensors", 0.7000000000000001, 1]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": {"0": -993, "1": -519}, "size": {"0": 320, "1": 100}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [181, 236, 242], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [36, 237, 243], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [158], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["kohakuXLZeta_rev1.safetensors"]}, {"id": 49, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -633, "1": -175}, "size": {"0": 320, "1": 126}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 242}, {"name": "clip", "type": "CLIP", "link": 243}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [244], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [245], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["shuicai.safetensors", 0.8, 1]}, {"id": 19, "type": "EmptyLatentImage", "pos": {"0": -607, "1": -1251}, "size": {"0": 240, "1": 106}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [151], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 2]}, {"id": 14, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": -143.25999450683594, "1": -1317.949462890625}, "size": {"0": 210, "1": 234}, "flags": {"collapsed": false}, "order": 20, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 61}, {"name": "positive", "type": "CONDITIONING", "link": 17}, {"name": "negative", "type": "CONDITIONING", "link": 18}, {"name": "latent_image", "type": "LATENT", "link": 83}, {"name": "seed", "type": "INT", "link": 79, "widget": {"name": "seed"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [87, 172], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [319276359, "increment", 30, 5.5, "dpmpp_2m", "karras", 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": {"0": -135.32000732421875, "1": -273.6900329589844}, "size": {"0": 430, "1": 270}, "flags": {"pinned": true}, "order": 18, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 64}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [17, 48], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["<PERSON><PERSON>, <PERSON><PERSON>, \n(watercolor:1.2), watercolor (medium),solo, (portrait:1.2), \n\n(imigimuru:0.8), (sy4:0.4), (hiten:0.1),\nblue hair,ahoge,medium hair,looking at viewer,surprised, smile, (blush:0.4),\n\n(masterpiece, best quality), absurdres, amazing quality, very aesthetic, highly detailed, high res, finely detailed, \n\n"]}], "links": [[17, 6, 0, 14, 1, "CONDITIONING"], [18, 7, 0, 14, 2, "CONDITIONING"], [20, 15, 0, 16, 0, "IMAGE"], [36, 4, 1, 20, 1, "CLIP"], [46, 23, 2, 24, 4, "INT"], [48, 6, 0, 24, 1, "CONDITIONING"], [49, 7, 0, 24, 2, "CONDITIONING"], [61, 30, 0, 14, 0, "MODEL"], [62, 30, 0, 24, 0, "MODEL"], [64, 31, 0, 6, 0, "CLIP"], [65, 31, 0, 7, 0, "CLIP"], [71, 33, 0, 8, 1, "VAE"], [79, 23, 2, 14, 4, "INT"], [81, 33, 0, 16, 1, "VAE"], [83, 34, 0, 14, 3, "LATENT"], [86, 35, 0, 8, 0, "LATENT"], [87, 14, 0, 35, 0, "*"], [89, 36, 0, 37, 0, "UPSCALE_MODEL"], [94, 8, 0, 39, 0, "IMAGE"], [105, 8, 0, 37, 1, "IMAGE"], [151, 19, 0, 34, 0, "*"], [158, 4, 2, 33, 0, "*"], [171, 47, 0, 24, 3, "LATENT"], [172, 14, 0, 47, 0, "LATENT"], [181, 4, 0, 20, 0, "MODEL"], [210, 8, 0, 38, 0, "IMAGE"], [215, 42, 0, 50, 0, "MODEL"], [216, 42, 1, 50, 1, "CLIP"], [236, 4, 0, 42, 0, "MODEL"], [237, 4, 1, 42, 1, "CLIP"], [240, 48, 0, 30, 0, "*"], [241, 48, 1, 31, 0, "*"], [242, 4, 0, 49, 0, "MODEL"], [243, 4, 1, 49, 1, "CLIP"], [244, 49, 0, 48, 0, "MODEL"], [245, 49, 1, 48, 1, "CLIP"]], "groups": [{"title": "Output", "bounding": [102, -1388, 361, 682], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "<PERSON><PERSON>", "bounding": [-164, -1389, 244, 770], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Input", "bounding": [-625, -1331, 426, 713], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Prompt", "bounding": [-164, -590, 481, 867], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Load Model", "bounding": [-1000, -599, 801, 519], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7513148009015777, "offset": {"0": 1045.50048828125, "1": 1049.8099365234375}}}, "version": 0.4}
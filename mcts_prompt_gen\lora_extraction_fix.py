#!/usr/bin/env python3
"""
Critical fix for LoRA extraction and tag parsing issues.

This script addresses two major problems:
1. Missing LoRA extraction from different ComfyUI workflow formats
2. LoRA filename corruption due to incorrect normalization

The fix ensures:
- Robust workflow parsing for multiple ComfyUI formats
- Preservation of original LoRA filenames without normalization
- Only regular tags get normalized, not LoRA filenames
"""

import os
import sys
import json
import re
from typing import List, Tuple, Dict, Any
from collections import defaultdict
from pathlib import Path

# Add src directory to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

from utils import (
    extract_comfyui_workflow_params,
    convert_api_workflow_to_standard,
    traverse_workflow_graph,
    extract_connected_lora_nodes
)


class FixedLoRAExtractor:
    """
    Fixed LoRA extractor that handles multiple ComfyUI workflow formats
    and preserves original LoRA filenames without corruption.
    """
    
    def __init__(self):
        self.extraction_stats = {
            'total_extractions': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'workflow_formats': defaultdict(int),
            'loras_extracted': 0
        }
    
    def extract_loras_from_workflow(self, workflow_json_str: str) -> List[str]:
        """
        Extract LoRAs from ComfyUI workflow with support for multiple formats.
        
        Args:
            workflow_json_str: JSON string containing ComfyUI workflow
            
        Returns:
            List of LoRA strings in format "filename.safetensors:weight"
        """
        self.extraction_stats['total_extractions'] += 1
        
        try:
            # Parse workflow JSON
            workflow = json.loads(workflow_json_str)
            
            # Detect and handle different workflow formats
            if self._is_api_format(workflow):
                self.extraction_stats['workflow_formats']['API'] += 1
                return self._extract_from_api_format(workflow)
            elif self._is_standard_format(workflow):
                self.extraction_stats['workflow_formats']['Standard'] += 1
                return self._extract_from_standard_format(workflow)
            else:
                self.extraction_stats['workflow_formats']['Unknown'] += 1
                # Try both methods as fallback
                try:
                    return self._extract_from_api_format(workflow)
                except:
                    return self._extract_from_standard_format(workflow)
                    
        except Exception as e:
            self.extraction_stats['failed_extractions'] += 1
            print(f"Failed to extract LoRAs: {e}")
            return []
    
    def _is_api_format(self, workflow: dict) -> bool:
        """Check if workflow is in ComfyUI API format."""
        return (isinstance(workflow, dict) and 
                'nodes' in workflow and 
                isinstance(workflow['nodes'], list))
    
    def _is_standard_format(self, workflow: dict) -> bool:
        """Check if workflow is in ComfyUI standard format."""
        return (isinstance(workflow, dict) and 
                any(key.isdigit() for key in workflow.keys()))
    
    def _extract_from_api_format(self, workflow: dict) -> List[str]:
        """Extract LoRAs from API format workflow."""
        # Convert to standard format first
        standard_workflow = convert_api_workflow_to_standard(workflow)
        return self._extract_from_standard_format(standard_workflow)
    
    def _extract_from_standard_format(self, workflow: dict) -> List[str]:
        """Extract LoRAs from standard format workflow using graph traversal."""
        try:
            # Find connected nodes
            connected_nodes = traverse_workflow_graph(workflow)
            
            # Extract only connected LoRAs
            loras = extract_connected_lora_nodes(workflow, connected_nodes)
            
            self.extraction_stats['successful_extractions'] += 1
            self.extraction_stats['loras_extracted'] += len(loras)
            
            return loras
            
        except Exception as e:
            self.extraction_stats['failed_extractions'] += 1
            raise e


class FixedTagParser:
    """
    Fixed tag parser that preserves LoRA filenames without normalization
    while properly normalizing regular tags.
    """
    
    def __init__(self):
        self.parsing_stats = {
            'total_parses': 0,
            'successful_parses': 0,
            'failed_parses': 0,
            'lora_tags_preserved': 0,
            'regular_tags_normalized': 0
        }
    
    def parse_prompt_with_preserved_loras(self, prompt_string: str) -> List[Tuple[str, float]]:
        """
        Parse prompt while preserving LoRA filenames exactly as they appear.
        
        Args:
            prompt_string: Enhanced prompt string
            
        Returns:
            List of (tag, weight) tuples with preserved LoRA filenames
        """
        self.parsing_stats['total_parses'] += 1
        
        try:
            # Step 1: Extract and preserve LoRA parameters first
            lora_tags, cleaned_prompt = self._extract_and_preserve_loras(prompt_string)
            
            # Step 2: Parse remaining tags with normalization
            regular_tags = self._parse_regular_tags(cleaned_prompt)
            
            # Step 3: Combine results
            all_tags = lora_tags + regular_tags
            
            self.parsing_stats['successful_parses'] += 1
            return all_tags
            
        except Exception as e:
            self.parsing_stats['failed_parses'] += 1
            print(f"Failed to parse prompt: {e}")
            return [('parsing_error', 1.0)]
    
    def _extract_and_preserve_loras(self, prompt: str) -> Tuple[List[Tuple[str, float]], str]:
        """
        Extract LoRA parameters while preserving original filenames.
        
        Returns:
            Tuple of (lora_tags, cleaned_prompt)
        """
        lora_tags = []
        cleaned_prompt = prompt
        
        # Pattern to match --lora filename.safetensors:weight
        lora_pattern = re.compile(r'--lora\s+([^:\s]+\.safetensors):([0-9.-]+)')
        
        for match in lora_pattern.finditer(prompt):
            filename = match.group(1)  # Preserve exact filename
            weight_str = match.group(2)
            
            try:
                weight = float(weight_str)
            except ValueError:
                weight = 1.0
            
            # CRITICAL: Preserve original filename without any normalization
            lora_tag = f"lora_{filename.replace('.safetensors', '')}"
            lora_tags.append((lora_tag, weight))
            self.parsing_stats['lora_tags_preserved'] += 1
        
        # Remove LoRA parameters from prompt for regular tag processing
        cleaned_prompt = lora_pattern.sub('', cleaned_prompt)
        
        return lora_tags, cleaned_prompt
    
    def _parse_regular_tags(self, prompt: str) -> List[Tuple[str, float]]:
        """Parse regular tags with proper normalization."""
        tags = []
        
        # Split by common delimiters
        parts = re.split(r'[,\n;]+', prompt)
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # Handle various weight syntaxes
            tag, weight = self._parse_tag_with_weight(part)
            
            if tag:
                # Apply normalization only to regular tags
                normalized_tag = self._normalize_regular_tag(tag)
                tags.append((normalized_tag, weight))
                self.parsing_stats['regular_tags_normalized'] += 1
        
        return tags
    
    def _parse_tag_with_weight(self, part: str) -> Tuple[str, float]:
        """Parse a single tag part to extract tag and weight."""
        # Handle {tag} = 1.1
        if part.startswith('{') and part.endswith('}'):
            return part[1:-1].strip(), 1.1
        
        # Handle [tag] = 0.9
        if part.startswith('[') and part.endswith(']'):
            return part[1:-1].strip(), 0.9
        
        # Handle (tag:weight)
        weight_match = re.match(r'\(([^:)]+):([0-9.]+)\)', part)
        if weight_match:
            tag = weight_match.group(1).strip()
            try:
                weight = float(weight_match.group(2))
                return tag, weight
            except ValueError:
                return tag, 1.0
        
        # Regular tag
        return part.strip(), 1.0
    
    def _normalize_regular_tag(self, tag: str) -> str:
        """
        Normalize regular tags (NOT LoRA filenames).
        
        Args:
            tag: Regular tag to normalize
            
        Returns:
            Normalized tag
        """
        if not isinstance(tag, str):
            return str(tag)
        
        # Clean the tag
        tag = tag.strip()
        if not tag:
            return ""
        
        # Replace underscores with spaces temporarily
        tag = tag.replace('_', ' ')
        
        # Normalize whitespace
        tag = re.sub(r'\s+', ' ', tag).strip()
        
        # Convert back to underscores
        tag = tag.replace(' ', '_')
        
        # Clean problematic characters
        tag = re.sub(r'[^\w\-:.]', '_', tag)
        
        # Clean up multiple underscores
        tag = re.sub(r'_+', '_', tag).strip('_')
        
        return tag.lower()
    
    def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get parsing statistics."""
        return self.parsing_stats.copy()


def test_fixed_lora_extraction():
    """Test the fixed LoRA extraction with the failing test cases."""
    print("TESTING FIXED LORA EXTRACTION")
    print("=" * 60)

    extractor = FixedLoRAExtractor()

    # Test cases from the original problem description
    test_cases = [
        {
            "path": r"F:\SD-webui\ComfyUI\output\2024-12-17\ComfyUI_00099_.png",
            "expected": ["luce2_Noob75XL.safetensors:0.6"]
        },
        {
            "path": r"F:\SD-webui\ComfyUI\output\2025-04-22\ComfyUI_00212_.png",
            "expected": ["prts_sn59.safetensors:0.8"]
        },
        {
            "path": r"F:\SD-webui\gallery\server\2024-12-25\ComfyUI_00129_.png",
            "expected": [
                "a31_style_koni-000010.safetensors:0.8",
                "outline_xl_kohaku_delta_spv5x.safetensors:-1.0",
                "Hoseki_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9"
            ]
        }
    ]

    for i, case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {case['path']}")
        print(f"Expected: {case['expected']}")

        if os.path.exists(case['path']):
            try:
                from PIL import Image
                from utils import image_info

                with Image.open(case['path']) as img:
                    info = image_info(img, case['path'], enhanced_prompts=True)

                    # Extract workflow
                    if hasattr(img, 'info') and 'workflow' in img.info:
                        workflow_json = img.info['workflow']

                        # Test extraction
                        extracted = extractor.extract_loras_from_workflow(workflow_json)
                        print(f"Extracted: {extracted}")

                        # Compare
                        if set(extracted) == set(case['expected']):
                            print("✅ SUCCESS: Extraction matches expected results")
                        else:
                            print("❌ FAILURE: Extraction does not match")
                            missing = set(case['expected']) - set(extracted)
                            extra = set(extracted) - set(case['expected'])
                            if missing:
                                print(f"   Missing: {missing}")
                            if extra:
                                print(f"   Extra: {extra}")
                    else:
                        print("❌ No workflow found in image")

            except Exception as e:
                print(f"❌ Error processing image: {e}")
        else:
            print("❌ Image file not found")

    print(f"\nExtraction Statistics:")
    stats = extractor.extraction_stats
    for key, value in stats.items():
        print(f"  {key}: {value}")


def test_fixed_tag_parsing():
    """Test the fixed tag parsing with LoRA filename preservation."""
    print("\n\nTESTING FIXED TAG PARSING")
    print("=" * 60)

    parser = FixedTagParser()

    test_cases = [
        # Test LoRA filename preservation
        "1girl, masterpiece --lora character_model.safetensors:0.8 --lora style-v2.safetensors:1.2",

        # Test complex LoRA names
        "beautiful portrait --lora a31_style_koni-000010.safetensors:0.8 --lora Hoseki_ZenlessZoneZero_HoshimiMiyabi_IllustriousXL_v1.safetensors:0.9",

        # Test regular tag normalization
        "floral_print, floral print, very_aesthetic",

        # Test mixed content
        "{masterpiece}, [low quality], (detailed:1.2) --lora test_model.safetensors:0.7"
    ]

    for i, test_prompt in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_prompt}")
        result = parser.parse_prompt_with_preserved_loras(test_prompt)

        print("Results:")
        for tag, weight in result:
            if tag.startswith('lora_'):
                print(f"  LoRA: {tag} -> {weight} (filename preserved)")
            else:
                print(f"  Tag: {tag} -> {weight}")

    print(f"\nParsing Statistics:")
    stats = parser.get_parsing_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    test_fixed_lora_extraction()
    test_fixed_tag_parsing()

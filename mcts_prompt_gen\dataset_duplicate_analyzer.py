#!/usr/bin/env python3
"""
Dataset Duplicate Analysis Script

This script analyzes the current dataset to identify duplicate images
caused by the folder structure where images are copied to subfolders
to indicate goodness scores.

Author: AI Assistant
Date: 2025-06-24
"""

import os
import sys
import dill
import logging
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import List, Tuple, Dict, Set, Any
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'duplicate_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DatasetDuplicateAnalyzer:
    """Analyzes dataset for duplicates and irrelevant prompts."""
    
    def __init__(self, dataset_path: str = "production_dataset.pkl"):
        """Initialize the analyzer."""
        self.dataset_path = dataset_path
        self.dataset = None
        self.analysis_results = {}
        
    def load_dataset(self) -> bool:
        """Load the dataset from pickle file."""
        try:
            logger.info(f"Loading dataset from {self.dataset_path}...")
            with open(self.dataset_path, 'rb') as f:
                self.dataset = dill.load(f)
            logger.info(f"Successfully loaded {len(self.dataset)} entries")
            return True
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            return False
    
    def analyze_file_paths(self) -> Dict[str, Any]:
        """Analyze file paths to identify duplicate patterns."""
        logger.info("Analyzing file paths for duplicate patterns...")
        
        path_analysis = {
            'total_entries': len(self.dataset),
            'unique_filenames': set(),
            'duplicate_groups': defaultdict(list),
            'base_level_images': [],
            'subfolder_images': [],
            'date_folder_patterns': Counter(),
            'folder_structure_analysis': defaultdict(list)
        }
        
        for i, (filename, tags, goodness_score) in enumerate(self.dataset):
            # Extract filename without path
            base_filename = os.path.basename(filename)
            path_analysis['unique_filenames'].add(base_filename)
            
            # Group by base filename to find duplicates
            path_analysis['duplicate_groups'][base_filename].append({
                'index': i,
                'full_path': filename,
                'goodness_score': goodness_score,
                'tags_count': len(tags)
            })
            
            # Analyze path structure
            path_parts = Path(filename).parts
            
            # Look for date folder patterns (YYYY-MM-DD or YYYY-MM-DD-XX)
            date_pattern = None
            for part in path_parts:
                if re.match(r'\d{4}-\d{2}-\d{2}(-\d{2})?$', part):
                    date_pattern = part
                    break
            
            if date_pattern:
                path_analysis['date_folder_patterns'][date_pattern] += 1
                
                # Find position of date folder
                if date_pattern in path_parts:
                    date_index = path_parts.index(date_pattern)
                    remaining_parts = path_parts[date_index+1:]
                    
                    # Check if it's a base level image (date_folder/ComfyUI_XXXXX_.png)
                    if len(remaining_parts) == 1 and remaining_parts[0].startswith('ComfyUI_'):
                        path_analysis['base_level_images'].append({
                            'index': i,
                            'filename': base_filename,
                            'full_path': filename,
                            'goodness_score': goodness_score
                        })
                    else:
                        # It's in a subfolder
                        path_analysis['subfolder_images'].append({
                            'index': i,
                            'filename': base_filename,
                            'full_path': filename,
                            'goodness_score': goodness_score,
                            'subfolder_path': '/'.join(remaining_parts[:-1])
                        })
                        
                        # Track folder structure
                        folder_structure = '/'.join(remaining_parts[:-1])
                        path_analysis['folder_structure_analysis'][folder_structure].append(base_filename)
        
        # Identify actual duplicates (same filename in multiple locations)
        actual_duplicates = {}
        for filename, entries in path_analysis['duplicate_groups'].items():
            if len(entries) > 1:
                actual_duplicates[filename] = entries
        
        path_analysis['actual_duplicates'] = actual_duplicates
        path_analysis['duplicate_count'] = len(actual_duplicates)
        path_analysis['base_level_count'] = len(path_analysis['base_level_images'])
        path_analysis['subfolder_count'] = len(path_analysis['subfolder_images'])
        
        self.analysis_results['path_analysis'] = path_analysis
        return path_analysis
    
    def analyze_tag_relevance(self) -> Dict[str, Any]:
        """Analyze tags to identify irrelevant prompts."""
        logger.info("Analyzing tag relevance (1girl/2girls filter)...")
        
        tag_analysis = {
            'total_entries': len(self.dataset),
            'relevant_entries': [],
            'irrelevant_entries': [],
            'tag_statistics': Counter()
        }
        
        for i, (filename, tags, goodness_score) in enumerate(self.dataset):
            # Extract tag names (ignore weights)
            tag_names = [tag.lower() for tag, weight in tags]
            
            # Count all tags for statistics
            for tag_name in tag_names:
                tag_analysis['tag_statistics'][tag_name] += 1
            
            # Check for relevance (contains "1girl" or "2girls")
            is_relevant = any(tag in tag_names for tag in ['1girl', '2girls'])
            
            entry_info = {
                'index': i,
                'filename': os.path.basename(filename),
                'full_path': filename,
                'goodness_score': goodness_score,
                'tags_count': len(tags),
                'has_1girl': '1girl' in tag_names,
                'has_2girls': '2girls' in tag_names
            }
            
            if is_relevant:
                tag_analysis['relevant_entries'].append(entry_info)
            else:
                tag_analysis['irrelevant_entries'].append(entry_info)
        
        tag_analysis['relevant_count'] = len(tag_analysis['relevant_entries'])
        tag_analysis['irrelevant_count'] = len(tag_analysis['irrelevant_entries'])
        tag_analysis['relevance_percentage'] = (tag_analysis['relevant_count'] / tag_analysis['total_entries']) * 100
        
        self.analysis_results['tag_analysis'] = tag_analysis
        return tag_analysis
    
    def print_analysis_summary(self):
        """Print a comprehensive analysis summary."""
        logger.info("\n" + "="*80)
        logger.info("DATASET DUPLICATE ANALYSIS SUMMARY")
        logger.info("="*80)
        
        # Path analysis summary
        if 'path_analysis' in self.analysis_results:
            path_analysis = self.analysis_results['path_analysis']
            logger.info(f"\nPATH ANALYSIS:")
            logger.info(f"  Total entries: {path_analysis['total_entries']:,}")
            logger.info(f"  Unique filenames: {len(path_analysis['unique_filenames']):,}")
            logger.info(f"  Duplicate groups: {path_analysis['duplicate_count']:,}")
            logger.info(f"  Base-level images: {path_analysis['base_level_count']:,}")
            logger.info(f"  Subfolder images: {path_analysis['subfolder_count']:,}")
            
            logger.info(f"\nTOP DATE FOLDERS:")
            for date_folder, count in path_analysis['date_folder_patterns'].most_common(10):
                logger.info(f"  {date_folder}: {count:,} images")
            
            logger.info(f"\nTOP SUBFOLDER STRUCTURES:")
            for folder_structure, filenames in list(path_analysis['folder_structure_analysis'].items())[:10]:
                logger.info(f"  {folder_structure}: {len(filenames):,} images")
        
        # Tag analysis summary
        if 'tag_analysis' in self.analysis_results:
            tag_analysis = self.analysis_results['tag_analysis']
            logger.info(f"\nTAG RELEVANCE ANALYSIS:")
            logger.info(f"  Total entries: {tag_analysis['total_entries']:,}")
            logger.info(f"  Relevant entries (1girl/2girls): {tag_analysis['relevant_count']:,} ({tag_analysis['relevance_percentage']:.1f}%)")
            logger.info(f"  Irrelevant entries: {tag_analysis['irrelevant_count']:,}")
            
            logger.info(f"\nTOP TAGS:")
            for tag, count in tag_analysis['tag_statistics'].most_common(20):
                logger.info(f"  {tag}: {count:,}")
    
    def run_full_analysis(self) -> bool:
        """Run the complete analysis."""
        logger.info("Starting comprehensive dataset duplicate analysis...")
        
        if not self.load_dataset():
            return False
        
        # Run all analyses
        self.analyze_file_paths()
        self.analyze_tag_relevance()
        
        # Print summary
        self.print_analysis_summary()
        
        logger.info("\nAnalysis completed successfully!")
        return True


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Analyze dataset for duplicates and irrelevant prompts")
    parser.add_argument("--dataset", default="production_dataset.pkl", help="Path to dataset pickle file")
    
    args = parser.parse_args()
    
    analyzer = DatasetDuplicateAnalyzer(args.dataset)
    success = analyzer.run_full_analysis()
    
    if success:
        logger.info("Analysis completed successfully!")
        return 0
    else:
        logger.error("Analysis failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())

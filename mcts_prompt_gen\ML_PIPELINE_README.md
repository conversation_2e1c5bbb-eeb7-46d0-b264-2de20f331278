# Machine Learning Pipeline for Prompt Quality Prediction

This directory contains a comprehensive machine learning pipeline for training models to predict prompt quality based on user selection patterns from image generation datasets.

## Overview

The pipeline implements a ranking-focused approach to prompt quality prediction, treating the problem as learning to rank "good" prompts higher than "normal" prompts rather than simple binary classification.

## Dataset Statistics

**Current Dataset (promptlabels.pkl):**
- **Total Samples:** 66,312 prompts
- **Good Quality:** 4,375 (6.6%) - prompts from user-selected images
- **Normal Quality:** 61,937 (93.4%) - prompts from regular images
- **Valid Prompts:** 66,258 (99.9%) - prompts with extractable text
- **Average Prompt Length:** 482 characters
- **File Types:** 97.6% PNG, 2.4% WebP, <0.1% JPG

## Pipeline Architecture

### Phase 1: Data Validation and Preparation
**Script:** `data_validation.py`

- Validates data structure integrity
- Comprehensive quality analysis with visualizations
- Stratified train/test split (80/20) maintaining class balance
- Generates analysis dashboard and statistics

**Key Outputs:**
- `train_data.pkl` - Training dataset (53,006 samples)
- `test_data.pkl` - Test dataset (13,252 samples)
- Data quality visualizations and statistics

### Phase 2: LightGBM Model Training
**Script:** `lightgbm_trainer.py`

- **Feature Engineering:** TF-IDF (1-2 grams) + text statistics
- **Hyperparameter Optimization:** Optuna with 50+ trials
- **Cross-Validation:** Stratified K-fold for robust evaluation
- **Objective:** Binary classification with probability scores for ranking

**Key Outputs:**
- `lightgbm_model.pkl` - Trained model
- `feature_extractor.pkl` - Feature extraction pipeline
- Training curves and hyperparameter optimization results

### Phase 3: Comprehensive Evaluation
**Script:** `model_evaluator.py`

- **Ranking Metrics:** ROC-AUC, Average Precision, NDCG, Precision@K
- **Statistical Tests:** Mann-Whitney U test for ranking significance
- **Visualizations:** ROC curves, calibration plots, feature importance
- **Analysis:** Score distributions, confusion matrices

**Key Outputs:**
- Comprehensive evaluation metrics
- Detailed predictions with confidence scores
- Feature importance analysis

## Quick Start

### Prerequisites
```bash
pip install -r requirements.txt
```

### Complete Pipeline (Recommended)
```bash
# Run the entire pipeline from data validation through evaluation
python ml_pipeline.py --data promptlabels.pkl
```

### Individual Phases
```bash
# Phase 1: Data validation and splitting
python data_validation.py --data promptlabels.pkl

# Phase 2: Model training (requires Phase 1 outputs)
python lightgbm_trainer.py --train-data results/validation_*/train_data.pkl --test-data results/validation_*/test_data.pkl

# Phase 3: Model evaluation (requires Phase 2 outputs)
python model_evaluator.py --model-dir results/lightgbm_training_* --test-data results/validation_*/test_data.pkl
```

## Command-Line Options

### Master Pipeline (`ml_pipeline.py`)
```bash
python ml_pipeline.py [OPTIONS]

Options:
  --data PATH              Path to promptlabels.pkl (default: promptlabels.pkl)
  --results-dir PATH       Results directory (default: results)
  --test-size FLOAT        Test set proportion (default: 0.2)
  --random-seed INT        Random seed (default: 42)
  --no-optimization        Skip hyperparameter optimization
  --n-trials INT           Optimization trials (default: 50)
```

### Data Validation (`data_validation.py`)
```bash
python data_validation.py [OPTIONS]

Options:
  --data PATH              Path to promptlabels.pkl
  --results-dir PATH       Results directory
  --test-size FLOAT        Test set proportion
  --random-seed INT        Random seed
```

### Training (`lightgbm_trainer.py`)
```bash
python lightgbm_trainer.py [OPTIONS]

Required:
  --train-data PATH        Training data pickle file
  --test-data PATH         Test data pickle file

Options:
  --results-dir PATH       Results directory
  --no-optimization        Skip hyperparameter optimization
  --n-trials INT           Optimization trials
```

### Evaluation (`model_evaluator.py`)
```bash
python model_evaluator.py [OPTIONS]

Required:
  --model-dir PATH         Directory with trained model
  --test-data PATH         Test data pickle file

Options:
  --results-dir PATH       Results directory
```

## Output Structure

```
results/
├── ml_pipeline_YYYYMMDD_HHMMSS/          # Master pipeline results
│   ├── pipeline_summary.json             # Complete pipeline summary
│   └── PIPELINE_SUMMARY.md               # Human-readable summary
├── validation_YYYYMMDD_HHMMSS/           # Data validation results
│   ├── train_data.pkl                    # Training dataset
│   ├── test_data.pkl                     # Test dataset
│   ├── data_analysis_dashboard.png       # Visualization dashboard
│   └── data_statistics.txt               # Dataset statistics
├── lightgbm_training_YYYYMMDD_HHMMSS/    # Training results
│   ├── lightgbm_model.pkl                # Trained model (pickle)
│   ├── lightgbm_model.txt                # Trained model (native)
│   ├── feature_extractor.pkl             # Feature extraction pipeline
│   ├── model_parameters.json             # Hyperparameters
│   ├── hyperparameter_optimization.pkl   # Optimization results
│   └── training_curves.png               # Training visualizations
└── evaluation_YYYYMMDD_HHMMSS/           # Evaluation results
    ├── evaluation_metrics.json           # All metrics
    ├── detailed_predictions.csv          # Per-sample predictions
    ├── comprehensive_evaluation.png      # Evaluation dashboard
    └── feature_importance.csv            # Feature analysis
```

## Expected Performance

Based on the dataset characteristics and ranking-focused approach:

- **ROC-AUC:** 0.75-0.85 (good ranking discrimination)
- **Average Precision:** 0.15-0.30 (given 6.6% positive class)
- **NDCG:** 0.60-0.80 (ranking quality measure)
- **Mean Score Difference:** >0.05 (good prompts score higher)
- **Precision@10:** 0.20-0.40 (top 10 predictions contain good prompts)

## Key Features

### Ranking-Focused Design
- Uses continuous probability scores rather than hard classification
- Optimizes for ranking quality metrics (NDCG, Precision@K)
- Statistical significance testing for ranking performance

### Robust Feature Engineering
- TF-IDF vectorization with 1-2 gram features
- Text statistics (length, punctuation, formatting)
- Handles empty prompts and edge cases gracefully

### Comprehensive Evaluation
- Multiple ranking and classification metrics
- Calibration analysis for probability reliability
- Feature importance for model interpretability
- Detailed error analysis and predictions export

### Production Ready
- Reproducible with fixed random seeds
- Comprehensive logging and error handling
- Modular design for easy integration
- Timestamped results for version control

## Integration with MCTS

The trained model provides a quality scoring function that can be integrated with the existing MCTS framework:

```python
# Load trained model and feature extractor
with open('results/lightgbm_training_*/lightgbm_model.pkl', 'rb') as f:
    model = dill.load(f)
with open('results/lightgbm_training_*/feature_extractor.pkl', 'rb') as f:
    feature_extractor = dill.load(f)

# Score a prompt
def score_prompt(prompt_text):
    features = feature_extractor.transform([prompt_text])
    score = model.predict(features, num_iteration=model.best_iteration)[0]
    return score  # Higher scores indicate better quality
```

## Troubleshooting

### Common Issues

1. **Memory Errors:** For very large datasets, consider reducing `max_features` in feature extraction
2. **Long Training Times:** Use `--no-optimization` to skip hyperparameter tuning for faster results
3. **Poor Performance:** Check class balance and consider adjusting the decision threshold
4. **Missing Dependencies:** Install all requirements with `pip install -r requirements.txt`

### Performance Tuning

- **More Data:** The model benefits from larger datasets with more good examples
- **Feature Engineering:** Consider domain-specific features (art styles, technical terms)
- **Hyperparameters:** Increase `n_trials` for more thorough optimization
- **Ensemble Methods:** Combine multiple models for better performance

## Next Steps

1. **MCTS Integration:** Use the trained model as a reward function in MCTS
2. **Online Learning:** Implement feedback loops to continuously improve the model
3. **Multi-Class Extension:** Extend to multiple quality levels beyond binary
4. **Domain Adaptation:** Fine-tune for specific image generation styles or domains

## Contributing

To extend the pipeline:

1. **New Features:** Add feature extraction methods in `PromptFeatureExtractor`
2. **New Models:** Implement additional model types in the training pipeline
3. **New Metrics:** Add evaluation metrics in `ModelEvaluator`
4. **New Visualizations:** Extend plotting functions for additional insights

## License

This pipeline is part of the MCTS Prompt Generation project. See project documentation for license information.

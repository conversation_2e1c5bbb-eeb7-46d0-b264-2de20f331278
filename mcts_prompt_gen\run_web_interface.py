#!/usr/bin/env python3
"""
Simple launcher for the web interface.
"""

import os
import sys

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    print("Starting web interface...")
    print(f"Current directory: {current_dir}")
    print(f"Python path: {sys.path[:3]}")
    
    # Import and run the web interface
    from web_interface_backend import app, initialize_predictor
    
    print("Initializing predictor...")
    initialize_predictor()
    
    print("Starting Flask app on http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    
except Exception as e:
    print(f"Error starting web interface: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

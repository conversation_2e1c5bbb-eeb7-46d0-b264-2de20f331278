#!/usr/bin/env python3
"""
Test script for the new graph traversal-based LoRA extraction logic.

This script tests that the new implementation correctly identifies only connected
LoRA nodes in the execution path, ignoring disconnected nodes.
"""

import json
import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import extract_comfyui_workflow_params, traverse_workflow_graph, extract_connected_lora_nodes


def create_test_workflow_with_disconnected_loras():
    """
    Create a test workflow that includes both connected and disconnected LoRA nodes.
    This simulates a real scenario where users have multiple LoRA nodes but only some are connected.
    """
    return {
        # Connected execution path: SaveImage -> VAEDecode -> KSampler -> LoraTagLoader -> CheckpointLoader
        "9": {  # SaveImage (output node)
            "inputs": {
                "filename_prefix": "test_output",
                "images": ["28", 0]  # Connected to VAEDecode
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Image"}
        },
        "28": {  # VAEDecode (connected)
            "inputs": {
                "samples": ["12", 0],  # Connected to KSampler
                "vae": ["4", 2]        # Connected to CheckpointLoader
            },
            "class_type": "VAEDecodeTiled",
            "_meta": {"title": "VAE Decode (Tiled)"}
        },
        "12": {  # KSampler (connected)
            "inputs": {
                "seed": 123456,
                "steps": 30,
                "cfg": 7.5,
                "sampler_name": "euler_ancestral",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["41", 0],      # Connected to LoraTagLoader
                "positive": ["6", 0],    # Connected to CLIPTextEncode
                "negative": ["7", 0],    # Connected to CLIPTextEncode
                "latent_image": ["30", 0] # Connected to EmptyLatentImage
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "41": {  # LoraTagLoader (CONNECTED - should be extracted)
            "inputs": {
                "text": "<lora:connected_lora.safetensors:1.0>, <lora:another_connected.safetensors:0.8>",
                "model": ["4", 0],  # Connected to CheckpointLoader
                "clip": ["4", 1]    # Connected to CheckpointLoader
            },
            "class_type": "LoraTagLoader",
            "_meta": {"title": "Load LoRA Tag (Connected)"}
        },
        "4": {  # CheckpointLoader (connected)
            "inputs": {
                "ckpt_name": "test_model.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "6": {  # CLIPTextEncode positive (connected)
            "inputs": {
                "text": "1girl, masterpiece, best quality",
                "clip": ["41", 1]  # Connected to LoraTagLoader
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Prompt)"}
        },
        "7": {  # CLIPTextEncode negative (connected)
            "inputs": {
                "text": "low quality, blurry",
                "clip": ["41", 1]  # Connected to LoraTagLoader
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Negative)"}
        },
        "30": {  # EmptyLatentImage (connected)
            "inputs": {
                "width": 1024,
                "height": 1024,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Empty Latent Image"}
        },
        
        # DISCONNECTED NODES (should NOT be extracted)
        "99": {  # Disconnected LoraTagLoader (should be ignored)
            "inputs": {
                "text": "<lora:disconnected_lora.safetensors:1.2>, <lora:unused_lora.safetensors:0.5>",
                "model": ["100", 0],  # Connected to disconnected checkpoint
                "clip": ["100", 1]
            },
            "class_type": "LoraTagLoader",
            "_meta": {"title": "Load LoRA Tag (Disconnected)"}
        },
        "100": {  # Disconnected CheckpointLoader
            "inputs": {
                "ckpt_name": "unused_model.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint (Disconnected)"}
        },
        "101": {  # Another disconnected LoRA node
            "inputs": {
                "text": "<lora:orphaned_lora.safetensors:0.9>",
                "model": ["100", 0],
                "clip": ["100", 1]
            },
            "class_type": "LoraLoader",
            "_meta": {"title": "LoRA Loader (Disconnected)"}
        }
    }


def test_graph_traversal():
    """Test the graph traversal function."""
    print("Testing graph traversal...")
    
    workflow = create_test_workflow_with_disconnected_loras()
    connected_nodes = traverse_workflow_graph(workflow)
    
    print(f"Connected nodes found: {sorted(connected_nodes)}")
    
    # Expected connected nodes: 9, 28, 12, 41, 4, 6, 7, 30
    expected_connected = {"9", "28", "12", "41", "4", "6", "7", "30"}
    
    # Nodes that should NOT be connected: 99, 100, 101
    expected_disconnected = {"99", "100", "101"}
    
    # Verify all expected connected nodes are found
    missing_connected = expected_connected - connected_nodes
    if missing_connected:
        print(f"❌ ERROR: Missing expected connected nodes: {missing_connected}")
        return False
    
    # Verify disconnected nodes are not included
    incorrectly_connected = expected_disconnected & connected_nodes
    if incorrectly_connected:
        print(f"❌ ERROR: Incorrectly included disconnected nodes: {incorrectly_connected}")
        return False
    
    print("✅ Graph traversal test passed!")
    return True


def test_connected_lora_extraction():
    """Test that only connected LoRA nodes are extracted."""
    print("\nTesting connected LoRA extraction...")
    
    workflow = create_test_workflow_with_disconnected_loras()
    connected_nodes = traverse_workflow_graph(workflow)
    loras = extract_connected_lora_nodes(workflow, connected_nodes)
    
    print(f"Extracted LoRAs: {loras}")
    
    # Expected LoRAs (only from connected node 41)
    expected_loras = ["connected_lora.safetensors:1.0", "another_connected.safetensors:0.8"]
    
    # LoRAs that should NOT be extracted (from disconnected nodes 99, 101)
    forbidden_loras = ["disconnected_lora.safetensors:1.2", "unused_lora.safetensors:0.5", "orphaned_lora.safetensors:0.9"]
    
    # Check that all expected LoRAs are present
    for expected_lora in expected_loras:
        if expected_lora not in loras:
            print(f"❌ ERROR: Missing expected LoRA: {expected_lora}")
            return False
    
    # Check that no forbidden LoRAs are present
    for forbidden_lora in forbidden_loras:
        if forbidden_lora in loras:
            print(f"❌ ERROR: Incorrectly extracted disconnected LoRA: {forbidden_lora}")
            return False
    
    print("✅ Connected LoRA extraction test passed!")
    return True


def test_full_workflow_extraction():
    """Test the complete workflow parameter extraction."""
    print("\nTesting full workflow parameter extraction...")
    
    workflow = create_test_workflow_with_disconnected_loras()
    workflow_json = json.dumps(workflow)
    
    params = extract_comfyui_workflow_params(workflow_json)
    
    print("Extracted parameters:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # Verify expected parameters
    expected_checks = [
        ('cfg', 7.5),
        ('steps', 30),
        ('width', 1024),
        ('height', 1024),
        ('model', 'test_model.safetensors'),
        ('sampler_name', 'euler_ancestral')
    ]
    
    for param_name, expected_value in expected_checks:
        if params.get(param_name) != expected_value:
            print(f"❌ ERROR: Parameter {param_name} = {params.get(param_name)}, expected {expected_value}")
            return False
    
    # Verify LoRAs
    expected_loras = ["connected_lora.safetensors:1.0", "another_connected.safetensors:0.8"]
    actual_loras = params.get('loras', [])
    
    if set(actual_loras) != set(expected_loras):
        print(f"❌ ERROR: LoRAs = {actual_loras}, expected {expected_loras}")
        return False
    
    print("✅ Full workflow extraction test passed!")
    return True


def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Graph Traversal-Based LoRA Extraction")
    print("=" * 60)
    
    tests = [
        test_graph_traversal,
        test_connected_lora_extraction,
        test_full_workflow_extraction
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ Test {test_func.__name__} failed!")
        except Exception as e:
            print(f"❌ Test {test_func.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Graph traversal implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

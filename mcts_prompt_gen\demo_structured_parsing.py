#!/usr/bin/env python3
"""
Demo script for structured prompt parsing on real dataset.

This script demonstrates the advanced structured prompt parsing capabilities
on real enhanced prompts from the dataset, showing tag extraction, weight
parsing, and technical parameter handling.
"""

import os
import sys
import dill
import json
from pathlib import Path
from collections import Counter

# Add src directory to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

from prompt_parser import PromptParser, parse_enhanced_prompt


def demo_structured_parsing_on_real_data(pkl_file: str = "promptlabels.pkl", num_samples: int = 10):
    """
    Demonstrate structured parsing on real enhanced prompts from the dataset.
    
    Args:
        pkl_file: Path to the enhanced promptlabels.pkl file
        num_samples: Number of samples to demonstrate
    """
    print("Structured Prompt Parsing Demo on Real Data")
    print("=" * 60)
    
    if not os.path.exists(pkl_file):
        print(f"❌ Dataset file not found: {pkl_file}")
        print("Please run the enhanced data cleaning script first:")
        print("  python run_data_cleaning.py --run")
        return False
    
    # Load dataset
    with open(pkl_file, 'rb') as f:
        data = dill.load(f)
    
    print(f"Loaded dataset with {len(data)} entries")
    
    # Filter for enhanced prompts (those with ComfyUI parameters)
    enhanced_prompts = []
    for filename, prompt, quality in data:
        if '--cfg' in prompt or '--steps' in prompt or '--lora' in prompt:
            enhanced_prompts.append((filename, prompt, quality))
    
    print(f"Found {len(enhanced_prompts)} enhanced prompts with ComfyUI parameters")
    
    if len(enhanced_prompts) == 0:
        print("No enhanced prompts found. Using regular prompts for demo...")
        enhanced_prompts = data[:num_samples]
    
    # Take samples for demo
    demo_samples = enhanced_prompts[:num_samples]
    
    print(f"\nDemonstrating structured parsing on {len(demo_samples)} samples...\n")
    
    parser = PromptParser()
    structured_results = []
    
    for i, (filename, prompt, quality) in enumerate(demo_samples):
        print(f"--- Sample {i+1} ---")
        print(f"File: {Path(filename).name}")
        print(f"Quality: {quality}")
        print(f"Original prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
        
        # Parse into structured format
        structured = parser.parse_enhanced_prompt(prompt)
        structured_results.append(structured)
        
        print(f"\nStructured Analysis:")
        print(f"  Total tags: {structured['tag_count']}")
        print(f"  Average weight: {structured['avg_weight']:.2f}")
        print(f"  Weight variance: {structured['weight_variance']:.3f}")
        print(f"  Technical parameters: {len(structured['technical_params'])}")
        
        # Show text tags (non-technical)
        text_tags = [(tag, weight) for tag, weight in structured['tags'] 
                    if not tag.startswith(('cfg_', 'sampling_', 'width', 'height', 'lora_', 'sampler', 'model'))]
        
        if text_tags:
            print(f"  Text tags ({len(text_tags)}):")
            for tag, weight in text_tags[:5]:  # Show first 5
                weight_desc = ""
                if weight > 1.0:
                    weight_desc = f" (emphasized {weight:.1f}x)"
                elif weight < 1.0:
                    weight_desc = f" (reduced {weight:.1f}x)"
                print(f"    {tag}{weight_desc}")
            
            if len(text_tags) > 5:
                print(f"    ... and {len(text_tags) - 5} more")
        
        # Show technical parameters
        if structured['technical_params']:
            print(f"  Technical parameters:")
            for param, value in structured['technical_params'].items():
                if param == 'loras':
                    print(f"    {param}: {len(value)} LoRA(s)")
                    for lora_name, lora_weight in value:
                        print(f"      - {lora_name}: {lora_weight}")
                else:
                    print(f"    {param}: {value}")
        
        # Test reconstruction
        reconstructed = parser.reconstruct_prompt(structured)
        print(f"\nReconstructed: {reconstructed[:100]}{'...' if len(reconstructed) > 100 else ''}")
        
        print()
    
    return True


def analyze_parsing_statistics(pkl_file: str = "promptlabels.pkl", sample_size: int = 1000):
    """
    Analyze parsing statistics across the dataset.
    
    Args:
        pkl_file: Path to the dataset file
        sample_size: Number of samples to analyze
    """
    print("Dataset Parsing Statistics Analysis")
    print("=" * 60)
    
    if not os.path.exists(pkl_file):
        print(f"❌ Dataset file not found: {pkl_file}")
        return False
    
    # Load dataset
    with open(pkl_file, 'rb') as f:
        data = dill.load(f)
    
    print(f"Analyzing parsing statistics on {min(sample_size, len(data))} samples...")
    
    # Take a sample for analysis
    import random
    random.seed(42)
    sample_data = random.sample(data, min(sample_size, len(data)))
    
    parser = PromptParser()
    
    # Parse all samples
    prompts = [prompt for _, prompt, _ in sample_data]
    structured_results = parser.iterative_cleaning_process(prompts, max_iterations=2)
    
    # Analyze results
    stats = parser.get_parsing_statistics()
    
    print(f"\n--- Parsing Statistics ---")
    print(f"Total prompts processed: {len(prompts)}")
    print(f"Total tags parsed: {stats['total_tags_parsed']}")
    print(f"Unique tags: {stats['unique_tags']}")
    print(f"Parsing errors: {stats['parsing_errors']}")
    print(f"Error rate: {stats['error_rate']:.3f}")
    print(f"Cleaning iterations: {stats['cleaning_iterations']}")
    
    # Tag frequency analysis
    print(f"\n--- Most Common Tags (Top 20) ---")
    for i, (tag, count) in enumerate(stats['most_common_tags'][:20], 1):
        percentage = count / stats['total_tags_parsed'] * 100
        print(f"{i:2d}. {tag}: {count} ({percentage:.1f}%)")
    
    # Weight analysis
    tag_weights = Counter()
    weight_distributions = {}
    
    for structured in structured_results:
        for tag, weight in structured['tags']:
            tag_weights[tag] += 1
            if tag not in weight_distributions:
                weight_distributions[tag] = []
            weight_distributions[tag].append(weight)
    
    # Find tags with interesting weight patterns
    print(f"\n--- Tags with Weight Variations ---")
    varied_tags = []
    for tag, weights in weight_distributions.items():
        if len(weights) > 5:  # Only consider tags that appear multiple times
            unique_weights = set(weights)
            if len(unique_weights) > 1:  # Has weight variations
                avg_weight = sum(weights) / len(weights)
                varied_tags.append((tag, len(weights), len(unique_weights), avg_weight))
    
    # Sort by frequency and show top 10
    varied_tags.sort(key=lambda x: x[1], reverse=True)
    for tag, count, unique_weights, avg_weight in varied_tags[:10]:
        print(f"  {tag}: {count} occurrences, {unique_weights} different weights, avg {avg_weight:.2f}")
    
    # Technical parameter analysis
    tech_param_counts = Counter()
    for structured in structured_results:
        for param in structured['technical_params'].keys():
            tech_param_counts[param] += 1
    
    if tech_param_counts:
        print(f"\n--- Technical Parameter Frequency ---")
        for param, count in tech_param_counts.most_common():
            percentage = count / len(structured_results) * 100
            print(f"  {param}: {count} ({percentage:.1f}%)")
    
    # Quality distribution analysis
    quality_tag_stats = {'good': Counter(), 'normal': Counter()}
    
    for i, (_, _, quality) in enumerate(sample_data):
        if i < len(structured_results):
            structured = structured_results[i]
            for tag, weight in structured['tags']:
                if not tag.startswith(('cfg_', 'sampling_', 'width', 'height')):  # Text tags only
                    quality_tag_stats[quality][tag] += 1
    
    print(f"\n--- Tag Distribution by Quality ---")
    good_tags = set(quality_tag_stats['good'].keys())
    normal_tags = set(quality_tag_stats['normal'].keys())
    
    # Tags more common in good quality prompts
    good_specific = []
    for tag in good_tags:
        good_count = quality_tag_stats['good'][tag]
        normal_count = quality_tag_stats['normal'].get(tag, 0)
        if good_count > normal_count and good_count > 5:
            ratio = good_count / max(normal_count, 1)
            good_specific.append((tag, good_count, normal_count, ratio))
    
    good_specific.sort(key=lambda x: x[3], reverse=True)
    print(f"Tags more common in GOOD quality prompts:")
    for tag, good_count, normal_count, ratio in good_specific[:10]:
        print(f"  {tag}: {good_count} good vs {normal_count} normal (ratio: {ratio:.1f})")
    
    return True


def export_structured_dataset(pkl_file: str = "promptlabels.pkl", 
                             output_file: str = "structured_promptlabels.pkl",
                             sample_size: int = None):
    """
    Export the dataset in structured format.
    
    Args:
        pkl_file: Input dataset file
        output_file: Output structured dataset file
        sample_size: Number of samples to process (None for all)
    """
    print("Exporting Structured Dataset")
    print("=" * 60)
    
    if not os.path.exists(pkl_file):
        print(f"❌ Dataset file not found: {pkl_file}")
        return False
    
    # Load dataset
    with open(pkl_file, 'rb') as f:
        data = dill.load(f)
    
    if sample_size:
        import random
        random.seed(42)
        data = random.sample(data, min(sample_size, len(data)))
    
    print(f"Converting {len(data)} prompts to structured format...")
    
    parser = PromptParser()
    structured_data = []
    
    for i, (filename, prompt, quality) in enumerate(data):
        if i % 1000 == 0:
            print(f"  Progress: {i}/{len(data)} ({i/len(data)*100:.1f}%)")
        
        structured_prompt = parser.parse_enhanced_prompt(prompt)
        structured_data.append((filename, structured_prompt, quality))
    
    # Save structured dataset
    with open(output_file, 'wb') as f:
        dill.dump(structured_data, f)
    
    print(f"✓ Structured dataset saved to: {output_file}")
    
    # Get final statistics
    stats = parser.get_parsing_statistics()
    print(f"✓ Parsing statistics:")
    print(f"  Total tags: {stats['total_tags_parsed']}")
    print(f"  Unique tags: {stats['unique_tags']}")
    print(f"  Error rate: {stats['error_rate']:.3f}")
    
    return True


def main():
    """Main demo function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Demo structured prompt parsing")
    parser.add_argument('--data', default='promptlabels.pkl', help='Dataset file')
    parser.add_argument('--samples', type=int, default=10, help='Number of samples for demo')
    parser.add_argument('--analyze', action='store_true', help='Run statistical analysis')
    parser.add_argument('--export', action='store_true', help='Export structured dataset')
    parser.add_argument('--export-size', type=int, help='Sample size for export')
    parser.add_argument('--output', default='structured_promptlabels.pkl', help='Output file for export')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.data):
        print(f"❌ Dataset file not found: {args.data}")
        print("Please run the enhanced data cleaning script first:")
        print("  python run_data_cleaning.py --run")
        return False
    
    # Run demo
    success = demo_structured_parsing_on_real_data(args.data, args.samples)
    
    if args.analyze and success:
        analyze_parsing_statistics(args.data, sample_size=2000)
    
    if args.export and success:
        export_structured_dataset(args.data, args.output, args.export_size)
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

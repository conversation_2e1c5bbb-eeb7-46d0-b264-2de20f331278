#!/usr/bin/env python3
"""
LightGBM Training Pipeline for Prompt Quality Prediction

This script implements a comprehensive LightGBM training pipeline with feature extraction,
hyperparameter tuning, and model evaluation for ranking prompt quality.

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional
import dill
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ML imports
import lightgbm as lgb
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import roc_auc_score, precision_recall_curve, roc_curve
from sklearn.preprocessing import LabelEncoder
import optuna
from scipy.sparse import hstack, csr_matrix


class PromptFeatureExtractor:
    """Enhanced feature extraction pipeline for prompt text and structured prompts."""

    def __init__(self, max_features: int = 10000, ngram_range: Tuple[int, int] = (1, 3),
                 structured_mode: bool = False):
        self.max_features = max_features
        self.ngram_range = ngram_range
        self.structured_mode = structured_mode
        self.tfidf_vectorizer = None
        self.label_encoder = None
        self.fitted = False
    
    def fit(self, prompts, labels: List[str]):
        """Fit the feature extractors on training data."""
        print(f"Fitting feature extractors on {len(prompts)} prompts...")

        # Handle both string and structured prompts
        if self.structured_mode and prompts and isinstance(prompts[0], dict):
            # Extract text for TF-IDF from structured prompts
            text_prompts = []
            for prompt_dict in prompts:
                if isinstance(prompt_dict, dict):
                    # Combine text tags for TF-IDF
                    text_tags = [tag for tag, weight in prompt_dict.get('tags', [])
                               if not tag.startswith(('cfg_', 'sampling_', 'width', 'height', 'lora_', 'sampler'))]
                    text_prompts.append(' '.join(text_tags))
                else:
                    text_prompts.append(str(prompt_dict))
        else:
            # Regular string prompts
            text_prompts = [str(p) for p in prompts]

        # TF-IDF for text features
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=self.max_features,
            ngram_range=self.ngram_range,
            stop_words='english',
            lowercase=True,
            strip_accents='unicode',
            token_pattern=r'\b\w+\b'
        )

        # Fit on non-empty prompts only
        valid_prompts = [p for p in text_prompts if p.strip()]
        if not valid_prompts:
            raise ValueError("No valid prompts found for feature extraction!")

        self.tfidf_vectorizer.fit(valid_prompts)

        # Label encoder for target
        self.label_encoder = LabelEncoder()
        self.label_encoder.fit(labels)

        self.fitted = True
        print(f"✓ Feature extractors fitted. TF-IDF vocabulary size: {len(self.tfidf_vectorizer.vocabulary_)}")
        print(f"✓ Structured mode: {self.structured_mode}")
    
    def transform(self, prompts, labels: Optional[List[str]] = None):
        """Transform prompts into features."""
        if not self.fitted:
            raise ValueError("Feature extractors must be fitted first!")

        # Handle both string and structured prompts
        if self.structured_mode and prompts and isinstance(prompts[0], dict):
            text_prompts, structured_features = self._extract_structured_features(prompts)
        else:
            text_prompts = [str(p) for p in prompts]
            structured_features = None

        # Handle empty prompts
        processed_prompts = [p if p.strip() else "empty_prompt" for p in text_prompts]

        # TF-IDF features
        tfidf_features = self.tfidf_vectorizer.transform(processed_prompts)
        
        # Enhanced text-based features including ComfyUI parameters
        text_features = []

        if self.structured_mode and structured_features is not None:
            # Use structured prompt features
            for i, prompt in enumerate(processed_prompts):
                # Basic text features from the text representation
                basic_features = [
                    len(prompt),  # Character length
                    len(prompt.split()),  # Word count
                    prompt.count(','),  # Comma count (common in prompts)
                    prompt.count('('),  # Parentheses count
                    prompt.count('['),  # Bracket count
                    len([w for w in prompt.split() if w.isupper()]),  # Uppercase words
                    1 if prompt.strip() else 0,  # Has content flag
                ]

                # Enhanced features (set to 0 for structured mode as they're handled separately)
                enhanced_features = [0] * 7  # Placeholder for compatibility

                # Parameter value features (set to 0 for structured mode)
                param_features = [0.0] * 6  # Placeholder for compatibility

                # Combine features
                features = basic_features + enhanced_features + param_features

                # Add structured features
                if i < len(structured_features):
                    features.extend(structured_features[i])
                else:
                    features.extend([0, 1.0, 0.0, 0])  # Default structured features

                text_features.append(features)
        else:
            # Original enhanced feature extraction for string prompts
            for prompt in processed_prompts:
                # Basic text features
                basic_features = [
                    len(prompt),  # Character length
                    len(prompt.split()),  # Word count
                    prompt.count(','),  # Comma count (common in prompts)
                    prompt.count('('),  # Parentheses count
                    prompt.count('['),  # Bracket count
                    len([w for w in prompt.split() if w.isupper()]),  # Uppercase words
                    1 if prompt.strip() else 0,  # Has content flag
                ]

                # Enhanced features for ComfyUI parameters
                enhanced_features = [
                    1 if '--cfg' in prompt else 0,  # Has CFG parameter
                    1 if '--steps' in prompt else 0,  # Has steps parameter
                    1 if '--size' in prompt else 0,  # Has size parameter
                    1 if '--lora' in prompt else 0,  # Has LoRA parameter
                    1 if '--sampler' in prompt else 0,  # Has sampler parameter
                    1 if '--model' in prompt else 0,  # Has model parameter
                    prompt.count('--lora'),  # Number of LoRAs
                ]

                # Extract numeric parameter values if present
                import re

                # Extract CFG value
                cfg_match = re.search(r'--cfg\s+([0-9.]+)', prompt)
                cfg_value = float(cfg_match.group(1)) if cfg_match else 0.0

                # Extract steps value
                steps_match = re.search(r'--steps\s+(\d+)', prompt)
                steps_value = float(steps_match.group(1)) if steps_match else 0.0

                # Extract image dimensions
                size_match = re.search(r'--size\s+(\d+)x(\d+)', prompt)
                if size_match:
                    width = float(size_match.group(1))
                    height = float(size_match.group(2))
                    aspect_ratio = width / height if height > 0 else 1.0
                    total_pixels = width * height
                else:
                    width = height = aspect_ratio = total_pixels = 0.0

                # Parameter value features
                param_features = [
                    cfg_value,
                    steps_value,
                    width,
                    height,
                    aspect_ratio,
                    total_pixels / 1000000,  # Megapixels
                ]

                # Combine all features
                features = basic_features + enhanced_features + param_features
                text_features.append(features)
        
        text_features = np.array(text_features, dtype=np.float32)
        
        # Combine features
        if text_features.shape[0] > 0:
            combined_features = hstack([tfidf_features, csr_matrix(text_features)])
        else:
            combined_features = tfidf_features
        
        if labels is not None:
            encoded_labels = self.label_encoder.transform(labels)
            return combined_features, encoded_labels
        
        return combined_features
    
    def get_feature_names(self):
        """Get feature names for interpretability."""
        if not self.fitted:
            return []

        tfidf_names = self.tfidf_vectorizer.get_feature_names_out().tolist()

        # Basic text features
        basic_text_names = ['char_length', 'word_count', 'comma_count', 'paren_count',
                           'bracket_count', 'uppercase_words', 'has_content']

        # Enhanced parameter features
        enhanced_names = ['has_cfg', 'has_steps', 'has_size', 'has_lora',
                         'has_sampler', 'has_model', 'lora_count']

        # Parameter value features
        param_value_names = ['cfg_value', 'steps_value', 'width', 'height',
                            'aspect_ratio', 'megapixels']

        all_names = tfidf_names + basic_text_names + enhanced_names + param_value_names

        # Add structured prompt features if in structured mode
        if self.structured_mode:
            structured_names = ['tag_count', 'avg_weight', 'weight_variance', 'technical_param_count']
            all_names.extend(structured_names)

        return all_names

    def _extract_structured_features(self, structured_prompts):
        """Extract features from structured prompts."""
        text_prompts = []
        structured_features = []

        for prompt_dict in structured_prompts:
            if isinstance(prompt_dict, dict):
                # Extract text tags for TF-IDF
                text_tags = [tag for tag, _ in prompt_dict.get('tags', [])
                           if not tag.startswith(('cfg_', 'sampling_', 'width', 'height', 'lora_', 'sampler'))]
                text_prompts.append(' '.join(text_tags))

                # Extract structured features
                tag_count = prompt_dict.get('tag_count', 0)
                avg_weight = prompt_dict.get('avg_weight', 1.0)
                weight_variance = prompt_dict.get('weight_variance', 0.0)
                technical_param_count = len(prompt_dict.get('technical_params', {}))

                structured_features.append([tag_count, avg_weight, weight_variance, technical_param_count])
            else:
                # Fallback for non-dict prompts
                text_prompts.append(str(prompt_dict))
                structured_features.append([0, 1.0, 0.0, 0])

        return text_prompts, structured_features


class LightGBMTrainer:
    """LightGBM training pipeline with hyperparameter optimization."""
    
    def __init__(self, results_dir: str = "results"):
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(exist_ok=True)
        
        # Create timestamped subdirectory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = self.results_dir / f"lightgbm_training_{timestamp}"
        self.run_dir.mkdir(exist_ok=True)
        
        self.feature_extractor = None
        self.model = None
        self.best_params = None
        self.training_history = {}
        
        print(f"Training results will be saved to: {self.run_dir}")
    
    def load_data(self, train_file: str, test_file: str):
        """Load training and test data."""
        print("="*60)
        print("LOADING TRAINING DATA")
        print("="*60)
        
        with open(train_file, 'rb') as f:
            train_data = dill.load(f)
        
        with open(test_file, 'rb') as f:
            test_data = dill.load(f)
        
        # Extract components
        self.train_filenames = [item[0] for item in train_data]
        self.train_prompts = [item[1] for item in train_data]
        self.train_labels = [item[2] for item in train_data]
        
        self.test_filenames = [item[0] for item in test_data]
        self.test_prompts = [item[1] for item in test_data]
        self.test_labels = [item[2] for item in test_data]
        
        print(f"✓ Loaded {len(train_data)} training samples")
        print(f"✓ Loaded {len(test_data)} test samples")
        
        # Class distribution
        train_good = sum(1 for label in self.train_labels if label == 'good')
        test_good = sum(1 for label in self.test_labels if label == 'good')
        
        print(f"Training set - Good: {train_good}, Normal: {len(self.train_labels) - train_good}")
        print(f"Test set - Good: {test_good}, Normal: {len(self.test_labels) - test_good}")
    
    def prepare_features(self):
        """Extract and prepare features from text data."""
        print("\n" + "="*60)
        print("FEATURE EXTRACTION")
        print("="*60)
        
        # Initialize and fit feature extractor
        self.feature_extractor = PromptFeatureExtractor(max_features=5000, ngram_range=(1, 2))
        self.feature_extractor.fit(self.train_prompts, self.train_labels)
        
        # Transform training data
        self.X_train, self.y_train = self.feature_extractor.transform(
            self.train_prompts, self.train_labels
        )
        
        # Transform test data
        self.X_test, self.y_test = self.feature_extractor.transform(
            self.test_prompts, self.test_labels
        )
        
        print(f"✓ Training features shape: {self.X_train.shape}")
        print(f"✓ Test features shape: {self.X_test.shape}")
        
        # Save feature extractor
        feature_extractor_file = self.run_dir / "feature_extractor.pkl"
        with open(feature_extractor_file, 'wb') as f:
            dill.dump(self.feature_extractor, f)
        
        print(f"✓ Feature extractor saved to: {feature_extractor_file}")
    
    def optimize_hyperparameters(self, n_trials: int = 50, cv_folds: int = 5):
        """Optimize hyperparameters using Optuna."""
        print("\n" + "="*60)
        print("HYPERPARAMETER OPTIMIZATION")
        print("="*60)
        
        def objective(trial):
            # Define hyperparameter search space
            params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
                'verbosity': -1,
                'random_state': 42
            }
            
            # Cross-validation
            cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
            
            scores = []
            for train_idx, val_idx in cv.split(self.X_train, self.y_train):
                X_fold_train = self.X_train[train_idx]
                y_fold_train = self.y_train[train_idx]
                X_fold_val = self.X_train[val_idx]
                y_fold_val = self.y_train[val_idx]
                
                # Create datasets
                train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
                val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
                
                # Train model
                model = lgb.train(
                    params,
                    train_data,
                    valid_sets=[val_data],
                    num_boost_round=1000,
                    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
                )
                
                # Predict and score
                y_pred = model.predict(X_fold_val, num_iteration=model.best_iteration)
                score = roc_auc_score(y_fold_val, y_pred)
                scores.append(score)
            
            return np.mean(scores)
        
        # Run optimization
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=42))
        study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
        
        self.best_params = study.best_params
        self.best_params.update({
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'verbosity': -1,
            'random_state': 42
        })
        
        print(f"✓ Best CV score: {study.best_value:.4f}")
        print(f"✓ Best parameters: {self.best_params}")
        
        # Save optimization results
        optuna_file = self.run_dir / "hyperparameter_optimization.pkl"
        with open(optuna_file, 'wb') as f:
            dill.dump({
                'study': study,
                'best_params': self.best_params,
                'best_score': study.best_value
            }, f)
        
        print(f"✓ Optimization results saved to: {optuna_file}")
    
    def train_final_model(self):
        """Train the final model with optimized hyperparameters."""
        print("\n" + "="*60)
        print("TRAINING FINAL MODEL")
        print("="*60)
        
        if self.best_params is None:
            print("Using default parameters (no optimization performed)")
            self.best_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbosity': -1,
                'random_state': 42
            }
        
        # Create datasets
        train_data = lgb.Dataset(self.X_train, label=self.y_train)
        val_data = lgb.Dataset(self.X_test, label=self.y_test, reference=train_data)
        
        # Train with validation
        callbacks = [
            lgb.early_stopping(100),
            lgb.log_evaluation(100),
            lgb.record_evaluation(self.training_history)
        ]
        
        self.model = lgb.train(
            self.best_params,
            train_data,
            valid_sets=[train_data, val_data],
            valid_names=['train', 'test'],
            num_boost_round=2000,
            callbacks=callbacks
        )
        
        print(f"✓ Model trained with {self.model.best_iteration} iterations")
        
        # Save model
        model_file = self.run_dir / "lightgbm_model.pkl"
        with open(model_file, 'wb') as f:
            dill.dump(self.model, f)

        # Also save in LightGBM native format
        native_model_file = self.run_dir / "lightgbm_model.txt"
        self.model.save_model(str(native_model_file))

        # Save parameters
        params_file = self.run_dir / "model_parameters.json"
        with open(params_file, 'w') as f:
            json.dump(self.best_params, f, indent=2)

        print(f"✓ Model saved to: {model_file}")
        print(f"✓ Native model saved to: {native_model_file}")
        print(f"✓ Parameters saved to: {params_file}")
    
    def create_training_plots(self):
        """Create training visualization plots."""
        print("\n" + "="*60)
        print("CREATING TRAINING PLOTS")
        print("="*60)
        
        if not self.training_history:
            print("No training history available for plotting")
            return
        
        # Training curves
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        # Loss curves
        ax1 = axes[0]
        train_loss = self.training_history['train']['binary_logloss']
        test_loss = self.training_history['test']['binary_logloss']
        
        ax1.plot(train_loss, label='Training Loss', color='blue')
        ax1.plot(test_loss, label='Validation Loss', color='red')
        ax1.set_xlabel('Iteration')
        ax1.set_ylabel('Binary Log Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Feature importance
        ax2 = axes[1]
        if self.model:
            importance = self.model.feature_importance(importance_type='gain')
            feature_names = self.feature_extractor.get_feature_names()
            
            # Get top 20 features
            top_indices = np.argsort(importance)[-20:]
            top_importance = importance[top_indices]
            top_names = [feature_names[i] if i < len(feature_names) else f'feature_{i}' 
                        for i in top_indices]
            
            ax2.barh(range(len(top_importance)), top_importance)
            ax2.set_yticks(range(len(top_importance)))
            ax2.set_yticklabels(top_names, fontsize=8)
            ax2.set_xlabel('Feature Importance (Gain)')
            ax2.set_title('Top 20 Feature Importance')
        
        plt.tight_layout()
        
        # Save plot
        training_plot_file = self.run_dir / "training_curves.png"
        plt.savefig(training_plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Training plots saved to: {training_plot_file}")
    
    def run_training_pipeline(self, train_file: str, test_file: str, 
                            optimize_hyperparams: bool = True, n_trials: int = 50):
        """Run the complete training pipeline."""
        print("Starting LightGBM training pipeline...")
        
        self.load_data(train_file, test_file)
        self.prepare_features()
        
        if optimize_hyperparams:
            self.optimize_hyperparameters(n_trials=n_trials)
        
        self.train_final_model()
        self.create_training_plots()
        
        print("\n" + "="*60)
        print("TRAINING PIPELINE COMPLETED")
        print("="*60)
        print(f"All results saved to: {self.run_dir}")
        
        return self.model, self.feature_extractor


def main():
    """Main function with command-line interface."""
    import argparse
    
    parser = argparse.ArgumentParser(description="LightGBM training pipeline")
    parser.add_argument('--train-data', required=True, help='Path to training data pickle file')
    parser.add_argument('--test-data', required=True, help='Path to test data pickle file')
    parser.add_argument('--results-dir', default='results', help='Results directory')
    parser.add_argument('--no-optimization', action='store_true', 
                       help='Skip hyperparameter optimization')
    parser.add_argument('--n-trials', type=int, default=50, 
                       help='Number of optimization trials')
    
    args = parser.parse_args()
    
    # Create trainer and run pipeline
    trainer = LightGBMTrainer(args.results_dir)
    
    try:
        model, feature_extractor = trainer.run_training_pipeline(
            args.train_data, 
            args.test_data,
            optimize_hyperparams=not args.no_optimization,
            n_trials=args.n_trials
        )
        
        print("\n🎉 Training completed successfully!")
        print("Ready to proceed with model evaluation.")
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
